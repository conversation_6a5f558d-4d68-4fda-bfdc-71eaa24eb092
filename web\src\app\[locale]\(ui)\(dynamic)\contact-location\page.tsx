import { ContactLocationView } from "#/src/modules/contact-location";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Contact Location - Kafi",
  description: "Contact Location - Kafi",
};

export default async function AboutUsPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <ContactLocationView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

/**
 * Partner Calculator Service
 *
 * This service is used to calculate the partner of a user.
 *
 * Params:
 * - targetAmount: number
 * - annualInterestRate: number
 * - durationInMonths: number
 * - initialInvestment?: number
 * - monthlyDeposit?: number
 *
 * Returns:
 * - partner: number
 * - partnerPercentage: number
 * - partnerSeries: { x: number; y: number }[]
 *
 * Formulas:
 * - partner = targetAmount * 0.1
 * - partnerPercentage = partner / targetAmount
 * - partnerSeries = [
 *   { x: 0, y: partner },
 *   { x: 1, y: partner },
 *   { x: 2, y: partner },
 * **/

interface PartnerParams {
  targetAmount: number;
  annualInterestRate: number;
  durationInMonths: number;
  initialInvestment?: number;
  monthlyDeposit?: number;
}

interface PartnerResult {
  partner: number;
  partnerPercentage: number;
  partnerSeries: { x: number; y: number }[];
}

export const calculatePartner = ({
  targetAmount,
  annualInterestRate,
  durationInMonths,
  initialInvestment,
  monthlyDeposit,
}: PartnerParams): PartnerResult => {
  console.log(
    targetAmount,
    annualInterestRate,
    durationInMonths,
    initialInvestment,
    monthlyDeposit,
  );

  return {
    partner: 0,
    partnerPercentage: 0,
    partnerSeries: [],
  };
};

/**
 * Example:
 *
 * **/

"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useRouter } from "next/navigation";

// Define the structure of a job opening
interface JobListing {
  id: string | number;
  position: string;
  location: string;
  quantity: number;
  deadline: string;
}

// Define the structure of a table column
interface ColumnConfig {
  key: keyof JobListing;
  label: string;
  align: "left" | "right";
}

// Job openings data
const JOB_LISTING: JobListing[] = [
  {
    id: 1,
    position: "Data Engineer",
    location: "<PERSON><PERSON><PERSON> Minh",
    quantity: 1,
    deadline: "30/06/2025",
  },
  {
    id: 2,
    position: "Quantitative Researcher",
    location: "<PERSON><PERSON> Minh",
    quantity: 1,
    deadline: "30/06/2025",
  },
  {
    id: 3,
    position: "Senior Frontend Engineer",
    location: "<PERSON><PERSON> Chí Minh",
    quantity: 1,
    deadline: "30/06/2025",
  },
  {
    id: 4,
    position: "Treasury Relationship Manager",
    location: "<PERSON><PERSON><PERSON>",
    quantity: 1,
    deadline: "30/06/2025",
  },
  {
    id: 5,
    position: "<PERSON><PERSON><PERSON><PERSON>",
    location: "<PERSON><PERSON>",
    quantity: 1,
    deadline: "30/06/2025",
  },
  {
    id: 6,
    position: "Chuyên Viên Môi Giới Chứng <PERSON>hoán",
    location: "Hồ Chí Minh, Hà Nội",
    quantity: 1,
    deadline: "30/06/2025",
  },
];

// Column configuration for dynamic rendering
const columns: ColumnConfig[] = [
  { key: "position", label: "Vị trí công việc", align: "left" },
  { key: "location", label: "Địa điểm", align: "left" },
  { key: "quantity", label: "Số lượng", align: "right" },
  { key: "deadline", label: "Thời hạn", align: "right" },
];

export default function JobListing() {
  const router = useRouter();

  return (
    <Table className="w-full text-sm text-gray-700">
      <TableHeader>
        <TableRow className="border-none pointer-events-none h-14">
          {columns.map((column) => (
            <TableHead
              key={column.key}
              className={`font-semibold text-base ${
                column.align === "right" ? "text-right" : "text-left"
              }`}
            >
              {column.label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {JOB_LISTING.map((job) => (
          <TableRow
            key={job.id}
            className="h-14 border-b border-gray-200 hover:bg-transparent hover:text-brand cursor-pointer"
            onClick={() => router.push(`/recruitment/${job.id}`)}
          >
            {columns.map((column) => (
              <TableCell
                key={`${job.id}-${column.key}`}
                className={`py-4 text-base ${
                  column.align === "right" ? "text-right" : "text-left"
                }`}
              >
                {job[column.key]}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}

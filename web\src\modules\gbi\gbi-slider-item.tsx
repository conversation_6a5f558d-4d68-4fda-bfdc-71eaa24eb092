"use client";

import { Slider, Typography } from "#/src/components/ui";
import { useEffect, useState } from "react";
import { cn } from "#/src/lib/utils";
import { fNumber } from "#/src/utils/number-format";

type TProps = {
  label?: string;
  defaultValue: number;
  value?: number;
  unit?: string;
  min?: number;
  max?: number;
  step?: number;
  onChange?: (value: number[]) => void;
  color?: "gradient" | "heatmap";
  hideLabel?: boolean;
  disabled?: boolean;
};
const SliderItem = ({
  label,
  defaultValue,
  value,
  unit,
  min,
  max,
  step = 1,
  onChange,
  color = "gradient",
  hideLabel = false,
  disabled = false,
}: TProps) => {
  const [newValue, setNewValue] = useState(value ?? defaultValue);

  const handleChange = (val: number[]) => {
    setNewValue(val[0] ?? defaultValue);
    onChange?.(val);
  };

  useEffect(() => {
    setNewValue(value ?? defaultValue);
  }, [value, defaultValue]);

  return (
    <div className="flex flex-col gap-4">
      {!hideLabel && (
        <div className="flex justify-between">
          <Typography>{label}</Typography>
          <Typography variant="headline">
            {fNumber(newValue, 0)} {unit}
          </Typography>
        </div>
      )}
      <Slider
        defaultValue={[defaultValue]}
        value={[newValue]}
        min={min}
        max={max}
        step={step}
        onValueChange={handleChange}
        disabled={disabled}
        rangeClassName={cn(
          "bg-gradient-left",
          color === "heatmap" && "bg-transparent",
        )}
        thumbClassName={cn(
          "bg-primary-500 border-primary-500 hover:ring-primary-500/10 cursor-pointer focus:ring-primary-500/10 w-6 h-6",
        )}
        trackClassName={cn(
          "bg-[#E0E2E3]",
          color === "heatmap" && "bg-transparent",
        )}
        className={cn(
          color === "heatmap" &&
            "bg-gradient-to-r from-[#12B88E] via-[#FBC62F] to-[#FF0000] rounded-full",
        )}
      />
    </div>
  );
};

export default SliderItem;

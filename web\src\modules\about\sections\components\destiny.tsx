import {
  Carousel,
  CarouselContent,
  CarouselItem,
  Typography,
} from "#/src/components/ui";
import { useCarousel } from "#/src/hooks";
import IconBoxCardCustom from "./icon-box-card-custom";

export default function Destiny({ items }: { items: any }) {
  const { setApi } = useCarousel();
  return (
    <div className="flex flex-col gap-10 container !py-0 mx-auto">
      <div className="flex flex-col gap-6 max-w-[761px] mx-auto">
        <Typography
          variant="large-title"
          classname="text-center max-lg:text-3xl max-md:text-2xl"
        >
          G<PERSON><PERSON> trị cốt lõi
        </Typography>
        <Typography
          variant="body-regular"
          classname="text-emphasize text-center"
        >
          Với lộ trình tăng trưởng bền vững cùng với hệ thống quản trị chuyên
          nghiệp là nền tảng vững chắc để <PERSON> không ngừng phát triển các sản
          phẩm, dịch vụ tài chính đáp ứng hiệu quả nhu cầu và tối ưu hóa lợi ích
          cho nhà đầu tư, cổ đông
        </Typography>
      </div>
      <div className="w-[calc(100%+((100vw-100%)/2))] h-full hidden md:flex">
        <Carousel
          setApi={setApi}
          // plugins={[plugin.current]}
          opts={{
            align: "start",
            loop: false,
          }}
          // onMouseEnter={plugin.current.stop}
          // onMouseLeave={plugin.current.reset}
          className="w-full relative"
        >
          <CarouselContent className="gap-4">
            {items.map((item: any, idx: number) => (
              <CarouselItem key={idx} className="basis-3/7 lg:basis-7/24">
                <IconBoxCardCustom item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
      <div className="md:hidden flex flex-col gap-8">
        {items.map((item: any, idx: number) => (
          <IconBoxCardCustom key={idx} item={item} />
        ))}
      </div>
    </div>
  );
}

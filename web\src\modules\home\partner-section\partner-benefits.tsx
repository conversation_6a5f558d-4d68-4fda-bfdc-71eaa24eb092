"use client";
import { Typography } from "@components/ui";
import { CustomIcon } from "./icons/custom-icon";

const listBenefits = [
  {
    // user plus
    icon: "M38 42.4502V30.4502M32 36.4502H44M24 30.4502H16C12.2725 30.4502 10.4087 30.4502 8.93853 31.0592C6.97831 31.8711 5.42092 33.4285 4.60896 35.3887C4 36.8589 4 38.7227 4 42.4502M31 7.03171C33.9318 8.21849 36 11.0928 36 14.4502C36 17.8076 33.9318 20.6819 31 21.8687M27 14.4502C27 18.8685 23.4183 22.4502 19 22.4502C14.5817 22.4502 11 18.8685 11 14.4502C11 10.0319 14.5817 6.4502 19 6.4502C23.4183 6.4502 27 10.0319 27 14.4502Z",
    title: "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> khách hàng dễ dàng",
    description:
      "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u kh<PERSON>ch hàng dễ dàng với công cụ được cung cấp trên các nền tảng <PERSON>",
  },
  {
    // file
    icon: "<PERSON>28 4.98926V13.2503C28 14.3704 28 14.9305 28.218 15.3583C28.4097 15.7346 28.7157 16.0406 29.092 16.2324C29.5198 16.4503 30.0799 16.4503 31.2 16.4503H39.4611M28 34.4502H16M32 26.4502H16M40 20.4266V34.8502C40 38.2105 40 39.8907 39.346 41.1741C38.7708 42.3031 37.8529 43.221 36.7239 43.7962C35.4405 44.4502 33.7603 44.4502 30.4 44.4502H17.6C14.2397 44.4502 12.5595 44.4502 11.2761 43.7962C10.1471 43.221 9.2292 42.3031 8.65396 41.1741C8 39.8907 8 38.2105 8 34.8502V14.0502C8 10.6899 8 9.00972 8.65396 7.72625C9.2292 6.59728 10.1471 5.6794 11.2761 5.10416C12.5595 4.4502 14.2397 4.4502 17.6 4.4502H24.0236C25.4911 4.4502 26.2249 4.4502 26.9154 4.61598C27.5276 4.76296 28.1129 5.00538 28.6497 5.33435C29.2552 5.7054 29.7741 6.22426 30.8118 7.26197L37.1882 13.6384C38.2259 14.6761 38.7448 15.195 39.1158 15.8005C39.4448 16.3373 39.6872 16.9226 39.8342 17.5348C40 18.2253 40 18.9591 40 20.4266Z",
    title: "Quản lý khách hàng trực quan",
    description:
      "Cung cấp công cụ quản lý khách hàng giới thiệu và hoạt động giao dịch theo thời gian ",
  },
  {
    // chart
    icon: "M24 6.4502H32.4C35.7603 6.4502 37.4405 6.4502 38.7239 7.10416C39.8529 7.6794 40.7708 8.59728 41.346 9.72625C42 11.0097 42 12.6899 42 16.0502V32.8502C42 36.2105 42 37.8907 41.346 39.1741C40.7708 40.3031 39.8529 41.221 38.7239 41.7962C37.4405 42.4502 35.7603 42.4502 32.4 42.4502H15.6C12.2397 42.4502 10.5595 42.4502 9.27606 41.7962C8.14708 41.221 7.2292 40.3031 6.65396 39.1741C6 37.8907 6 36.2105 6 32.8502V24.4502M16 26.4502V34.4502M32 22.4502V34.4502M24 14.4502V34.4502M10 16.4502V4.4502M4 10.4502H16",
    title: "Thống kê và quản lý K-Points",
    description:
      "K-Points được quản lý toàn diện trực quan và dễ sử dụng cho người khách hàng ",
  },
  {
    // shield
    icon: "M18 23.4504L22 27.4504L31 18.4504M40 24.4504C40 34.2673 29.2921 41.4072 25.396 43.6802C24.9532 43.9385 24.7318 44.0677 24.4194 44.1347C24.1769 44.1867 23.8231 44.1867 23.5806 44.1347C23.2682 44.0677 23.0468 43.9385 22.604 43.6802C18.7079 41.4072 8 34.2673 8 24.4504V14.8856C8 13.2866 8 12.4871 8.26152 11.7998C8.49255 11.1927 8.86797 10.651 9.35532 10.2215C9.90699 9.7353 10.6556 9.45458 12.1528 8.89312L22.8764 4.87177C23.2922 4.71585 23.5001 4.63789 23.714 4.60699C23.9037 4.57957 24.0963 4.57957 24.286 4.60699C24.4999 4.63789 24.7078 4.71585 25.1236 4.87177L35.8472 8.89312C37.3444 9.45458 38.093 9.7353 38.6447 10.2215C39.132 10.651 39.5075 11.1927 39.7385 11.7998C40 12.4871 40 13.2866 40 14.8856V24.4504Z",
    title: "Bảo mật danh sách khách hàng",
    description:
      "Lưu trữ an toàn, bảo vệ khỏi truy cập trái phép và không chia sẻ với bên thứ ba",
  },
];
const PartnerBenefit = () => {
  return (
    <div className="flex flex-col gap-8 md:gap-y-12 lg:gap-8 mx-auto md:grid grid-cols-2 lg:grid-cols-4">
      {listBenefits.map((benefit, index) => (
        <div
          className="group flex flex-col gap-6 lg:gap-10 text-center md:text-start"
          key={index}
        >
          <div className="w-fit mx-auto md:mx-0">
            <CustomIcon
              path={benefit.icon}
              className="group-hover:text-brand"
            />
          </div>
          <div className="flex flex-col gap-3 lg:gap-5">
            <Typography variant="title-3" className=" text-black lg:text-2xl">
              {benefit.title}
            </Typography>
            <Typography classname="font-normal text-base text-black leading-6">
              {benefit.description}
            </Typography>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PartnerBenefit;

import type { StrapiApp } from "@strapi/strapi/admin";
// @ts-ignore
import Menu<PERSON>ogo from "../extensions/logo-single.png";
// @ts-ignore
import AuthLogo from "../extensions/logo-white.png";

export default {
  config: {
    locales: ["en", "vi"],
    // Extend the translations
    // you can see the traslations keys here https://github.com/strapi/strapi/blob/develop/packages/core/admin/admin/src/translations
    translations: {
      vi: {
        "Auth.form.button.login.strapi": "Login via Kafi CMS",
        "Auth.form.welcome.subtitle": "Log in to your Admin",
        "Auth.form.welcome.title": "Welcome to Kafi CMS!",
        "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":
          "Made by Kafi",
        "app.components.LeftMenu.navbrand.title": "Kafi Dashboard",
      },
      en: {
        "Auth.form.button.login.strapi": "Login via Kafi CMS",
        "Auth.form.welcome.subtitle": "Log in to your Admin",
        "Auth.form.welcome.title": "Welcome to Kafi CMS!",
        "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":
          "Made by Kafi",
        "app.components.LeftMenu.navbrand.title": "Kafi Dashboard",
      },
    },
    head: {
      favicon: MenuLogo,
    },
    auth: {
      // Replace the Strapi logo in auth (login) views
      logo: AuthLogo,
    },
    menu: {
      // Replace the Strapi logo in the main navigation
      logo: MenuLogo,
    },
    // Disable video tutorials
    tutorials: false,
    // Disable notifications about new Strapi releases
    notifications: { releases: false },
  },
  bootstrap(app: StrapiApp) {
    console.log(app);
  },
};

// import ServerSiteHandler from '../../server-site-handler'

import { ClientSlugHandler, PageContent } from "@/components/common";
import { apiRoute } from "@/lib/constants/cms";
import { fetchContentType, generateMetadataObject } from "@/lib/data";
import { Metadata } from "next";

// @ts-ignore
export type paramsType = Promise<{ locale: string; slug: string[] }>;

type TProps = {
  params: paramsType;
};

export async function generateMetadata({ params }: TProps): Promise<Metadata> {
  const { slug, locale } = await params;

  const pageData = await fetchContentType({
    contentType: apiRoute.pages,
    params: {
      filters: {
        slug,
        locale,
      },
      populate: "seo.metaImage",
    },
    spreadData: true,
  });

  const seo = pageData?.seo;
  const metadata = generateMetadataObject(seo);
  return metadata;
}

export default async function Page({ params }: TProps) {
  const { slug, locale } = await params;

  const pageData = await fetchContentType({
    contentType: apiRoute.pages,
    params: {
      filters: {
        slug,
        locale,
      },
    },
    spreadData: true,
  });

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug;

      return acc;
    },
    { [locale]: slug },
  );

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageContent pageData={pageData} />
    </>
  );
}

export const dynamic = "force-dynamic";

{"kind": "collectionType", "collectionName": "promotions", "info": {"singularName": "promotion", "pluralName": "promotions", "displayName": "Promotion - List"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "title"}, "banner": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "elementals.promotion-banner", "repeatable": false}, "content": {"type": "customField", "pluginOptions": {"i18n": {"localized": true}}, "customField": "plugin::ckeditor5.CKEditor", "options": {"preset": "defaultHtml"}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::promotion-category.promotion-category", "inversedBy": "promotions"}, "views": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "thumbnail": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}}}
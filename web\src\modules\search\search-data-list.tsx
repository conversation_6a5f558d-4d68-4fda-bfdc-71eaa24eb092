"use client";

import AppPagination from "#/src/components/common/app-pagination";
import { Separator, Typography } from "#/src/components/ui";
import { TPagination } from "#/src/types/common";
import { SearchItem } from "#/src/types/search";
import { formatDate } from "date-fns";
import { useSearchParams } from "next/navigation";

type SearchDataListProps = {
  searchData: SearchItem[];
  pagination: TPagination;
};

export default function SearchDataList({
  searchData,
  pagination,
}: SearchDataListProps) {
  const searchParams = useSearchParams();
  const search = searchParams.get("search");
  if (searchData.length === 0 && search) {
    return (
      <div className="flex flex-col gap-4 text-center">
        <Typography variant="headline" classname="text-placeholder">
          Vui lòng thử lại với từ khóa khác
        </Typography>
      </div>
    );
  } else if (searchData.length === 0 && !search) {
    return (
      <div className="flex flex-col gap-4 text-center">
        <Typography variant="headline" classname="text-placeholder">
          Không có dữ liệu cho mục này
        </Typography>
      </div>
    );
  }
  return (
    <div className="flex flex-col gap-4">
      {searchData.map((item) => (
        <SearchDataItem key={item.id} item={item} />
      ))}
      <AppPagination
        page={pagination.page}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onChange={(nextPage) => console.log(nextPage)}
      />
    </div>
  );
}

type SearchDataItemProps = {
  item: SearchItem;
};

function SearchDataItem({ item }: SearchDataItemProps) {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-3">
        <Typography variant="headline" classname="text-placeholder">
          {item.category.title}
        </Typography>
        <div
          className="line-clamp-1"
          dangerouslySetInnerHTML={{ __html: item.title }}
        />
        <div
          className="line-clamp-2"
          dangerouslySetInnerHTML={{ __html: item.description }}
        />
        <Typography variant="body-regular" className="text-default">
          {formatDate(new Date(item.created_at), "dd/MM/yyyy")}
        </Typography>
      </div>
      <Separator orientation="horizontal" className="bg-border-button h-1" />
    </div>
  );
}

type ShareLinkIconProps = {
  size?: number;
  className?: string;
};

export const ShareLinkIcon = ({ size = 24, className }: ShareLinkIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3382_13291)">
        <path
          d="M12.4715 16.2423L11.5287 17.1851C10.227 18.4868 8.11644 18.4868 6.81469 17.1851C5.51294 15.8834 5.51294 13.7728 6.81469 12.4711L7.7575 11.5282M16.2428 12.4711L17.1856 11.5282C18.4873 10.2265 18.4873 8.11595 17.1856 6.8142C15.8838 5.51245 13.7733 5.51245 12.4715 6.8142L11.5287 7.75701M9.66681 14.333L14.3335 9.6663"
          stroke="#767C82"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3382_13291">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(4 4)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

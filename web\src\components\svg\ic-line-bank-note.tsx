type LineBankNoteIconProps = {
  size?: number;
  className?: string;
};

export const LineBankNoteIcon = ({
  size = 48,
  className,
}: LineBankNoteIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M28 18H23C21.3431 18 20 19.3431 20 21C20 22.6569 21.3431 24 23 24H25C26.6569 24 28 25.3431 28 27C28 28.6569 26.6569 30 25 30H20M24 16V18M24 30V32M36 24H36.02M12 24H12.02M4 16.4L4 31.6C4 33.8402 4 34.9603 4.43597 35.816C4.81947 36.5686 5.43139 37.1805 6.18404 37.564C7.03968 38 8.15979 38 10.4 38L37.6 38C39.8402 38 40.9603 38 41.816 37.564C42.5686 37.1805 43.1805 36.5686 43.564 35.816C44 34.9603 44 33.8402 44 31.6V16.4C44 14.1598 44 13.0397 43.564 12.184C43.1805 11.4314 42.5686 10.8195 41.816 10.436C40.9603 10 39.8402 10 37.6 10L10.4 10C8.15979 10 7.03969 10 6.18404 10.436C5.43139 10.8195 4.81947 11.4314 4.43597 12.184C4 13.0397 4 14.1598 4 16.4ZM37 24C37 24.5523 36.5523 25 36 25C35.4477 25 35 24.5523 35 24C35 23.4477 35.4477 23 36 23C36.5523 23 37 23.4477 37 24ZM13 24C13 24.5523 12.5523 25 12 25C11.4477 25 11 24.5523 11 24C11 23.4477 11.4477 23 12 23C12.5523 23 13 23.4477 13 24Z"
        stroke="#767C82"
        className={className}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

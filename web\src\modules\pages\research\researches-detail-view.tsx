"use client";
import { CATEGORY_RESEARCH } from "#/src/__mock__/research";
import { format } from "date-fns";
import {
  Button,
  Input,
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Calendar as CalendarComponent,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
} from "#/src/components/ui";
import { ArrowLeft, Calendar } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { cn } from "#/src/lib";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";

export default function ResearchesDetailView() {
  const { slug } = useParams();
  const selectedSubCategorySlug = typeof slug === "string" ? slug : "";
  const router = useRouter();
  const [fromDate, setFromDate] = useState<Date | undefined>(new Date());
  const [toDate, setToDate] = useState<Date | undefined>(new Date());
  const [selectedCategoryTitle, setSelectedCategoryTitle] = useState<
    string | undefined
  >(undefined);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const pages = [1, 2, 3, "ellipsis", 40];
  const renderPageItem = (page: number | string) => {
    if (page === "ellipsis") {
      return (
        <PaginationItem key="ellipsis">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    const isActive = currentPage === page;
    return (
      <PaginationItem key={page} onClick={() => setCurrentPage(Number(page))}>
        <PaginationLink
          href="#"
          isActive={isActive}
          className={cn(
            "font-semibold text-emphasize",
            isActive && "!bg-brand !border-0 !text-white",
          )}
        >
          {page}
        </PaginationLink>
      </PaginationItem>
    );
  };
  const handleGoBack = () => {
    router.push("/research");
  };
  const handleChangeSubCategory = (slug: string) => {
    router.push(`${slug}`);
  };
  const handleChangeCategoryTitle = (categoryTitle: string) => {
    setSelectedCategoryTitle(categoryTitle);
    const matchingCategory = CATEGORY_RESEARCH.find(
      (category) =>
        category.title.toLocaleLowerCase() ===
        categoryTitle.toLocaleLowerCase(),
    );
    if (matchingCategory) {
      router.push(`/research/${matchingCategory.subCategories[0].slug}`);
    }
  };
  const matchingSubCategory = useMemo(() => {
    if (!slug || typeof slug !== "string") return undefined;
    return CATEGORY_RESEARCH.flatMap((category) => category.subCategories).find(
      (subCategory) => subCategory.slug === selectedSubCategorySlug,
    );
  }, [selectedSubCategorySlug, slug]);
  useEffect(() => {
    if (slug) {
      const matchingCategory = CATEGORY_RESEARCH.find((category) =>
        category.subCategories.some((subCategory) => subCategory.slug === slug),
      );

      if (matchingCategory) {
        setSelectedCategoryTitle(matchingCategory.title);
      }
    }
  }, [slug]);

  return (
    <div className="container flex flex-col gap-7 lg:gap-5">
      <Button
        variant={"outline"}
        className="flex items-center gap-2 w-fit !p-0 !border-0 hover:!bg-transparent"
        onClick={handleGoBack}
      >
        <ArrowLeft className="size-6 text-icon-default" />
        <Typography variant={"button-label"}>Quay lại</Typography>
      </Button>
      <div className="lg:bg-screen rounded-2xl lg:p-8 flex gap-6 lg:gap-10 flex-col lg:flex-row">
        <div className="hidden min-w-[377px] lg:flex flex-col gap-10 border-r border-subtle">
          {CATEGORY_RESEARCH.map((category, index) => (
            <div key={index} className="flex flex-col gap-6">
              <Typography variant="title-2" classname="text-emphasize">
                {category.title}
              </Typography>
              <div className="flex flex-col gap-3">
                {category.subCategories.map((subCategory, index) => (
                  <div
                    key={index}
                    onClick={() => handleChangeSubCategory(subCategory.slug)}
                  >
                    <Typography
                      variant="headline"
                      classname={cn(
                        "text-default hover:text-brand cursor-pointer ease-in-out transition-all duration-500",
                        slug === subCategory.slug && "text-brand",
                      )}
                    >
                      {subCategory.title}
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <Tabs
          value={selectedCategoryTitle}
          onValueChange={(data) => handleChangeCategoryTitle(data)}
          className="lg:hidden gap-6"
        >
          <TabsList className="bg-transparent p-0 gap-6">
            {CATEGORY_RESEARCH.map((category, index) => (
              <TabsTrigger
                value={category.title}
                key={index}
                className="group data-[state=active]:shadow-none data-[state=active]:border-0 p-0 flex flex-col justify-end gap-2.5 items-start"
              >
                <Typography
                  variant="headline"
                  classname="text-emphasize text-nowrap group-data-[state=active]:text-brand"
                >
                  {category.title}
                </Typography>
                <div className="min-h-1 opacity-0 bg-brand w-1/2 group-data-[state=active]:opacity-100 rounded-full "></div>
              </TabsTrigger>
            ))}
          </TabsList>
          {CATEGORY_RESEARCH.map((category, index) => (
            <TabsContent key={index} value={category.title}>
              <div className="flex flex-col md:flex-row md:justify-between gap-6 ">
                <Select
                  value={selectedSubCategorySlug}
                  onValueChange={(data) => handleChangeSubCategory(data)}
                >
                  <SelectTrigger className="w-full md:w-1/3 border-border-button text-emphasize text-sm leading-5 font-medium bg-white rounded-xl min-h-14">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-canvas text-emphasize z-50">
                    {category.subCategories.map((subCategory, index) => (
                      <SelectItem key={index} value={subCategory.slug}>
                        {subCategory.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex justify-between w-full md:w-2/3 gap-6">
                  <DatePopover
                    label="Từ ngày"
                    date={fromDate}
                    setDate={setFromDate}
                    className="h-14"
                  />
                  <DatePopover
                    label="Đến ngày"
                    date={toDate}
                    setDate={setToDate}
                    className="h-14"
                  />
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
        <div className="flex flex-col gap-4 lg:gap-10">
          <div className="hidden lg:flex gap-6">
            <DatePopover
              label="Từ ngày"
              date={fromDate}
              setDate={setFromDate}
            />
            <DatePopover label="Đến ngày" date={toDate} setDate={setToDate} />
          </div>
          <div className="flex flex-col gap-6">
            {matchingSubCategory?.documentsList.map((document, index) => (
              <DocumentItem
                key={index}
                subCategory={matchingSubCategory?.title}
                title={document.title}
                content={document.content}
                publishedDate={document.publishedDate}
                url={document.url}
              />
            ))}
          </div>
          <Pagination className="lg:mx-0 lg:justify-start">
            <PaginationContent className="items-end">
              {pages.map(renderPageItem)}
            </PaginationContent>
          </Pagination>
        </div>
      </div>
    </div>
  );
}

type DocumentItemProps = {
  subCategory: string;
  title: string;
  content: string;
  publishedDate: string;
  url: string;
};

const DocumentItem = ({
  subCategory,
  title,
  content,
  publishedDate,
  url,
}: DocumentItemProps) => {
  return (
    <div className="flex flex-col py-4 gap-3 border-b border-subtle last:border-0">
      <div className="w-full flex justify-between">
        <Typography variant="headline" classname="text-placeholder uppercase">
          {subCategory}
        </Typography>
      </div>
      <div className="flex flex-col gap-3">
        <Link href={url} download={"sample"} target="_blank">
          <Typography
            variant="title-2"
            classname="text-default line-clamp-1 cursor-pointer"
          >
            {title}
          </Typography>
        </Link>

        <Typography
          variant="body-regular"
          classname="text-default line-clamp-2"
        >
          {content}
        </Typography>
        <Typography
          variant="body-regular"
          classname="text-default line-clamp-2"
        >
          {publishedDate}
        </Typography>
      </div>
    </div>
  );
};

type DatePopoverProps = {
  label: string;
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  className?: string;
  triggerClassName?: string;
};

export const DatePopover = ({
  label,
  date,
  setDate,
  className,
  triggerClassName,
}: DatePopoverProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild className={cn("cursor-pointer", className)}>
        <div
          className={cn(
            "relative border border-border-button lg:border-0 xl:min-w-[256px] bg-screen lg:bg-canvas rounded-xl px-4 py-2",
            triggerClassName,
          )}
        >
          <div className="w-full flex justify-between items-center">
            <div className="flex flex-col gap-0.5">
              <Typography variant="caption-regular" classname="text-default">
                {label}
              </Typography>
              <Input
                value={date ? format(date, "dd/MM/yyyy") : ""}
                readOnly
                className="focus-visible:ring-0 border-0 p-0 shadow-none h-auto text-sm font-medium text-emphasize"
              />
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full"
            >
              <Calendar className="h-4 w-4 text-gray-500" />
            </Button>
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <CalendarComponent
          mode="single"
          selected={date}
          onSelect={setDate}
          className=""
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
};

"use client";

import { Button, Typography } from "#/src/components/ui";
import { fNumber } from "#/src/utils/number-format";
import GBIChart from "./gbi-chart";
import SliderItem from "./gbi-slider-item";
import { useGBICalculator } from "./use-gbi-calculate";
import { EGbiField, SLIDER_CONFIG } from "./configs";
import { cn } from "@/lib";
// import { getInitialInvestmentRatio_Z } from "@/services/calculate-gbi";

const GBIToolView = () => {
  const { state, changeValue, reset } = useGBICalculator();

  const isInitial = state.targetAmount_g1 === 0;

  const renderHeading = (
    <Typography
      variant="large-title"
      className="text-balance text-center leading-tight"
    >
      <PERSON><PERSON><PERSON> tư mục tiêu cá nhân
      <br />
      Chưa bao giờ dễ dàng đến thế
    </Typography>
  );

  const renderInvestmentForm = (
    <>
      <Typography variant="title-2" className="text-center">
        Th<PERSON> ngay số tiền đầu tư ban đầu của bạn
      </Typography>
      <div className="flex flex-col gap-10 max-w-2xl w-full mx-auto">
        <Typography variant="title-3">Số tiền đầu tư ban đầu</Typography>
        <SliderItem
          defaultValue={state.initialInvestment_x ?? 0}
          min={SLIDER_CONFIG.initialInvestment.min}
          max={SLIDER_CONFIG.initialInvestment.max}
          step={SLIDER_CONFIG.initialInvestment.step}
          onChange={(vals: number[]) => {
            changeValue(EGbiField.InitialInvestment_x, vals[0]);
          }}
          color="gradient"
          hideLabel
        />
        <Button variant="default" color="white" className="w-fit mx-auto">
          {fNumber(state.initialInvestment_x ?? 0)}
        </Button>
      </div>
    </>
  );

  const renderCardInputs = (
    <div className="flex flex-col gap-6">
      <Typography>Tổng giá trị dự kiến</Typography>
      <Typography variant="title-0" classname="text-primary-500">
        {isInitial ? "0" : `~${fNumber(state.gbi ?? 0)}`}
      </Typography>
      <Typography
        variant="subheadline-medium"
        className="border-2 border-dashed border-brand-middle rounded-md p-2"
      >
        {isInitial
          ? "-"
          : `Mục tiêu có thể đạt ${fNumber(state.gbiPercentage ?? 0, 2)}%`}
      </Typography>

      <SliderItem
        label="Mục tiêu (g1)"
        defaultValue={state.targetAmount_g1 ?? 0}
        min={SLIDER_CONFIG.targetAmount.min}
        max={SLIDER_CONFIG.targetAmount.max}
        step={SLIDER_CONFIG.targetAmount.step}
        onChange={(vals: number[]) => {
          changeValue(EGbiField.TargetAmount_g1, vals[0]);
        }}
      />
      <SliderItem
        label="Lợi nhuận kỳ vọng (r)"
        defaultValue={state.annualReturn_r}
        unit="%"
        min={SLIDER_CONFIG.annualReturn.min}
        max={SLIDER_CONFIG.annualReturn.max}
        step={SLIDER_CONFIG.annualReturn.step}
        onChange={(vals: number[]) => {
          changeValue(EGbiField.AnnualReturn_r, vals[0]);
        }}
        color="heatmap"
      />
      <SliderItem
        label="Thời gian (t)"
        defaultValue={state.timePeriod_t}
        unit="tháng"
        min={SLIDER_CONFIG.timePeriod.min}
        max={SLIDER_CONFIG.timePeriod.max}
        step={SLIDER_CONFIG.timePeriod.step}
        onChange={(vals: number[]) => {
          changeValue(EGbiField.TimePeriod_t, vals[0]);
        }}
      />
      <SliderItem
        label={`Đầu tư ban đầu (x)`}
        defaultValue={state.initialInvestment_x ?? 0}
        min={SLIDER_CONFIG.initialInvestment.min}
        max={SLIDER_CONFIG.initialInvestment.max}
        step={SLIDER_CONFIG.initialInvestment.step}
        onChange={(vals: number[]) => {
          // const z = getInitialInvestmentRatio_Z(state.targetAmount_g1 ?? 0);
          // const maxX = z * (state.targetAmount_g1 ?? 0);
          // const x = vals[0];
          // const isError = x > maxX;

          changeValue(EGbiField.InitialInvestment_x, vals[0]);
        }}
        disabled={state.targetAmount_g1 === 0}
      />
      <SliderItem
        label="Số tiền nộp hàng tháng (a)"
        defaultValue={state.monthlyDeposit_a ?? 0}
        value={Math.max(0, state.monthlyDeposit_a ?? 0)}
        min={0}
        max={100000000}
        step={10000000}
        onChange={(vals: number[]) => {
          changeValue(EGbiField.MonthlyDeposit_a, vals[0]);
        }}
        disabled={state.targetAmount_g1 === 0}
      />

      <div className="flex flex-col lg:flex-row justify-between items-end w-full mt-2">
        <div className="text-sm text-gray-500">
          <Typography variant="caption-regular">
            Thay đổi trường thông tin, Kafi sẽ tự động sắp xếp chiến lược đầu tư
            phù hợp cho khách hàng.
          </Typography>
          <Typography variant="caption-regular" classname="block">
            Lưu ý: Lợi nhuận càng cao thì biến động rủi ro càng nhiều
          </Typography>
        </div>
        {!isInitial && (
          <Button variant="text" onClick={reset}>
            <span className="text-sm text-primary-500">Làm lại</span>
          </Button>
        )}
      </div>
    </div>
  );

  const renderCardChart = (
    <div className="h-full">
      <Typography variant="title-2">Kết quả đầu tư</Typography>
      <div
        className={cn(
          "flex flex-col gap-4 h-full",
          isInitial && "justify-center items-center",
        )}
      >
        {isInitial ? (
          <div className="text-neutral-600 text-sm bg-[#F1F3F5] rounded-lg p-4 max-w-sm mx-auto text-center">
            Kéo thanh <strong>&quot;Mục tiêu&quot;</strong> của bạn ở bên trái,{" "}
            <strong>Kafi</strong> sẽ <strong>tự động</strong> thiết lập hoàn
            thiện phần còn lại.
          </div>
        ) : (
          <GBIChart
            currentMonth={state.timePeriod_t}
          />
        )}
      </div>
    </div>
  );

  const renderCard = (
    <div className="bg-white text-emphasize p-8 rounded-lg w-full grid grid-cols-1 lg:grid-cols-12 gap-4">
      <div className="col-span-4">{renderCardInputs}</div>
      <div className="col-span-8">{renderCardChart}</div>
    </div>
  );

  return (
    <div className="min-h-screen">
      <section className="container mx-auto flex flex-col gap-4 md:gap-10">
        {renderHeading}

        {renderInvestmentForm}

        {renderCard}
      </section>
    </div>
  );
};

// ------------------------------------------------------------

export default GBIToolView;

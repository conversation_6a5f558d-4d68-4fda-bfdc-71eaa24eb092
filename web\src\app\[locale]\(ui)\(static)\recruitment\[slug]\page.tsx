import { RecruitmentDetailView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Research Detail - Kafi",
  description: "Research Detail - Kafi",
};

const RecruitmentDetailPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <RecruitmentDetailView />
    </Suspense>
  );
};

export default RecruitmentDetailPage;

export const dynamic = "force-dynamic";

import { cmsContentType } from "../constants/cms";
import { QueryParams, useClientFetch } from "./use-client-fetch";

const CNT_ARTICLES = cmsContentType.articles;
const CNT_ARTICLES_CATEGORY = cmsContentType.articleCategories;

/**
 * GET LIST ARTICLES
 */
export const useGetArticles = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_ARTICLES,
    params: {
      filters,
      locale,
      status: status || "published",
      sort: sort || "publishedAt:desc",
      pagination,
      populate,
      fields,
    },
  });

  return {
    data,
    isLoading,
    error,
    mutate,
  };
};

/**
 * GET ARTICLE BY SLUG
 */
export const useGetArticleBySlug = ({
  slug,
  locale,
}: {
  slug: string;
  locale?: string;
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_ARTICLES,
    params: {
      locale,
      filters: {
        slug,
      },
      populate: "*",
    },
  });

  return {
    data,
    isLoading,
    error,
    mutate,
  };
};

/**
 * GET LIST ARTICLES CATEGORY
 */
export const useGetArticlesCategory = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_ARTICLES_CATEGORY,
    params: {
      filters,
      locale,
      status,
      sort,
      pagination,
      populate,
      fields,
    },
  });

  return {
    data,
    isLoading,
    error,
    mutate,
  };
};

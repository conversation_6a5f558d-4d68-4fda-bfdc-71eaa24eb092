import { SEO_DATA } from "@/lib/data/next-metadata";
import { strapiImage } from "@/lib/data/strapi-image";

export function generateMetadataObject(seo: any, title?: string) {
  return {
    title: seo?.metaTitle || title || SEO_DATA.title, // Fallback to 'Default Title' if title is not provided
    description: seo?.metaDescription || SEO_DATA.description, // Fallback to 'Default Description'
    openGraph: {
      title: seo?.ogTitle || seo?.metaTitle || SEO_DATA.title,
      description:
        seo?.ogDescription || seo?.metaDescription || SEO_DATA.description,
      images: seo?.metaImage ? [{ url: strapiImage(seo?.metaImage.url) }] : [],
    },
    twitter: {
      card: seo?.twitterCard || "summary_large_image",
      title: seo?.twitterTitle || seo?.metaTitle || SEO_DATA.title,
      description:
        seo?.twitterDescription || seo?.metaDescription || SEO_DATA.description,
      images: seo?.twitterImage ? [{ url: seo.twitterImage }] : [],
    },
  };
}

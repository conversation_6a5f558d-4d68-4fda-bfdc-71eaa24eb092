import {
  TRADE_FAQ,
  TRA<PERSON>_HERO_SECTION,
  TRADE_SERVICES,
  TRADE_TOGGLE_CARDS,
} from "#/src/__mock__";
import PageHeroSection from "@components/dynamic-zone/page-hero-section";
import ToggleCardsSection from "@components/dynamic-zone/toggle-cards-section";
import { Faqs, SupportInfo } from "@/modules/shared";
import AvailablePlatforms from "../trade/components/available-platforms";
import SecuritiesServices from "../trade/components/securities-services";
import { MarginSection } from "../trade/margin-section";
import { TradingFeeSchedule } from "../trade/trading-fee-schedule-section";

export default function TradeView() {
  return (
    <div className="flex flex-col gap-y-10 sm:gap-y-20">
      <PageHeroSection data={TRADE_HERO_SECTION} />
      <AvailablePlatforms />
      <div data-id="trade-products-section">
        <ToggleCardsSection data={TRADE_TOGGLE_CARDS} />
      </div>
      <SecuritiesServices data={TRADE_SERVICES} />
      <MarginSection />
      <TradingFeeSchedule />
      <Faqs dataFAQ={TRADE_FAQ} />
      <SupportInfo />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

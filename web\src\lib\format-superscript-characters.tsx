export const SUPERSCRIPT_CHARACTERS_MAP = {
  "®": { element: "sup", className: "text-[0.6em] font-normal" },
  "™": { element: "sup", className: "text-[0.6em] font-normal" },
  "©": { element: "sup", className: "text-[0.6em] font-normal" },
  "℠": { element: "sup", className: "text-[0.6em] font-normal" },
  "†": { element: "sup", className: "text-[0.6em]" },
  "‡": { element: "sup", className: "text-[0.6em]" },
  "°": { element: "sup", className: "text-[0.8em]" },
  "′": { element: "sup", className: "text-[0.8em]" },
  "″": { element: "sup", className: "text-[0.8em]" },
  "½": { element: "span", className: "font-normal" },
  "¼": { element: "span", className: "font-normal" },
  "¾": { element: "span", className: "font-normal" },
} as const;

type SuperscriptCharacter = keyof typeof SUPERSCRIPT_CHARACTERS_MAP;

export const formatSuperscriptCharacters = (text: string) => {
  const pattern = new RegExp(
    `(${Object.keys(SUPERSCRIPT_CHARACTERS_MAP)
      .map((char) => `\\${char}`)
      .join("|")})`,
  );

  const parts = text.split(pattern);

  return parts.map((part, index) => {
    const config = SUPERSCRIPT_CHARACTERS_MAP[part as SuperscriptCharacter];

    if (!config) return part;

    const Element = config.element;
    return (
      <Element key={index} className={config.className}>
        {part}
      </Element>
    );
  });
};

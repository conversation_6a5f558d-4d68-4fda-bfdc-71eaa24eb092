import PromotionCard from "../card/promotion-card";
//-----------------------------------------------------------
type IProps = {
  data: any;
};
//-----------------------------------------------------------
export default function PromotionList({ data }: IProps) {
  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {data?.length &&
            data.map((item: any, idx: number) => (
              <PromotionCard key={idx} promotion={item} />
            ))}
        </div>
      </div>
    </section>
  );
}

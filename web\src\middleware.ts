/*
 * For more info see
 * https://nextjs.org/docs/app/building-your-application/routing/internationalization
 * */

import Negotiator from "negotiator";
import { type NextRequest, NextResponse } from "next/server";
import linguiConfig from "#/lingui.config";

import { LINGUI_CONFIG } from "./config-global";

const { locales } = linguiConfig;

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // if includes params :locale. check locale is valid, if

  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`,
  );

  if (pathnameHasLocale) return;

  // Redirect if there is no locale
  const locale = getRequestLocale(request.headers);
  request.nextUrl.pathname = `/${locale}${pathname}`;
  // e.g. incoming request is /products
  // The new URL is now /en/products
  return NextResponse.redirect(request.nextUrl);
}

function getRequestLocale(requestHeaders: Headers): string {
  const langHeader = requestHeaders.get("accept-language") || undefined;
  const languages = new Negotiator({
    headers: { "accept-language": langHeader },
  }).languages(locales.slice());

  const activeLocale =
    languages[0] || locales[0] || LINGUI_CONFIG.defaultLocale || "vi";

  return activeLocale;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * - sitemap.xml, robots.txt
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!_next/static|_next/image|favicon.ico|design-system|sitemap.xml|robots.txt|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};

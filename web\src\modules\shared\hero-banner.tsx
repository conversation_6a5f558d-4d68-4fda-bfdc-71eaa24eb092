"use client";

import Image from "next/image";

interface StatisticItem {
  value: string;
  label: string;
}

const statistics: StatisticItem[] = [
  {
    value: "+150%",
    label: "<PERSON><PERSON><PERSON> nhuận\nhàng năm",
  },
  {
    value: "+2,000",
    label: "Tà<PERSON> khoản đăng ký mới\nhàng tháng",
  },
  {
    value: "+4.520 tỷ",
    label: "Gi<PERSON> trị giao dịch\nhàng ngày",
  },
];

export default function HeroBanner() {
  return (
    <section
      className="relative py-20"
      style={{
        background: "url(/patterns/banner-information.png)",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        backgroundSize: "contain",
      }}
    >
      <div className="container relative overflow-hidden">
        <div className="">
          {/* Background Pattern */}
          <div className="relative z-10 w-full max-w-[680px] mx-auto">
            <div className="flex flex-col items-center">
              {/* Top Content */}
              <div className="text-center flex flex-col items-center gap-3">
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
                  Đầu tư thông minh,
                  <br />
                  sinh lời hiệu quả
                </h1>
                <p className="text-base text-gray-600 mx-auto lg:mx-0">
                  Chiến lược đầu tư thông minh hi<PERSON>u quả, giúp bạn tăng trưởng{" "}
                  <strong>5.00% - 15.00% / Năm</strong>
                </p>
                <button className="bg-brand w-fit text-white font-semibold px-8 py-3 rounded-full transition-colors duration-200">
                  Đăng ký ngay
                </button>
              </div>
              {/* Image */}
              <div className="relative max-w-[5">
                <Image
                  src="/images/home/<USER>"
                  alt="Investment growth illustration"
                  className="object-contain w-full h-full"
                  width={534}
                  height={426}
                />
              </div>
              {/* Statistics */}
              <div className="flex items-start justify-between max-w-[623px] px-[30px] w-full">
                {statistics.map((stat, index) => (
                  <div
                    key={index}
                    className="text-center flex flex-col items-center justify-center w-[153px]"
                  >
                    <div className="w-full h-[53px] flex items-center justify-center">
                      <div className="text-2xl leading-8 font-semibold whitespace-nowrap shrink-0">
                        {stat.value}
                      </div>
                    </div>
                    <div className="text-sm whitespace-pre-line leading-5">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

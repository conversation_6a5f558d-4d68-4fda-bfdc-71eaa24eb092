import { CONTACT, LOCATIONS } from "#/src/__mock__/contact-location";
import { Typography } from "#/src/components/ui";
import AddressesView from "./components/addresses-view";
import ContactInfoView from "./components/contact-info-view";

export default function ContactLocationView() {
  return (
    <div className="flex flex-col">
      <div className="flex flex-col container !py-0 gap-4 mb-8">
        <Typography
          variant="special-title"
          classname="max-lg:text-4xl max-md:text-2xl max-md:font-semibold text-center"
        >
          Đ<PERSON><PERSON> chỉ & <PERSON><PERSON><PERSON> h<PERSON>
        </Typography>
        <Typography
          variant="body-regular"
          classname="text-emphasize text-center max-w-[634px] mx-auto"
        >
          Danh sách các chi nhánh và phòng giao dịch Kafi Dễ dàng tìm kiếm địa
          điểm gần bạn để được hỗ trợ trực tiếp.
        </Typography>
      </div>
      <AddressesView addressesList={LOCATIONS} />
      <ContactInfoView contactList={CONTACT} />
    </div>
  );
}

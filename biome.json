{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "warn"}, "suspicious": {"noArrayIndexKey": "off", "noExplicitAny": "off", "noDoubleEquals": "off", "noEmptyBlock": "off"}, "style": {"noNonNullAssertion": "off", "useImportType": "off", "useSelfClosingElements": "off", "noInferrableTypes": "off", "noUselessElse": "off", "useNodejsImportProtocol": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "a11y": {"noAriaHiddenOnFocusable": "off", "useKeyWithClickEvents": "off", "noLabelWithoutControl": "off", "useValidAnchor": "off", "useAnchorContent": "off", "useButtonType": "off", "useValidAriaRole": "off", "noSvgWithoutTitle": "off", "useSemanticElements": "off", "useFocusableInteractive": "off", "noStaticElementInteractions": "off"}, "complexity": {"useLiteralKeys": "off", "noBannedTypes": "off", "useOptionalChain": "off", "noForEach": "off", "noUselessFragments": "off"}, "performance": {"noAccumulatingSpread": "off", "noImgElement": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": {"level": "off", "options": {"groups": [[":URL:", ":NODE:"], ":BLANK_LINE:", ["@components/**"], ":BLANK_LINE:", ["@/**"], ":BLANK_LINE:", ["./**"]]}}}}}}
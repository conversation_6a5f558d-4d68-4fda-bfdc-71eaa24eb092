import LoadingPage from "@/components/loading/loading-page";
import { NewsEventsDetailView } from "@/modules/pages";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "News & Events Detail - Kafi",
  description: "News & Events Detail - Kafi",
};

const NewsEventsPage = () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <NewsEventsDetailView />
    </Suspense>
  );
};

export default NewsEventsPage;

export const dynamic = "force-dynamic";

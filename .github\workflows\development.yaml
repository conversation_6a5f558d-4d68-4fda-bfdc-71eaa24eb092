name: Build NextJS Source
env:
  VERCEL_ORG_ID:
  VERCEL_PROJECT_ID:
on:
  push:
    branches:
      - develop
jobs:
  Build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - name: Access Nextjs Source
        run: cd web
      - name: Install packages
        run: bun install
      - name: Build Project Artifacts
        run: bun run build

type DesktopLogoProps = {
  size?: number;
  width?: number;
  height?: number;
  className?: string;
};

export const DesktopLogo = ({
  width = 37,
  height,
  className,
}: DesktopLogoProps) => {
  const computedHeight = height ?? (width / 37) * 40;
  return (
    <svg
      width={width}
      height={computedHeight}
      viewBox="0 0 37 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M35.3127 40H25.6019C25.2699 39.9999 24.9429 39.9185 24.6496 39.7629C24.3563 39.6074 24.1056 39.3824 23.9195 39.1078L0 3.67913V1.7548C0 1.2894 0.185073 0.843054 0.514523 0.513966C0.843973 0.184878 1.29081 1.73098e-06 1.75672 1.73098e-06H9.26311C9.59528 -0.000432174 9.92247 0.0807157 10.2159 0.236304C10.5092 0.391891 10.7598 0.617145 10.9456 0.892232L36.208 38.3045C36.3205 38.4671 36.3863 38.6574 36.3981 38.8546C36.4099 39.0519 36.3673 39.2486 36.275 39.4234C36.1827 39.5982 36.0442 39.7444 35.8745 39.8461C35.7049 39.9477 35.5105 40.001 35.3127 40Z"
        fill="url(#paint0_linear_2687_24063)"
      />
      <path
        d="M9.26099 0H1.75672C0.786512 0 0 0.785648 0 1.75479V38.2367C0 39.2059 0.786512 39.9915 1.75672 39.9915H9.26099C10.2312 39.9915 11.0177 39.2059 11.0177 38.2367V1.75479C11.0177 0.785648 10.2312 0 9.26099 0Z"
        fill="url(#paint1_linear_2687_24063)"
      />
      <path
        d="M36.208 1.68274L10.935 39.0993C10.7494 39.3741 10.4992 39.5991 10.2062 39.7547C9.91319 39.9103 9.58644 39.9916 9.25463 39.9915H1.75672C1.29081 39.9915 0.843973 39.8066 0.514523 39.4775C0.185073 39.1485 0 38.7021 0 38.2367V36.306L23.9131 0.892234C24.0986 0.617444 24.3489 0.392376 24.6419 0.236803C24.9349 0.0812305 25.2616 -8.31874e-05 25.5934 3.36605e-06H35.3106C35.5069 -0.00048631 35.6995 0.0524592 35.868 0.153157C36.0364 0.253855 36.1741 0.398498 36.2664 0.57153C36.3587 0.744561 36.402 0.939444 36.3917 1.13523C36.3814 1.33102 36.3179 1.5203 36.208 1.68274Z"
        fill="url(#paint2_linear_2687_24063)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2687_24063"
          x1="30.486"
          y1="40.1928"
          x2="5.20129"
          y2="0.900841"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.15" stopColor="#0C6070" />
          <stop offset="0.51" stopColor="#096B6E" />
          <stop offset="0.99" stopColor="#07756D" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2687_24063"
          x1="5.50992"
          y1="0.201335"
          x2="5.50992"
          y2="38.8365"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.14" stopColor="#07756D" />
          <stop offset="0.35" stopColor="#058977" />
          <stop offset="0.76" stopColor="#01BB8F" />
          <stop offset="0.85" stopColor="#00C795" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_2687_24063"
          x1="4.65702"
          y1="39.449"
          x2="30.154"
          y2="0.578292"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00C795" />
          <stop offset="0.19" stopColor="#02CB94" />
          <stop offset="0.39" stopColor="#09D892" />
          <stop offset="0.59" stopColor="#13EC8F" />
          <stop offset="0.66" stopColor="#18F58E" />
        </linearGradient>
      </defs>
    </svg>
  );
};

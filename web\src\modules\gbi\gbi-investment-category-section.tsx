"use client";
import { Separator, Slider, Typography } from "@/components/ui";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useMediaQuery } from "@/hooks/use-media-query";
import { motion } from "framer-motion";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";

export type AssetInfo = {
  value: string;
  percentage: string;
};

export type InvestmentCategory = {
  title: string;
  description: string;
  profit: string;
  image: string;
  risk: string;
  [assetCode: string]: string | AssetInfo;
};

const mockInvestmentCategoryData: InvestmentCategory[] = [
  {
    title: "An toàn",
    description:
      "Danh mục An toàn tập trung vào các tài sản có thu nhập cố định như tr<PERSON>i phiếu ch<PERSON>h phủ, chứng chỉ tiền gửi, và các sản phẩm tài chính ổn định khác. <PERSON><PERSON><PERSON> là lựa chọn lý tưởng cho những mục tiêu ngắn đến trung hạn (dưới 3 năm) như quỹ khẩn cấp, mua sắm lớn, hoặc bảo toàn tài sản",
    profit: "2-4%/năm",
    image: "/images/gbi/investment-profit-1.png",
    risk: "Thấp",
    ACB: {
      value: "12,234,567",
      percentage: "23%",
    },
    VIB: {
      value: "6,244,567",
      percentage: "13%",
    },
    GMD: {
      value: "4,345,567",
      percentage: "23%",
    },
    TCB: {
      value: "12,234,567",
      percentage: "23%",
    },
    MBB: {
      value: "6,244,567",
      percentage: "13%",
    },
    VPB: {
      value: "4,345,567",
      percentage: "23%",
    },
  },
  {
    title: "Sinh lợi",
    description:
      "Danh mục Sinh lợi kết hợp linh hoạt giữa cổ phiếu tăng trưởng và các công cụ thu nhập cố định. Mục tiêu là tối ưu hóa lợi nhuận trong khi vẫn kiểm soát rủi ro ở mức hợp lý – thích hợp cho các kế hoạch trung đến dài hạn như tích lũy tài sản, đầu tư cho con cái hoặc mua nhà",
    profit: "8-12%/năm",
    image: "/images/gbi/investment-profit-2.png",
    risk: "Trung bình",
    ACB: {
      value: "12,234,567",
      percentage: "23%",
    },
    VIB: {
      value: "6,244,567",
      percentage: "13%",
    },
    GMD: {
      value: "4,345,567",
      percentage: "23%",
    },
    TCB: {
      value: "12,234,567",
      percentage: "23%",
    },
    MBB: {
      value: "6,244,567",
      percentage: "13%",
    },
    VPB: {
      value: "4,345,567",
      percentage: "23%",
    },
  },
  {
    title: "Mạo hiểm",
    description:
      "Danh mục Mạo Hiểm tập trung vào cổ phiếu tăng trưởng và tài sản rủi ro cao nhằm tối đa hóa lợi nhuận. Phù hợp với nhà đầu tư chấp nhận biến động lớn để theo đuổi mục tiêu tài chính dài hạn (trên 3 năm) như đầu tư đón đầu xu hướng, tạo dựng tài sản lớn hoặc tự do tài chính sớm",
    profit: "15-20%/năm",
    image: "/images/gbi/investment-profit-3.png",
    risk: "Cao",
    ACB: {
      value: "12,234,567",
      percentage: "23%",
    },
    VIB: {
      value: "6,244,567",
      percentage: "13%",
    },
    GMD: {
      value: "4,345,567",
      percentage: "23%",
    },
    TCB: {
      value: "12,234,567",
      percentage: "23%",
    },
    MBB: {
      value: "6,244,567",
      percentage: "13%",
    },
    VPB: {
      value: "4,345,567",
      percentage: "23%",
    },
  },
];

const getRiskTextColor = (text: string) => {
  const loweredCaseText = text.toLowerCase();
  switch (loweredCaseText) {
    case "thấp":
      return "#00E1FF";
    case "trung bình":
      return "#FFD201";
    case "cao":
      return "#FF2600";
    default:
      return "#00E1FF";
  }
};

const GBIInvestmentCategorySection = () => {
  const isTablet = useMediaQuery("(max-width: 1023px)");
  const isMobile = useMediaQuery("(max-width: 744px)");
  const containerRef = useRef<HTMLDivElement>(null);
  const [maxHeight, setMaxHeight] = useState(0);
  const [slideValue, setSlideValue] = useState<number[]>([20]);
  const [selectedCategory, setSelectedCategory] = useState<
    InvestmentCategory | undefined
  >(mockInvestmentCategoryData[0]);

  const handleChangeCategory = useCallback((category: InvestmentCategory) => {
    const getSlideDefaultValue = (risk: string) => {
      switch (risk.toLowerCase()) {
        case "thấp":
          return [20];
        case "trung bình":
          return [50];
        case "cao":
          return [80];
        default:
          return [20];
      }
    };
    setSelectedCategory(category);
    setSlideValue(getSlideDefaultValue(category.risk));
  }, []);

  useEffect(() => {
    if (!containerRef.current) return;
    if (isMobile) return;
    const updateHeight = () => {
      if (containerRef.current) {
        setMaxHeight(containerRef.current.offsetHeight);
      }
    };

    // Initial height
    updateHeight();

    // Create ResizeObserver to track height changes
    const observer = new ResizeObserver(() => {
      updateHeight();
    });

    observer.observe(containerRef.current);

    // Clean up observer on unmount
    return () => {
      observer.disconnect();
    };
  }, [isMobile]);

  return (
    <div data-id="gbi-investment-category-section" className="bg-canvas">
      <div className="container flex flex-col">
        <Typography
          variant={"large-title"}
          className="max-md:font-semibold max-md:text-2xl max-lg:text-[28px] text-center text-icon-emphasize"
        >
          Danh mục đầu tư
        </Typography>
        {isTablet && (
          <div className="w-full max-w-[332px] h-[56px] flex bg-[#F1F3F5] p-[1.5px] rounded-3xl gap-1 mx-auto mt-10">
            {mockInvestmentCategoryData.map((category, index) => {
              const widths = ["123px", "87px", "106px"];
              return (
                <div
                  key={index}
                  onClick={() => handleChangeCategory(category)}
                  className={`cursor-pointer h-full rounded-3xl px-4 py-3 flex items-center justify-center transition-colors ${
                    selectedCategory?.title === category.title
                      ? "bg-white text-icon-emphasize shadow-lg"
                      : "text-icon-placeholder"
                  }`}
                  style={{
                    width: widths[index] || "100px",
                  }}
                >
                  <Typography variant={"body-medium"}>
                    {category.title}
                  </Typography>
                </div>
              );
            })}
          </div>
        )}
        {isTablet && (
          <Typography
            variant={"body-regular"}
            className=" pt-10 pb-6 text-emphasize"
          >
            {selectedCategory?.description}
          </Typography>
        )}
        <div
          className={cn(
            "w-full h-full grid grid-cols-1 md:grid-cols-2 lg:flex lg:flex-row lg:justify-between gap-6",
            !isTablet && "pt-10",
          )}
        >
          {!isTablet && (
            <Accordion
              collapsible
              type="single"
              className="w-full"
              defaultValue="item 1"
            >
              {mockInvestmentCategoryData.map((item, index) => (
                <AccordionItem
                  key={index}
                  value={`item ${index + 1}`}
                  className="border-0"
                  onClick={() => handleChangeCategory(item)}
                >
                  <AccordionTrigger
                    showIcon={false}
                    arrowIcon
                    className="hover:no-underline cursor-pointer"
                  >
                    <Typography
                      variant={"title-2"}
                      className="text-icon-emphasize"
                    >
                      {item.title}
                    </Typography>
                  </AccordionTrigger>
                  <AccordionContent>
                    <Typography
                      variant={"body-regular"}
                      classname="text-emphasize"
                    >
                      {" "}
                      {item.description}
                    </Typography>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
          <div
            ref={containerRef}
            className="w-full h-full lg:h-fit bg-white flex flex-col gap-6 relative rounded-2xl overflow-hidden "
          >
            <div className="flex flex-col px-4 pt-4 gap-2 justify-between lg:justify-start h-full">
              <div className="flex flex-col gap-2">
                <Typography
                  variant={"body-medium"}
                  classname="text-default font-normal text-xl"
                >
                  Lợi nhuận dự kiến
                </Typography>
                <motion.div
                  key={selectedCategory?.title}
                  className="flex flex-col gap-2 "
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{
                    duration: 0.2,
                    ease: "easeInOut",
                  }}
                >
                  <Typography variant={"title-2"} className="text-brand">
                    {selectedCategory?.profit}
                  </Typography>
                </motion.div>
              </div>
              <Image
                src={selectedCategory?.image ?? ""}
                alt={selectedCategory?.title ?? ""}
                width={0}
                height={0}
                sizes="50vw"
                className="w-full h-auto aspect-[16/18] object-contain justify-self-center max"
              />
            </div>
            {/**overlay */}
            <div
              className="w-full h-[154px] absolute bottom-0"
              style={{
                background:
                  "linear-gradient(180deg, rgba(253, 253, 254, 0.00) 0%, #F6FAFA 100%)",
              }}
            ></div>
          </div>
          <div
            className="w-full h-full flex flex-col md:grid grid-rows-2 gap-6 overflow-hidden"
            style={{
              maxHeight: isMobile ? "none" : maxHeight,
            }}
          >
            <div className="w-full bg-white p-4 flex justify-between flex-col gap-2 rounded-2xl h-full ">
              <div className="flex flex-col gap-2">
                <Typography
                  variant={"lead"}
                  className="text-icon-default"
                  weight={"normal"}
                >
                  Mức độ rủi ro
                </Typography>
                <motion.div
                  key={selectedCategory?.title}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{
                    duration: 0.2,
                    ease: "easeInOut",
                  }}
                >
                  <Typography
                    variant={"title-2"}
                    style={{
                      color: getRiskTextColor(selectedCategory?.risk ?? ""),
                    }}
                  >
                    {selectedCategory?.risk}
                  </Typography>
                </motion.div>
              </div>
              <div className="flex flex-col gap-5">
                <Slider
                  value={slideValue}
                  max={100}
                  step={1}
                  className="w-full h-2  rounded-sm"
                  style={{
                    background:
                      "linear-gradient(90deg, #0009FF 0%, #00E1FF 20.54%, #00FF40 40.86%, #FD0 61.5%, #FF9D00 81.4%, #F00 100%)",
                  }}
                  thumbClassName="hover:ring-0 border-0 size-[30px] bg-white"
                  trackClassName="bg-transparent"
                  rangeClassName="bg-transparent"
                  rangeDots
                />
                <div className="w-full flex justify-between">
                  {mockInvestmentCategoryData.map((item, index) => (
                    <Typography
                      key={index}
                      variant="caption"
                      className="text-icon-placeholder"
                    >
                      {item.risk}
                    </Typography>
                  ))}
                </div>
              </div>
            </div>
            <div
              className={cn(
                "w-full bg-white p-4 gap-2 flex flex-col relative  rounded-2xl h-full ",
                isMobile && "max-h-[238px]",
              )}
            >
              <div
                className="w-full h-[27px] absolute bottom-0 left-0 rounded-b-2xl"
                style={{
                  background:
                    "linear-gradient(180deg, rgba(255, 255, 255, 0.00) -20.37%, #FFF 68.52%)",
                }}
              ></div>
              <Typography
                variant={"lead"}
                className="text-icon-default"
                weight={"normal"}
              >
                Danh mục đầu tư
              </Typography>
              <div className="flex flex-col overflow-auto hide-scrollbar ">
                {Object.entries(selectedCategory ?? {}).map(
                  ([key, value], index) => {
                    // Filter out non-asset fields
                    if (
                      key === "title" ||
                      key === "description" ||
                      key === "profit" ||
                      key === "image" ||
                      key === "risk"
                    ) {
                      return null;
                    }

                    // Render asset code and its data
                    const asset = value as AssetInfo;
                    return (
                      <div key={index} className="flex flex-col gap-1">
                        <div key={index} className="flex justify-between">
                          <div className="flex flex-col">
                            <Typography
                              variant="body-medium"
                              classname="text-emphasize"
                            >
                              {key}
                            </Typography>
                            <Typography
                              variant="small"
                              className="text-icon-placeholder"
                              weight={"normal"}
                            >
                              {asset.percentage}
                            </Typography>
                          </div>
                          <Typography
                            variant="body-regular"
                            classname="text-emphasize"
                          >
                            {asset.value}
                          </Typography>
                        </div>
                        {index !== [key, value].length && (
                          <Separator
                            orientation="horizontal"
                            className="bg-[#F6F7F8]"
                          />
                        )}
                      </div>
                    );
                  },
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GBIInvestmentCategorySection;

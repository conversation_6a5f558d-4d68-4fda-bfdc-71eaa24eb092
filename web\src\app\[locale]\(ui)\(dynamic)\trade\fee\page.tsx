import { TradeFeeView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Trade Fee - Kafi",
  description: "Trade Fee - Kafi",
};

export default async function TradeFeePage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <TradeFeeView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

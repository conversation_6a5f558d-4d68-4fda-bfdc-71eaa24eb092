import { IcActivity } from "#/src/components";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";

const IconPage = () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <div className="container flex flex-col gap-4 py-12">
        <IcActivity size={24} />
      </div>
    </Suspense>
  );
};

export default IconPage;

export const dynamic = "force-dynamic";

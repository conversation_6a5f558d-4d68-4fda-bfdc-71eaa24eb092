import { cn } from "#/src/lib/utils";

interface ICompassBoxProps {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
}

export const CustomIcHandCoin: React.FC<ICompassBoxProps> = ({
  color = "text-icon-placeholder",
  strokeWidth = "3",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}) => {
  return (
    <svg
      width="48"
      height="49"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "cursor-pointer transition-all duration-300",
        color,
        `hover:${hoverColor}`,
        `focus:${activeColor}`,
        className,
      )}
    >
      <path
        d="M27.059 16.7037C25.9141 17.5199 24.5131 18 23 18C19.134 18 16 14.866 16 11C16 7.13401 19.134 4 23 4C25.506 4 27.7044 5.31683 28.941 7.29628M12 40.1743H17.2206C17.9013 40.1743 18.5778 40.2553 19.2376 40.4173L24.7538 41.7578C25.9507 42.0493 27.1976 42.0777 28.4069 41.8428L34.5059 40.6562C36.1171 40.3424 37.5992 39.5709 38.7607 38.441L43.0758 34.2434C44.3081 33.0467 44.3081 31.1049 43.0758 29.9061C41.9663 28.8269 40.2095 28.7054 38.9543 29.6206L33.9252 33.2897C33.205 33.8162 32.3286 34.0997 31.4273 34.0997H26.571L29.6621 34.0995C31.4044 34.0995 32.8157 32.7267 32.8157 31.0318V30.4183C32.8157 29.011 31.8311 27.7839 30.4282 27.4437L25.6572 26.2835C24.8808 26.0952 24.0856 26 23.2863 26C21.3567 26 17.8638 27.5976 17.8638 27.5976L12 30.0498M40 13C40 16.866 36.866 20 33 20C29.134 20 26 16.866 26 13C26 9.13401 29.134 6 33 6C36.866 6 40 9.13401 40 13ZM4 29.2L4 40.8C4 41.9201 4 42.4802 4.21799 42.908C4.40973 43.2843 4.71569 43.5903 5.09202 43.782C5.51984 44 6.07989 44 7.2 44H8.8C9.9201 44 10.4802 44 10.908 43.782C11.2843 43.5903 11.5903 43.2843 11.782 42.908C12 42.4802 12 41.9201 12 40.8V29.2C12 28.0799 12 27.5198 11.782 27.092C11.5903 26.7157 11.2843 26.4097 10.908 26.218C10.4802 26 9.92011 26 8.8 26L7.2 26C6.0799 26 5.51984 26 5.09202 26.218C4.7157 26.4097 4.40973 26.7157 4.21799 27.092C4 27.5198 4 28.0799 4 29.2Z"
        stroke={"currentColor"}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

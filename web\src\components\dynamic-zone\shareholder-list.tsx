"use client";

import {
  SHAREHOLDERS_CATEGORIES,
  SHAR<PERSON>H<PERSON>DERS_DOCUMENTS,
} from "#/src/__mock__/shareholder";
import { useCallback, useEffect, useState } from "react";
import { Tabs, TabsTrigger, TabsList } from "../ui/tabs";
import { cn } from "#/src/lib";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  Accordion<PERSON>rigger,
  <PERSON>over,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
  Typography,
} from "#/src/components/ui";
import { Check, ChevronDown, X } from "lucide-react";
import {
  Command,
  CommandGroup,
  CommandItem,
} from "#/src/components/ui/command";

//--------------------------------------------------------------------------------------------------
type T_Filter = {
  category_parent: string;
  category_children: string;
  year: string[];
  page: number;
  limit: number;
};
const defaultFilters: T_Filter = {
  category_parent: SHAREHOLDERS_CATEGORIES[0]?.slug || "",
  category_children: "",
  year: [],
  page: 1,
  limit: 10,
};
//--------------------------------------------------------------------------------------------------
export default function ShareholderList() {
  const [yearOptions, setYearOptions] = useState<
    { label: string; value: string }[]
  >([]);

  const [cateChildrenOptions, setCateChildrenOptions] = useState<string[]>([]);

  const [filters, setFilters] = useState(defaultFilters);

  const [dataFilter, setDataFilter] = useState<any[]>([]);

  const handleFilters = useCallback((name: string, value: any) => {
    setFilters((prevState: any) => ({
      ...prevState,
      [name]: value,
    }));
  }, []);

  const handleClickParent = (parent: any) => {
    handleFilters("category_parent", parent?.slug);

    const checkExistChildren = parent?.children?.length > 0;
    if (checkExistChildren) {
      setCateChildrenOptions(parent?.children?.map((item: any) => item));
      handleFilters("category_children", parent?.children?.[0]?.slug || "");
    } else {
      handleFilters("category_children", "");
      setCateChildrenOptions([]);
    }
  };
  const handleClickChildren = (children: any) => {
    handleFilters("category_children", children?.slug);
  };
  const handleClickFile = () => {
    window.open(
      "https://file-examples.com/storage/fe1f31703e68825e999a328/2017/10/file-sample_150kB.pdf",
      "_blank",
    );
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // meger array category
    const newArrCategory = [
      filters?.category_parent,
      ...(cateChildrenOptions?.map((item: any) => item?.slug) || []),
    ];
    //get data by category
    const dataByCategory = SHAREHOLDERS_DOCUMENTS.filter((item) =>
      newArrCategory.includes(item?.category?.slug),
    );

    //get year
    const year = dataByCategory.map((item) => item?.issued_date?.split("/")[2]);

    const uniqueYear = [...new Set(year)];

    handleFilters("year", uniqueYear);

    const yearOptionsFormatted = uniqueYear.map((y) => ({
      label: y,
      value: y,
    }));
    setYearOptions(yearOptionsFormatted);
    // get data by year and category
    const data = dataByCategory.filter((item) => {
      const yearItem = item?.issued_date?.split("/")[2];

      return filters?.year && filters?.year?.includes(yearItem);
    });

    setDataFilter(data);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cateChildrenOptions]);

  return (
    <section>
      <div className="container">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-10">
            {/* tab parent */}
            <Tabs defaultValue={filters?.category_parent} className="">
              <TabsList className="bg-[#F1F3F5] h-max gap-2 rounded-[24px] overflow-hidden">
                {SHAREHOLDERS_CATEGORIES?.map((category: any, idx: number) => {
                  const isActive = filters?.category_parent === category?.slug;
                  return (
                    <TabsTrigger
                      key={idx}
                      value={category?.slug}
                      onClick={() => handleClickParent(category)}
                      className={cn(
                        "p-1 h-full rounded-[24px]  data-[state=active]:shadow-none ring-0",
                        isActive ? "bg-[#F6F7F8]" : "bg-transparent",
                      )}
                    >
                      <div
                        className={cn(
                          "px-6 py-3.5 rounded-[24px]",
                          isActive
                            ? "bg-white shadow-[0px_12px_16px_-4px_#1018281A]"
                            : "bg-transparent",
                        )}
                      >
                        <Typography
                          variant="body-regular"
                          classname={cn(
                            "text-gray-900",
                            isActive ? "font-medium" : "font-normal",
                          )}
                        >
                          {category?.title}
                        </Typography>
                      </div>
                    </TabsTrigger>
                  );
                })}
              </TabsList>
            </Tabs>
            <MultiSelectYear
              options={yearOptions}
              filters={filters}
              onFilters={handleFilters}
            />
          </div>

          <div className="flex flex-col gap-6 p-6 bg-white rounded-2xl">
            {/* tab children */}
            {cateChildrenOptions?.length > 0 && (
              <Tabs defaultValue={filters?.category_children}>
                <TabsList className=" h-max gap-6 bg-transparent">
                  {cateChildrenOptions?.map((child: any, idx: number) => {
                    const isActive = filters?.category_children === child?.slug;
                    return (
                      <TabsTrigger
                        key={idx}
                        value={child?.slug}
                        onClick={() => handleClickChildren(child)}
                        className={cn(
                          "py-3 px-0 h-full relative before:absolute before:rounded-full before:bottom-0  before:content-[''] before:w-0 before:h-[4px] before:bg-brand before:left-0  data-[state=active]:shadow-none ring-0 data-[state=active]:before:w-[40px] transition-all duration-300",
                        )}
                      >
                        <Typography
                          variant="headline"
                          classname={cn(
                            "font-semibold",
                            isActive ? " text-brand" : " text-emphasize",
                          )}
                        >
                          {child?.title}
                        </Typography>
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
              </Tabs>
            )}
            <Accordion type="single" collapsible>
              {dataFilter?.length > 0 &&
                dataFilter?.map((item: any, index: number) => {
                  const isHaveMultiFile =
                    item?.files?.length > 1 && item?.title;
                  return (
                    <AccordionItem
                      key={index}
                      value={`item-${index}`}
                      className="py-6 border-[#E0E2E3]"
                    >
                      <AccordionTrigger className="p-0 [&>*:last-child]:hidden hover:no-underline">
                        <div className="w-full flex gap-6">
                          <Typography
                            variant="body-regular"
                            classname="text-emphasize w-[100px]"
                          >
                            {item?.issued_date}
                          </Typography>
                          {isHaveMultiFile ? (
                            <div className="w-full flex justify-between gap-10 ">
                              <Typography variant="headline">
                                {item?.title}
                              </Typography>
                              <ChevronDown className="group-data-[state=open]:rotate-180" />
                            </div>
                          ) : (
                            <Typography
                              variant="headline"
                              onClick={handleClickFile}
                            >
                              {item?.files?.[0]?.title}
                            </Typography>
                          )}
                        </div>
                      </AccordionTrigger>
                      {isHaveMultiFile ? (
                        <AccordionContent className="pb-0 pl-0 pt-4 pr-12">
                          <div className="w-full flex gap-6">
                            <div className="w-[100px]"></div>
                            <div className="w-full flex flex-col">
                              {item?.files?.map((file: any, index: number) => {
                                return (
                                  <div
                                    key={index}
                                    className="py-3 cursor-pointer"
                                    onClick={handleClickFile}
                                  >
                                    <Typography
                                      variant="body-medium"
                                      classname="text-emphasize"
                                    >
                                      {file?.title}
                                    </Typography>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </AccordionContent>
                      ) : null}
                    </AccordionItem>
                  );
                })}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  );
}

//--------------------------------------------------------------------------------------------------
type MultiSelectYearProps = {
  options: { label: string; value: string }[];
  filters: any;
  onFilters: (name: string, value: any) => void;
};
function MultiSelectYear({
  options,
  filters,
  onFilters,
}: MultiSelectYearProps) {
  const [open, setOpen] = useState(false);

  const toggleValue = (value: string) => {
    const checkExist = filters?.year?.includes(value);

    if (checkExist) {
      const newYear =
        filters?.year?.filter((item: any) => item !== value) || [];
      onFilters("year", newYear);
    } else {
      const newYear = [...(filters?.year || []), value];
      onFilters("year", newYear);
    }
  };
  const toggleAll = () => {
    const checkAll = filters?.year?.length === options?.length;

    if (checkAll) {
      onFilters("year", []);
    } else {
      onFilters(
        "year",
        options?.map((item) => item.value),
      );
    }
  };
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            "flex items-center  py-2 px-4 min-w-[200px] max-w-max justify-between rounded-xl bg-white cursor-pointer border ",
            open ? "border-brand" : "border-[#CBCDCE]",
          )}
        >
          <div className="flex flex-col items-start w-full">
            <Typography variant="caption-regular">Năm</Typography>
            <Typography variant="input-medium">
              {filters?.year?.length > 0
                ? filters?.year?.join("-")
                : "Select options"}
            </Typography>
          </div>
          {open ? (
            <X className="ml-2 h-5 w-5 text-placeholder" />
          ) : (
            <ChevronDown className="ml-2 h-5 w-5 text-placeholder" />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command className="bg-white">
          <CommandGroup className="p-0">
            <CommandItem
              onSelect={toggleAll}
              className={cn("my-0.5 px-3 py-2 data-[selected=true]:bg-canvas")}
            >
              <Typography
                variant="input-medium"
                classname={cn(
                  "text-emphasize",
                  filters?.year?.length === options?.length && "text-brand",
                )}
              >
                Chọn tất cả
              </Typography>
            </CommandItem>
            {options?.map((item) => (
              <CommandItem
                key={item?.value}
                onSelect={() => toggleValue(item?.value)}
                className={cn(
                  "my-0.5 px-3 py-2 data-[selected=true]:bg-canvas",
                  filters?.year?.includes(item?.value) && "bg-canvas",
                )}
              >
                <Typography variant="input-medium" classname="text-emphasize">
                  {item?.label}
                </Typography>

                <Check
                  className={cn(
                    "ml-auto h-4 w-4 text-brand",
                    filters?.year?.includes(item?.value)
                      ? "opacity-100"
                      : "opacity-0",
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

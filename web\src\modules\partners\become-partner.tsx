"use client";
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui";
import { ArrowLeft, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const stepsData = [
  {
    id: 1,
    title: "Đăng ký tham gia",
    description:
      "Tải app và tạo tài khoản Kafi để trở thành thành viên Kafi Partners – dễ dàng và hoàn toàn miễn phí",
    imageUrl: "/images/partners/join-kafi-partners.png",
    altText: "Join Kafi Partners successfully",
  },
  {
    id: 2,
    title: "Giới thiệu bạn bè",
    description:
      "Vào mục Partners để lấy mã giới thiệu. G<PERSON>i mã này cho bạn bè để bắt đầu nhận K-Points",
    imageUrl: "/images/partners/referral-code.png",
    altText: "Popup with sales code and trade link for referral",
  },
  {
    id: 3,
    title: "<PERSON> dõi K-Points",
    description:
      "K-<PERSON> được cập nhật liên tục, giúp dễ dàng theo dõi và đưa ra chiến lược chăm sóc phù hợp",
    imageUrl: "/images/partners/white.png",
    altText: "A graph of K-Points and activity list",
  },
];

export default function BecomePartner() {
  // State for carousel API - following icon-boxes-section pattern
  const [api, setApi] = useState<any>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  const scrollPrev = () => api?.scrollPrev();
  const scrollNext = () => api?.scrollNext();

  // Handle carousel state changes
  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    onSelect();
    api.on("select", onSelect);
    api.on("reInit", onSelect);

    return () => {
      api?.off("select", onSelect);
    };
  }, [api]);

  const renderStepCard = (step: (typeof stepsData)[0]) => (
    <div className="relative bg-white rounded-xl overflow-hidden p-6 md:p-8 !pb-0 flex flex-col gap-6 text-center h-full">
      {/* Content section - takes available space */}
      <div className="space-y-4 flex-shrink-0">
        <Typography variant="title-2" className="font-semibold text-left">
          {step.id}. <span>{step.title}</span>
        </Typography>
        <Typography>{step.description}</Typography>
      </div>

      {/* Image section - grows to fill remaining space and pushes to bottom */}
      <div className="mt-auto flex items-end justify-center relative h-[240px] lg:h-[320px] overflow-hidden">
        <Image
          alt={step.altText}
          src={step.imageUrl}
          className="w-full h-full object-contain object-bottom"
          height={400}
          width={400}
          loading="lazy"
        />
      </div>
      {/* Gradient overlay */}
      <div
        className="absolute bottom-0 left-0 right-0 h-[100px] w-full pointer-events-none z-10"
        style={{
          background:
            "linear-gradient(180deg, rgba(250, 250, 251, 0) 18.83%, #FAFAFB 83.77%)",
        }}
      ></div>
    </div>
  );

  return (
    <section className="container">
      <div className="content space-y-10">
        <div className="flex flex-col gap-4 items-center justify-center">
          <Typography
            variant="large-title"
            className="text-center max-md:text-2xl max-lg:text-3xl"
          >
            Cách tham gia Kafi Partners
          </Typography>
          <Link href={"#"}>
            <Button>Đăng ký ngay</Button>
          </Link>
        </div>

        {/* Desktop: Grid layout */}
        <div className="hidden lg:grid lg:grid-cols-3 gap-8 mx-auto items-stretch">
          {stepsData.map((step) => (
            <div key={step.id} className="flex">
              {renderStepCard(step)}
            </div>
          ))}
        </div>

        {/* Tablet & Mobile: Carousel */}
        <div className="lg:hidden">
          <Carousel
            setApi={setApi}
            opts={{
              align: "start",
              loop: false,
            }}
            className="w-full"
          >
            <CarouselContent className="gap-4 items-stretch">
              {stepsData.map((step, idx) => (
                <CarouselItem key={idx} className="basis-1/1 md:basis-1/2 flex">
                  {renderStepCard(step)}
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>

          {/* Mobile: Show pagination arrows - following icon-boxes-section pattern */}
          <div className="lg:hidden w-full flex items-center justify-end gap-4 mt-6">
            <Button
              variant={"ghost"}
              size={"icon"}
              onClick={scrollPrev}
              disabled={!canScrollPrev}
              className="bg-white hover:bg-black hover:text-white rounded-full w-12 h-12"
            >
              <ArrowLeft className="size-6" />
            </Button>
            <Button
              variant={"ghost"}
              size={"icon"}
              onClick={scrollNext}
              disabled={!canScrollNext}
              className="bg-white hover:bg-black hover:text-white rounded-full w-12 h-12"
            >
              <ArrowRight className="size-6" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

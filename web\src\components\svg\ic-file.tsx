import SvgIcon from "./icon";

const baseSize = 24;

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
};

const IcFile = ({
  size = 48,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  strokeWidth = "2",
  className,
}: TProps) => {
  const _pathScale = Number(size) / baseSize;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M21.75 2.53906V10.8001C21.75 11.9202 21.75 12.4803 21.968 12.9081C22.1597 13.2844 22.4657 13.5904 22.842 13.7822C23.2698 14.0001 23.8299 14.0001 24.95 14.0001H33.2111M21.75 32H9.75M25.75 24H9.75M33.75 17.9764V32.4C33.75 35.7603 33.75 37.4405 33.096 38.7239C32.5208 39.8529 31.6029 40.7708 30.4739 41.346C29.1905 42 27.5103 42 24.15 42H11.35C7.98969 42 6.30953 42 5.02606 41.346C3.89708 40.7708 2.9792 39.8529 2.40396 38.7239C1.75 37.4405 1.75 35.7603 1.75 32.4V11.6C1.75 8.23969 1.75 6.55953 2.40396 5.27606C2.9792 4.14708 3.89708 3.2292 5.02606 2.65396C6.30953 2 7.98969 2 11.35 2H17.7736C19.2411 2 19.9749 2 20.6654 2.16578C21.2776 2.31276 21.8629 2.55519 22.3997 2.88416C23.0052 3.25521 23.5241 3.77406 24.5618 4.81177L30.9382 11.1882C31.9759 12.2259 32.4948 12.7448 32.8658 13.3503C33.1948 13.8871 33.4372 14.4724 33.5842 15.0846C33.75 15.7751 33.75 16.5089 33.75 17.9764Z"
        stroke={"currentColor"}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{ transform: `scale(${_pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcFile;

{"kind": "collectionType", "collectionName": "research_categories", "info": {"singularName": "research-category", "pluralName": "research-categories", "displayName": "Research - Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title", "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "research": {"type": "relation", "relation": "oneToMany", "target": "api::research.research", "mappedBy": "research_category"}}}
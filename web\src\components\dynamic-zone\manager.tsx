"use client";

import React from "react";

import { PageHeroSection, ToggleCardsSection } from ".";

interface DynamicZoneComponent {
  __component: string;
  id: number;
  [key: string]: any;
}

interface Props {
  dynamicZone: DynamicZoneComponent[];
  locale: string;
  banner_ads?: any;
}

const componentMapping: { [key: string]: any } = {
  //global
  "dynamic-zone.page-hero-section": PageHeroSection,
  // Pages
  "dynamic-zone.toggle-cards": ToggleCardsSection,
  // News & Events & Notifications
};

const convertToDynamicZoneTogetherZone = (dynamicZone: any[]): any[] => {
  const processComponents = (
    startIndex: number,
    parentId?: string,
  ): [any[], number] => {
    const result = [];
    let i = startIndex;
    while (i < dynamicZone.length) {
      const current = dynamicZone[i];

      // Skip components that belong to a different parent
      if (current.parentId && !parentId) {
        i++;
        continue;
      }

      if (current.__component === "dynamic-zone.html") {
        if (current.is_open === true) {
          // Process nested components recursively
          const [children, nextIndex] = processComponents(i + 1, current.id);

          const newZone = {
            __component: "dynamic-zone.html",
            id: current.id,
            open: true,
            content: current.content,
            children: children,
          };
          result.push(newZone);
          i = nextIndex;
        } else if (current.is_open === false && parentId) {
          // End of nested section
          return [result, i + 1];
        } else {
          result.push(current);
          i++;
        }
      } else {
        result.push({
          ...current,
          parentId: parentId || current.parentId,
        });
        i++;
      }
    }

    return [result, i];
  };

  const [processedZones] = processComponents(0);
  return processedZones;
};

const DynamicZoneManager: React.FC<Props> = ({
  dynamicZone,
  locale,
  banner_ads,
}) => {
  const newDynamicZone = convertToDynamicZoneTogetherZone(dynamicZone);

  const renderComponent = (componentData: any, index: number) => {
    try {
      const Component = componentMapping[componentData?.__component];

      if (!Component) {
        console.warn(`No component found for: ${componentData.__component}`);
        return null;
      }

      return (
        <Component
          key={index}
          {...componentData}
          locale={locale}
          banner_ads={banner_ads}
        />
      );
    } catch (error) {
      console.error(
        `Error rendering component: ${componentData.__component}`,
        error,
      );
      return null;
    }
  };

  return newDynamicZone.map(renderComponent);
};

export default DynamicZoneManager;

// #########################################################

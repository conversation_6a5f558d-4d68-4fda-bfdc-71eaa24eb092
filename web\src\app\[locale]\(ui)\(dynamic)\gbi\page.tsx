import { GBIView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "GBI - Kafi",
  description: "GBI - Kafi",
};

export default async function GBIPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <GBIView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

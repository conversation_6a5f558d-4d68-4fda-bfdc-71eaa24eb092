type LineArrowNarrowRightIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const LineArrowNarrowRightIcon = ({
  size = 16,
  className,
  color = "white",
}: LineArrowNarrowRightIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.6665 8H13.3332M13.3332 8L9.33317 4M13.3332 8L9.33317 12"
        stroke={color}
        className={className}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
};

const IcActivity = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}: TProps) => {
  const pathScale = Number(size) / 24;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M22 12H18L15 21L9 3L6 12H2"
        stroke={"currentColor"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcActivity;

// Usage:
// <IcActivity size={24} hoverColor="text-brand" activeColor="text-brand" />

"use client";
import { Button } from "#/src/components/ui";
import { cn } from "#/src/lib";
import { TNewsEvents, TNewsEventsCategory } from "#/src/types/news-event";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import NewsEventsCard from "../../news/news-events-card";

type TProps = {
  data: TNewsEvents[];
  categories: TNewsEventsCategory[];
  // pagination: TPagination;
  classname?: string;
};
const getActiveClass = (current: string | null, value: string) => {
  if (value == "all" && !current) return "!bg-brand !text-white";
  return current === value
    ? "!bg-brand !text-white"
    : "!bg-screen !text-default";
};
export default function KnowledgeListPagination({
  data,
  categories,
  classname,
}: TProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const categoryParam = searchParams.get("q");

  const handleCategory = (category: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("q", category);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };
  const renderCategory = (
    <div className="flex items-start gap-4 w-[calc(100%+((100vw-100%)/2))] md:w-fit md:mx-auto overflow-x-auto hide-scrollbar">
      <Button
        variant="outline"
        className={cn(
          "rounded-lg px-4 py-2 !border-0 !shadow-none",
          getActiveClass(categoryParam, "all"),
        )}
        onClick={() => handleCategory("")}
      >
        Tất cả
      </Button>
      {categories.map((category) => (
        <Button
          key={category.id}
          variant="outline"
          className={cn(
            "rounded-lg px-4 py-2 !border-0 !shadow-none",
            getActiveClass(categoryParam, category.slug),
          )}
          onClick={() => handleCategory(category.slug)}
        >
          {category.name}
        </Button>
      ))}
    </div>
  );

  const renderListPosts = (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
        classname,
      )}
    >
      {data.map((item) => (
        <NewsEventsCard
          key={item.id}
          item={item}
          hideDescription={true}
          handle="/knowledge"
        />
      ))}
    </div>
  );
  return (
    <div className="flex flex-col gap-12">
      {renderCategory}
      {renderListPosts}
    </div>
  );
}

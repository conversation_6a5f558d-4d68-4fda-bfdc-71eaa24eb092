type LinkedinIconProps = {
  size?: number;
  className?: string;
};

export const LinkedinIcon = ({ size = 24, className }: LinkedinIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_3382_13287)">
        <path
          d="M7.63269 19V8.87953H4.20239V19H7.63269ZM5.91798 7.49692C7.11419 7.49692 7.85877 6.71978 7.85877 5.74862C7.83648 4.75556 7.11423 4 5.94068 4C4.76731 4 4 4.75558 4 5.74862C4 6.71983 4.7444 7.49692 5.89559 7.49692H5.91787H5.91798ZM9.53135 19H12.9617V13.3482C12.9617 13.0458 12.9839 12.7436 13.0745 12.5274C13.3225 11.923 13.8869 11.2971 14.8345 11.2971C16.0758 11.2971 16.5724 12.2252 16.5724 13.5857V18.9999H20.0025V13.1969C20.0025 10.0883 18.3101 8.64193 16.0532 8.64193C14.2027 8.64193 13.3902 9.65626 12.9388 10.3471H12.9617V8.87932H9.53143C9.57644 9.82897 9.53143 18.9998 9.53143 18.9998L9.53135 19Z"
          fill="#767C82"
        />
      </g>
      <defs>
        <clipPath id="clip0_3382_13287">
          <rect
            width="16"
            height="15"
            fill="white"
            transform="translate(4 4)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

"use client";

import * as SliderPrimitive from "@radix-ui/react-slider";
import * as React from "react";
import { cn } from "../../lib";

function Slider({
  className,
  defaultValue,
  value,
  min = 0,
  max = 100,
  thumbClassName = "",
  rangeClassName,
  trackClassName,
  rangeDots,
  ...props
}: React.ComponentProps<typeof SliderPrimitive.Root> & {
  thumbClassName?: string;
  rangeClassName?: string;
  trackClassName?: string;
  rangeDots?: boolean;
}) {
  const values = React.useMemo(
    () =>
      Array.isArray(value)
        ? value
        : Array.isArray(defaultValue)
          ? defaultValue
          : [min, max],
    [value, defaultValue, min, max],
  );

  return (
    <SliderPrimitive.Root
      data-slot="slider"
      defaultValue={defaultValue}
      value={value}
      min={min}
      max={max}
      className={cn(
        "relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",
        className,
      )}
      {...props}
    >
      {rangeDots && (
        <div className="absolute w-full h-full top-0 lef-0 ">
          <div className="flex justify-between items-center mx-auto w-full max-w-[200px] h-full">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="w-1 h-1 rounded-full bg-white/50" />
            ))}
          </div>
        </div>
      )}
      <SliderPrimitive.Track
        data-slot="slider-track"
        className={cn(
          "relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5 bg-[#E0E2E3]",
          trackClassName,
        )}
      >
        <SliderPrimitive.Range
          data-slot="slider-range"
          className={cn(
            "absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full bg-primary",
            rangeClassName,
          )}
        />
      </SliderPrimitive.Track>
      {Array.from({ length: values.length }, (_, index) => (
        <SliderPrimitive.Thumb
          data-slot="slider-thumb"
          key={index}
          className={cn(
            "border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50",
            thumbClassName,
          )}
          style={{
            filter: "drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.10))",
            backdropFilter: "blur(40px)",
          }}
        />
      ))}
    </SliderPrimitive.Root>
  );
}

export { Slider };

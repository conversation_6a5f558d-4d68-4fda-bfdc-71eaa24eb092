type LineMarkerPinIconProps = {
  size?: number;
  className?: string;
};

export const LineMarkerPinIcon = ({
  size = 32,
  className,
}: LineMarkerPinIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.0002 17.3334C18.2093 17.3334 20.0002 15.5426 20.0002 13.3334C20.0002 11.1243 18.2093 9.33341 16.0002 9.33341C13.791 9.33341 12.0002 11.1243 12.0002 13.3334C12.0002 15.5426 13.791 17.3334 16.0002 17.3334Z"
        stroke="black"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.0002 29.3334C21.3335 24.0001 26.6668 19.2245 26.6668 13.3334C26.6668 7.44238 21.8912 2.66675 16.0002 2.66675C10.1091 2.66675 5.3335 7.44238 5.3335 13.3334C5.3335 19.2245 10.6668 24.0001 16.0002 29.3334Z"
        stroke="black"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

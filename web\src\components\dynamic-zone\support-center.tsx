import { FAQS_DATA } from "#/src/__mock__/faqs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Typography,
} from "#/src/components/ui";
//-----------------------------------------------------------
export default function SupportCenter() {
  return (
    <section>
      <div className="container max-w-[800px]">
        <div className="flex flex-col gap-20">
          {FAQS_DATA?.length > 0 &&
            FAQS_DATA?.map((item, index) => (
              <FaqItem key={index} item={item} />
            ))}
        </div>
      </div>
    </section>
  );
}
//-----------------------------------------------------------
const FaqItem = ({ item }: { item: any }) => {
  return (
    <div className="flex flex-col gap-10">
      <Typography variant="large-title">{item?.title}</Typography>
      <Accordion type="single" collapsible>
        {item?.items?.length > 0 &&
          item?.items?.map((item: any, index: number) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="py-6 border-[#E0E2E3]"
            >
              <AccordionTrigger className="p-0 [&_svg]:text-[#767C82] hover:no-underline">
                <Typography variant="title-2">{item?.question}</Typography>
              </AccordionTrigger>
              <AccordionContent className="pb-0 pl-0 pt-4 pr-12">
                <Typography variant="body-regular">{item?.answer}</Typography>
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    </div>
  );
};

"use client";
import Image from "next/image";
import AnalysisCenter from "./components/analysis-center";
import MarketPerspective from "./components/market-perspective";
import Podcast from "./components/podcast";
import { useSearchStore } from "#/src/store/use-open-search-store";

export default function ResearchesView() {
  const { setOpenSearch } = useSearchStore();

  return (
    <div className="flex flex-col gap-10" onClick={() => setOpenSearch(false)}>
      <Image
        src={"/images/research/research-banner.png"}
        alt="research banner"
        width={0}
        height={0}
        className="w-0 md:w-full h-auto"
        sizes="(max-width: 768px) 100vw, (min-width: 1024px) 80vw"
      />
      <AnalysisCenter />
      <MarketPerspective />
      <Podcast />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

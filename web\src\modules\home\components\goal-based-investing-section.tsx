"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Accordion,
  AccordionContent,
  AccordionI<PERSON>,
  AccordionTrigger,
  <PERSON><PERSON>,
} from "@components/ui";
import { cn } from "@/lib/utils";
import {
  LineArrowUpIcon,
  LineBankNoteIcon,
  LineBarChartSquareIcon,
  LineLayersThreeIcon,
} from "@components/svg";

import { useIsMobile, useMediaQuery } from "@/hooks";
import Image from "next/image";
import { useState } from "react";
import {
  TAB_CONFIG,
  GOAL_BASED_INVESTING_DESKTOP_DATA,
  GOAL_BASED_INVESTING_MOBILE_DATA,
} from "@/__mock__/home";

// Helper function to render icons
const _renderIcon = (
  iconName: string,
  size: number = 48,
  isActive: boolean = false,
) => {
  const iconProps = {
    size,
    className: isActive ? "stroke-brand" : "stroke-[#808080]",
  };

  switch (iconName) {
    case "LineArrowUpIcon":
      return <LineArrowUpIcon {...iconProps} />;
    case "LineLayersThreeIcon":
      return <LineLayersThreeIcon {...iconProps} />;
    case "LineBarChartSquareIcon":
      return <LineBarChartSquareIcon {...iconProps} />;
    case "LineBankNoteIcon":
      return <LineBankNoteIcon {...iconProps} />;
    default:
      return null;
  }
};

// Dynamic Content Renderer based on content type
function _DynamicTabContent({ data }: { data: any }) {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [activeAccordion, setActiveAccordion] = useState<string>("item-0");

  // Benefits content (with accordions)
  if (data.contentType === "benefits") {
    return (
      <div className="space-y-8">
        {/* Accordions */}
        <Accordion
          type="single"
          collapsible
          className="space-y-[26px]"
          value={activeAccordion}
          onValueChange={setActiveAccordion}
        >
          {data.accordions?.map((item: any, index: number) => {
            const itemValue = `item-${index}`;
            const isActive = activeAccordion === itemValue;

            return (
              <AccordionItem
                key={index}
                value={itemValue}
                className="bg-white/90 backdrop-blur-sm border-none outline0none rounded-lg"
              >
                <div
                  className={cn(
                    "flex gap-4",
                    isActive ? "items-start" : "items-start lg:items-center",
                  )}
                >
                  <div className="shrink-0">
                    {_renderIcon(item.icon, 48, isActive)}
                  </div>

                  {/* Content Column - Flexible width */}
                  <div className="flex-1 flex flex-col gap-[10px]">
                    <AccordionTrigger
                      className={cn(
                        "hover:no-underline py-0 w-full items-start justify-between",
                        isActive ? "" : "text-[#808080]",
                      )}
                    >
                      <Typography
                        variant={isActive ? "title-3" : "body"}
                        classname="text-wrap"
                      >
                        {item.title}
                      </Typography>
                    </AccordionTrigger>
                    <AccordionContent className="md:pr-12">
                      <Typography>{item.content}</Typography>
                    </AccordionContent>
                  </div>
                </div>
              </AccordionItem>
            );
          })}
        </Accordion>

        {/* Buttons */}
        <div className="flex gap-6 items-center lg:justify-start justify-center">
          {data.buttons?.map((button: any, index: number) => (
            <Button key={index} variant={button.variant}>
              {button.text}
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Profitability content (investment details)
  if (
    data.contentType === "profitability" ||
    data.contentType === "safety" ||
    data.contentType === "risk"
  ) {
    return (
      <div className="space-y-3">
        {/* Investment Details */}
        <div className="bg-white/90 backdrop-blur-sm rounded-xl">
          <Typography variant="subheadline-medium" className="font-medium">
            {data.content?.subtitle}
          </Typography>
          <div className="text-[34px] text-brand font-extrabold mb-4">
            {data.content?.amount}
          </div>
          <div
            className="py-2 px-4 text-sm rounded-lg border-dashed border-brand border"
            dangerouslySetInnerHTML={{ __html: data.content?.targetRate }}
          />
          <div className="grid grid-cols-2 gap-4 text-sm mt-4 w-sm">
            <div>
              <div className="text-gray-600">Thời gian</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.period}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Đầu tư ban đầu</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.initialInvestment}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Lợi nhuận mục tiêu</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.returnRate}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Tiền nộp hàng tháng</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.monthlyContribution}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Mức rủi ro</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.riskLevel}
              </div>
            </div>
            <div>
              <div className="text-gray-600">Giá trị đã tham gia</div>
              <div className="font-medium text-[18px] mt-2 text-gray-900">
                {data.content?.participationFee}
              </div>
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-4">{data.content?.note}</div>
        </div>

        {/* Buttons */}
        <div className="flex mt-3">
          {data.buttons?.map((button: any, index: number) => (
            <Button key={index} variant={button.variant}>
              {button.text}
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Safety and Risk content (features list)
  // if (data.contentType === "safety" || data.contentType === "risk") {
  //   return (
  //     <div className="text-gray-500 text-sm space-y-8">
  //       <Typography
  //         variant="subheadline-medium"
  //         className="font-medium text-emphasize"
  //       >
  //         {data?.title}
  //       </Typography>
  //       No content
  //     </div>
  //   );
  // }

  // Fallback
  return null;
}

export default function GoalBasedInvestingSection() {
  const [activeTab, setActiveTab] = useState("loi-ich");
  const isBelowLg = useMediaQuery("(max-width: 1023px)");
  const isMobile = useIsMobile();
  // Use different data based on device type
  const dataSource = isBelowLg
    ? GOAL_BASED_INVESTING_MOBILE_DATA
    : GOAL_BASED_INVESTING_DESKTOP_DATA;

  const currentData = dataSource[activeTab as keyof typeof dataSource];

  // Desktop render (1024px and above)
  const renderDesktop = () => (
    <div className="w-full bg-white rounded-[20px] relative overflow-hidden content">
      <div className="flex relative z-10 flex-row gap-8 items-start justify-between px-[97px] py-12 pb-24">
        {/* Left Content Area */}
        <div className="h-auto xl:h-[520px] max-w-md 2xl:max-w-[500px] space-y-8 shrink-0">
          <Typography
            variant="large-title"
            classname="text-black max-md:text-2xl max-lg:text-3xl"
          >
            {currentData.title}
          </Typography>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            {TAB_CONFIG.map((tab) => (
              <TabsContent key={tab.key} value={tab.key} className="mt-0">
                <_DynamicTabContent data={currentData} />
              </TabsContent>
            ))}
          </Tabs>
        </div>

        {/* Right Tab Navigation */}
        <div className="flex-1">
          <div className="max-w-[437px] w-full ml-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="h-[50px] bg-[#F1F3F5] rounded-[25px] p-1 w-full flex justify-between">
                {TAB_CONFIG.map((tab) => (
                  <TabsTrigger
                    key={tab.key}
                    value={tab.key}
                    className="flex-1 h-full rounded-[25px] text-base font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-[0px_4px_6px_-2px_rgba(16,24,40,0.05),0px_12px_16px_-4px_rgba(16,24,40,0.1)] data-[state=active]:text-gray-900 data-[state=inactive]:bg-transparent data-[state=inactive]:text-gray-900 data-[state=inactive]:shadow-none cursor-pointer min-w-[100px]"
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Background Image */}
      <div className="absolute z-0 left-0 right-0 bottom-0 overflow-hidden">
        <Image
          src={currentData.image}
          alt=""
          width={800}
          height={500}
          className="w-full h-full object-contain transition-opacity duration-300"
        />
      </div>
    </div>
  );

  // Mobile/Tablet render (below 744px)
  const renderMobile = () => (
    <div className="w-full bg-white relative overflow-hidden min-h-[660px] md:rounded-[20px]">
      <div className="relative z-10 py-8 flex flex-col items-center">
        <div className="w-full">
          {/* Title at top */}
          <div className="text-2xl md:text-[28px] font-bold text-gray-900 mb-8 text-center">
            {currentData.title}
          </div>
          {/* Tab Navigation */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="overflow-auto whitespace-nowrap w-full hide-scrollbar">
              <TabsList className="mx-4 md:mx-auto h-[50px] rounded-[25px] p-1 w-max flex gap-1 bg-[#F1F3F5]">
                {TAB_CONFIG.map((tab) => (
                  <TabsTrigger
                    key={tab.key}
                    value={tab.key}
                    className="h-full rounded-[25px] text-base font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-[0px_4px_6px_-2px_rgba(16,24,40,0.05),0px_12px_16px_-4px_rgba(16,24,40,0.1)] data-[state=active]:text-gray-900 data-[state=inactive]:bg-transparent data-[state=inactive]:text-gray-900 data-[state=inactive]:shadow-none cursor-pointer min-w-[100px] whitespace-nowrap px-4"
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
          </Tabs>
        </div>

        {/* Background Image */}
        <div className="relative w-full overflow-hidden my-7">
          <Image
            src={currentData.image}
            alt=""
            width={600}
            height={300}
            className="w-full h-full object-contain transition-opacity duration-300 "
          />
        </div>

        {/* Content Area */}
        <div className="w-full px-6 max-w-md mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            {TAB_CONFIG.map((tab) => (
              <TabsContent key={tab.key} value={tab.key} className="mt-0">
                <_DynamicTabContent data={currentData} />
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </div>
  );

  return (
    <div className={isMobile ? "" : "container"}>
      {isBelowLg ? renderMobile() : renderDesktop()}
    </div>
  );
}

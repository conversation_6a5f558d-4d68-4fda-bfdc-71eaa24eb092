import { Locale, format, formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

const DATE_FORMAT = "dd/MM/yyyy";
const DATE_TIME_FORMAT = "dd/MM/yyyy HH:mm";
const DATE_TIME_FORMAT_WITH_SECONDS = "dd/MM/yyyy HH:mm:ss";

export const fDate = (
  date: string,
  formatString: string = DATE_FORMAT,
  locale: Locale = vi,
) => {
  if (!date) return "";
  return format(new Date(date), formatString, { locale });
};

export const fDateTime = (
  date: string,
  formatString: string = DATE_TIME_FORMAT,
  locale: Locale = vi,
) => {
  if (!date) return "";
  return format(new Date(date), formatString, { locale });
};

export const fDateTimeWithSeconds = (
  date: string,
  formatString: string = DATE_TIME_FORMAT_WITH_SECONDS,
  locale: Locale = vi,
) => {
  if (!date) return "";
  return format(new Date(date), formatString, { locale });
};

export const upToNow = (date: string) => {
  if (!date) return "";
  return formatDistanceToNow(new Date(date), { locale: vi });
};

export const fromNow = (date: string) => {
  if (!date) return "";
  return formatDistanceToNow(new Date(date), { locale: vi });
};

export const formatIsoString = (date: Date, addDays: number = 0) => {
  if (!date) return "";
  return new Date(date.getTime() + addDays).toISOString();
};

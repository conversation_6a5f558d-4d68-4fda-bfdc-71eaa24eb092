import Image from "next/image";
import { Typography } from "#/src/components/ui";
import Link from "next/link";
//-----------------------------------------------------------
type IProps = {
  promotion: any;
};
//-----------------------------------------------------------
export default function PromotionCard({ promotion }: IProps) {
  const { thumbnail, title, slug, category, created_at } = promotion || {};
  return (
    <Link href={`/promotions/${slug}`}>
      <div className="flex flex-col rounded-2xl overflow-hidden bg-white h-full">
        <Image
          src={thumbnail?.url}
          alt={promotion?.title}
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto aspect-[9/6] object-cover"
        />
        <div className="w-full p-6 flex flex-col gap-6">
          <div className="w-full flex flex-col gap-2">
            <Typography variant="body-bold" classname="text-placeholder">
              {category?.title}
            </Typography>
            <Typography variant="title-3" classname="text-emphasize">
              {title}
            </Typography>
          </div>
          <Typography variant="body-bold" classname="text-placeholder">
            {created_at}
          </Typography>
        </div>
      </div>
    </Link>
  );
}

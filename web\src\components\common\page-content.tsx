"use client";

// import { AmbientColor } from '@/components/decorations/ambient-color'
import { DynamicZoneManager } from "@/components/dynamic-zone";
import { SmootherContainer } from "../animates";

type TProps = {
  pageData: any;
};

export default function PageContent({ pageData }: TProps) {
  const dynamicZone = pageData?.dynamic_zone;

  const notFound = !pageData && !dynamicZone?.length;

  return (
    <SmootherContainer>
      <div className="relative overflow-hidden w-full">
        {dynamicZone && (
          <DynamicZoneManager
            dynamicZone={dynamicZone}
            locale={pageData.locale}
          />
        )}

        {notFound && <div>Content not found</div>}
      </div>
    </SmootherContainer>
  );
}

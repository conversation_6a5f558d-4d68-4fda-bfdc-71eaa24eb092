{"kind": "collectionType", "collectionName": "jobs", "info": {"singularName": "job", "pluralName": "jobs", "displayName": "Jobs"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "requirements": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "benefits": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "block": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "enum": ["Business Technology Solution"]}, "location": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}}}
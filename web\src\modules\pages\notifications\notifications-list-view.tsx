"use client";

import { Button, Typography } from "#/src/components/ui";
import { ArrowLeft } from "lucide-react";
import { _notifications } from "#/src/__mock__";
import NotificationsListPagination from "../../news/notifications-list-pagination";
import { useRouter } from "next/navigation";

export default function NotificationsListView() {
  const router = useRouter();
  const renderList = (
    <div className="container-inner flex flex-col gap-8 max-w-4xl mx-auto">
      <Typography variant="special-title" className="text-center">
        Thông báo
      </Typography>

      <NotificationsListPagination
        data={_notifications}
        pagination={{
          page: 2,
          pageSize: 10,
          total: _notifications.length,
        }}
      />
    </div>
  );

  return (
    <div className="container flex flex-col gap-8 py-10">
      <Button
        variant="text"
        className="!px-0 w-fit"
        onClick={() => router.back()}
      >
        <ArrowLeft />
        Quay lại
      </Button>
      {renderList}
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

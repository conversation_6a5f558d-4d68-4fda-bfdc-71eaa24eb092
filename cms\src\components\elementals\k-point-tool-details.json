{"collectionName": "components_elementals_k_point_tool_details", "info": {"displayName": "K_Point_Tool_Details"}, "options": {}, "attributes": {"transaction_value_min": {"type": "integer", "default": 0}, "transaction_value_max": {"type": "integer"}, "initial_transaction_value": {"type": "integer"}, "modes": {"type": "relation", "relation": "oneToMany", "target": "api::kpoint-mode.kpoint-mode"}, "note": {"type": "blocks"}}, "config": {}}
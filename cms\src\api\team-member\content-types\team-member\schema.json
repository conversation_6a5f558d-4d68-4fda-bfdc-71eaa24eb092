{"kind": "collectionType", "collectionName": "team_members", "info": {"singularName": "team-member", "pluralName": "team-members", "displayName": "Team - Member"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name_prefix": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "position": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "team_group": {"type": "relation", "relation": "manyToOne", "target": "api::team-group.team-group", "inversedBy": "team_members"}}}
"use client";

import { InputSearch } from "#/src/components/ui/input";
import { <PERSON>ton, Separator, Typography } from "@/components/ui";
import { SupportInfo } from "@/modules/shared";
import NewsEventsCard from "../../news/news-events-card";
import {
  _newsEvents,
  _newsEventsFeatured,
  _notifications,
} from "#/src/__mock__";
import NewsEventsListPagination from "../../news/news-events-list-pagination";
import Link from "next/link";
import { MoveRightIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function NewsEventsView() {
  const [searchValue, setSearchValue] = useState("");

  const searchParams = useSearchParams();
  const search = searchParams.get("search");
  const router = useRouter();

  useEffect(() => {
    setSearchValue(search || "");
  }, [search]);

  const handleSearch = () => {
    router.push(`/news-events/search?key=${searchValue}`);
  };

  const renderHead = (
    <div className="flex flex-col justify-center items-center gap-4">
      <Typography variant="special-title">Tin tức & sự kiện</Typography>

      <InputSearch
        placeholder="Tìm kiếm"
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onSearch={handleSearch}
        className="w-lg h-14 bg-white block mx-auto"
      />
    </div>
  );

  const renderFeatured = (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div className="col-span-1 lg:col-span-2">
        <NewsEventsCard hideDescription isFeatured item={_newsEventsFeatured} />
      </div>
      <div className="bg-white rounded-lg p-6 flex flex-col gap-6 h-full">
        {/* HEADING */}
        <div className="flex gap-2 items-center justify-between w-full">
          <Typography variant="title-1">Thông báo</Typography>
          <Link
            href="/news-events/notifications"
            className="text-emphasize group"
          >
            <Button variant="text">
              Xem tất cả
              <MoveRightIcon
                size={20}
                className="text-icon-placeholder transition-all duration-300 group-hover:text-brand"
              />
            </Button>
          </Link>
        </div>

        {/* LIST */}
        <div className="grid grid-cols-1">
          {_notifications.slice(0, 3).map((item, idx) => (
            <div key={idx}>
              <NewsEventsCard type="text-only" hideDescription item={item} />
              {idx < 2 && <Separator className="h-px" />}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderListPosts = (
    <div className="grid grid-cols-1 h-full">
      <NewsEventsListPagination
        data={_newsEvents}
        pagination={{
          page: 1,
          pageSize: 10,
          total: _newsEvents.length,
        }}
      />
    </div>
  );

  return (
    <div className="flex flex-col gap-10">
      <div className="container space-y-10">
        {renderHead}
        {renderFeatured}
        {renderListPosts}
      </div>
      <SupportInfo />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

import KnowledgeDetailView from "#/src/modules/pages/knowledge/knowledge-detail-view";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Knowledge Detail - Kafi",
  description: "Knowledge Detail - Kafi",
};

export default async function KnowledgeDetailPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <KnowledgeDetailView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON>po<PERSON>,
} from "@components/ui";
import { Check, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Command, CommandGroup, CommandItem } from "../components/ui/command";
//------------------------------------------------------------------------------------------------

type MultiSelectProps = {
  options: { label: string; value: string }[];
};
//------------------------------------------------------------------------------------------------
export default function MultiSelect({ options }: MultiSelectProps) {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);

  const toggleValue = (value: string) => {
    setSelected((prev) =>
      prev.includes(value) ? prev.filter((v) => v !== value) : [...prev, value],
    );
  };
  const toggleAll = () => {
    setSelected((prev) =>
      prev.length === options.length ? [] : options.map((item) => item.value),
    );
  };
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            "flex items-center  py-2 px-4 min-w-[200px] max-w-max justify-between rounded-xl bg-white cursor-pointer border ",
            open ? "border-brand" : "border-[#CBCDCE]",
          )}
        >
          <div className="flex flex-col items-start w-full">
            <Typography variant="caption-regular">Năm</Typography>
            <Typography variant="input-medium">
              {selected.length > 0 ? selected.join("-") : "Select options"}
            </Typography>
          </div>
          {open ? (
            <X className="ml-2 h-5 w-5 text-placeholder" />
          ) : (
            <ChevronDown className="ml-2 h-5 w-5 text-placeholder" />
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command className="bg-white">
          <CommandGroup className="p-0">
            <CommandItem
              onSelect={toggleAll}
              className={cn("my-0.5 px-3 py-2 data-[selected=true]:bg-canvas")}
            >
              <Typography
                variant="input-medium"
                classname={cn(
                  "text-emphasize",
                  selected.length === options.length && "text-brand",
                )}
              >
                Chọn tất cả
              </Typography>

              {/* <Check
                className={cn(
                  "ml-auto h-4 w-4 text-brand",

                )}
              /> */}
            </CommandItem>
            {options.map((item) => (
              <CommandItem
                key={item.value}
                onSelect={() => toggleValue(item.value)}
                className={cn(
                  "my-0.5 px-3 py-2 data-[selected=true]:bg-canvas",
                  selected.includes(item.value) && "bg-canvas",
                )}
              >
                <Typography variant="input-medium" classname="text-emphasize">
                  {item.label}
                </Typography>

                <Check
                  className={cn(
                    "ml-auto h-4 w-4 text-brand",
                    selected.includes(item.value) ? "opacity-100" : "opacity-0",
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

import { SearchView } from "#/src/modules/search";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Search - <PERSON><PERSON>",
  description: "Search - Ka<PERSON>",
};

export default async function SearchPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <SearchView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

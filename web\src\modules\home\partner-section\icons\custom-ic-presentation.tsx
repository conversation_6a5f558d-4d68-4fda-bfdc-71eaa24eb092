import { cn } from "#/src/lib/utils";

interface ICompassBoxProps {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
}

export const CustomIcPresentation: React.FC<ICompassBoxProps> = ({
  color = "text-icon-placeholder",
  strokeWidth = "3",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}) => {
  return (
    <svg
      width="48"
      height="49"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "cursor-pointer transition-all duration-300",
        color,
        `hover:${hoverColor}`,
        `focus:${activeColor}`,
        className,
      )}
    >
      <path
        d="M24.6667 32V42M36.6667 42L28.7639 35.4143C27.3064 34.1997 26.5776 33.5924 25.7641 33.3603C25.0468 33.1557 24.2867 33.1557 23.5694 33.3603C22.7559 33.5924 22.0271 34.1997 20.5696 35.4143L12.6667 42M16.6667 22V24M24.6667 18V24M32.6667 14V24M44.6667 6H4.66675M6.66675 6H42.6667V22.4C42.6667 25.7603 42.6667 27.4405 42.0128 28.7239C41.4375 29.8529 40.5197 30.7708 39.3907 31.346C38.1072 32 36.4271 32 33.0667 32H16.2667C12.9064 32 11.2263 32 9.9428 31.346C8.81383 30.7708 7.89595 29.8529 7.32071 28.7239C6.66675 27.4405 6.66675 25.7603 6.66675 22.4V6Z"
        stroke={"currentColor"}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

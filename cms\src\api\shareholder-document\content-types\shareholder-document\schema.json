{"kind": "collectionType", "collectionName": "shareholder_documents", "info": {"singularName": "shareholder-document", "pluralName": "shareholder-documents", "displayName": "Shareholder-Documents"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "text", "required": false}, "issued_date": {"type": "date"}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::shareholder-category.shareholder-category", "inversedBy": "documents"}, "files": {"type": "component", "component": "items.document-item", "repeatable": true}, "adminLabel": {"type": "string", "private": true}}}
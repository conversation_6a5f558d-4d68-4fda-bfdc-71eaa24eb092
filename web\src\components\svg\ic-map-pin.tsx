import SvgIcon from "./icon";

type MapPinIconProps = {
  size?: number;
  className?: string;
  isMatched?: boolean;
};

const MapPinIcon = ({ size = 32, className, isMatched }: MapPinIconProps) => {
  const pathScale = Number(size) / 32;

  return (
    // <svg
    //   width={size}
    //   height={size}
    //   viewBox="0 0 32 32"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    // >
    //   <path
    //     d="M15.9997 17.3334C18.2088 17.3334 19.9997 15.5425 19.9997 13.3334C19.9997 11.1242 18.2088 9.33335 15.9997 9.33335C13.7905 9.33335 11.9997 11.1242 11.9997 13.3334C11.9997 15.5425 13.7905 17.3334 15.9997 17.3334Z"
    //     stroke="black"
    //     strokeWidth="2"
    //     strokeLinecap="round"
    //     strokeLinejoin="round"
    //   />
    //   <path
    //     d="M15.9997 29.3334C21.333 24 26.6663 19.2244 26.6663 13.3334C26.6663 7.44232 21.8907 2.66669 15.9997 2.66669C10.1086 2.66669 5.33301 7.44232 5.33301 13.3334C5.33301 19.2244 10.6663 24 15.9997 29.3334Z"
    //     stroke="black"
    //     strokeWidth="2"
    //     strokeLinecap="round"
    //     strokeLinejoin="round"
    //   />
    // </svg>
    <SvgIcon size={size} className={className}>
      <path
        d="M15.9997 29.3334C21.333 24 26.6663 19.2244 26.6663 13.3334C26.6663 7.44232 21.8907 2.66669 15.9997 2.66669C10.1086 2.66669 5.33301 7.44232 5.33301 13.3334C5.33301 19.2244 10.6663 24 15.9997 29.3334Z"
        stroke={isMatched ? "#00C694" : "#000000"}
        fill={isMatched ? "#00C694" : "none"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{ transform: `scale(${pathScale})` }}
      />
      <path
        d="M15.9997 17.3334C18.2088 17.3334 19.9997 15.5425 19.9997 13.3334C19.9997 11.1242 18.2088 9.33335 15.9997 9.33335C13.7905 9.33335 11.9997 11.1242 11.9997 13.3334C11.9997 15.5425 13.7905 17.3334 15.9997 17.3334Z"
        stroke={isMatched ? "#F6F7F8" : "#000000"}
        fill={"#F6F7F8"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default MapPinIcon;

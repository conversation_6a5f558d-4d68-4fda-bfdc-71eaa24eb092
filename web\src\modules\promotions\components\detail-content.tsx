import { <PERSON><PERSON>, <PERSON>arator, Typography } from "#/src/components/ui";
import Image from "next/image";
//--------------------------------------------------------------------------
type DetailContentProps = {
  current_promotion: any;
};

//--------------------------------------------------------------------------
export default function DetailContent({
  current_promotion,
}: DetailContentProps) {
  const { created_at, views } = current_promotion || {};

  const renderBanner = (
    <div className="w-full grid grid-cols-1 md:grid-cols-12 items-center px-6 md:px-10 lg:px-20 rounded-2xl relative overflow-hidden">
      <div
        className="absolute top-0 left-0 w-[1760px] h-[1123px] bg-red-200 -z-10"
        style={{
          background:
            "radial-gradient(50% 50% at 50% 50%, #61B9F2 0%, #E9F9FD 100%)",
        }}
      ></div>
      <div className="col-span-1 md:col-span-6 lg:col-span-7 flex flex-col gap-8 py-10 lg:py-12 items-center md:items-start text-center md:text-left">
        <Image
          src={"/images/promotion/logo-1.png"}
          alt={"banner logo"}
          width={0}
          height={0}
          sizes="100vw"
          className="w-auto max-w-max h-[40px] md:h-[47px] object-cover"
        />

        <Typography variant="large-title" classname="text-emphasize">
          <span>Mở tài khoản trên VIB,</span>
          <br />
          <span>cơ hội nhận ngay</span>&nbsp;
          <span className="text-brand">100,000VND</span>
        </Typography>
        <Button variant="default" className="min-w-max">
          Mở tài khoản
        </Button>
        {/* {banner?.banner_cta?.length > 0 && (
          <div className="w-full flex justify-center md:justify-start">
            {banner?.banner_cta?.map((button: any, idx: number) => (
              <Link key={idx} href={button?.url} target={button?.target}>
                <Button variant={button?.variant} className="min-w-max">
                  {button?.text}
                </Button>
              </Link>
            ))}
          </div>
        )} */}
      </div>
      <div className="col-span-1 md:col-span-6 lg:col-span-5 relative h-full">
        <Image
          src={"/images/promotion/illustration-1.png"}
          alt={"banner illustration"}
          width={0}
          height={0}
          sizes="100vw"
          className="w-full max-w-[350px] h-auto object-cover relative md:absolute md:bottom-0 md:left-0"
        />
      </div>
    </div>
  );

  const renderContent = (
    <div className="w-full flex flex-col gap-8">
      <div className="w-full flex flex-col gap-3">
        <Typography variant="body-medium" classname="text-emphasize">
          Kafi trân trọng thông báo tài khoản chứng khoán Kafi đã chính thức
          được tích hợp trên ứng dụng Ngân hàng số MyVIB giúp việc đầu tư trở
          nên thuận tiện, an toàn và nhanh chóng hơn bao giờ hết!
        </Typography>
        <div className="w-full flex flex-col gap-3">
          <Typography variant="body-medium" classname="text-emphasize">
            Ưu đãi nhận ngay 100.000 VND khi mở tài khoản Kafi trên MyVIB với
            điều kiện sau:
          </Typography>
          <ul className="text-emphasize">
            <li>
              <Typography variant="body-medium" classname="text-emphasize">
                - Mở mới tài khoản Kafi Trade qua MyVIB.
              </Typography>
            </li>
            <li>
              <Typography variant="body-medium" classname="text-emphasize">
                - Thực hiện ít nhất 01 giao dịch chứng khoán trong 30 ngày đầu
                tiên.
              </Typography>
            </li>
            <li>
              <Typography variant="body-medium" classname="text-emphasize">
                - Không áp dụng với khách hàng đã từng mở tài khoản tại Kafi.
              </Typography>
            </li>
          </ul>
        </div>
        <Typography variant="body-medium" classname="text-emphasize">
          Hình thức nhận thưởng: 100.000 VND sẽ được chuyển trực tiếp vào tài
          khoản Kafi Trade trong vòng 05 ngày làm việc kể từ khi thỏa điều kiện.
        </Typography>
        <Button variant="outline" className="w-max">
          Thể lệ chi tiết
        </Button>
      </div>
      <div className="w-full flex flex-col gap-4">
        <Typography variant="body-medium" classname="text-emphasize">
          Tham gia dễ dàng chỉ với 04 bước đơn giản:
        </Typography>
        <Image
          src={"/images/promotion/post-1-image.png"}
          alt={"post image"}
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto rounded-2xl object-cover"
        />
      </div>
      <div className="w-full flex flex-col gap-3">
        <Typography variant="body-medium" classname="text-emphasize">
          Hãy trải nghiệm ngay hôm nay để dễ dàng theo dõi thị trường và nắm bắt
          cơ hội đầu tư hiệu quả!
        </Typography>
        <Button variant="default" className="w-max">
          Mở tài khoản
        </Button>
      </div>
    </div>
  );
  return (
    <div className="w-full flex flex-col gap-8 ">
      {renderBanner}

      <div className="flex flex-col gap-10 w-full max-w-[850px] mx-auto">
        <div className="w-full flex flex-col gap-8">
          <div className="flex items-center justify-between gap-4 py-6 border-b border-gray-200">
            <div className="flex h-5 items-center gap-4">
              <Typography
                variant="subheadline-medium"
                classname="text-emphasize"
              >
                {created_at}
              </Typography>
              <Separator orientation="vertical" className="text-gray-200" />

              <Typography
                variant="subheadline-medium"
                classname="text-placeholder"
              >
                <span className="text-emphasize">{views}</span>
                &nbsp;Đã xem
              </Typography>
            </div>
          </div>
          {renderContent}
        </div>
      </div>
    </div>
  );
}

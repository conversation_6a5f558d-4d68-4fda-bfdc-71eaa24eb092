"use client";

import {
  But<PERSON>,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Typography,
} from "#/src/components/ui";
import Image from "next/image";
import { useEffect, useMemo, useRef, useState } from "react";
import AboutView from "./sections/about-view";
import TeamView from "./sections/team-view";
import PeopleView from "./sections/people-view";
import ListJobView from "./sections/list-job-view";
import {
  ABOUT_DESTINY_ICON_BOX,
  ABOUT_JOB_DOCUMENTS,
  ABOUT_PEOPLE_ICON_BOX,
  ABOUT_STAFF,
  ABOUT_VISION_ICON_BOX,
} from "#/src/__mock__/about";
import { AWARDS_DATA } from "#/src/__mock__/awards";
import { ChevronDown } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const listTabs = [
  {
    name: "<PERSON><PERSON>",
    value: "about",
  },
  {
    name: "<PERSON><PERSON><PERSON> ng<PERSON>",
    value: "team",
  },
  {
    name: "<PERSON> người & Văn hoá",
    value: "people",
  },
  {
    name: "DS hành nghề chứng khoán",
    value: "list_jobs",
  },
];

export default function AboutUsView() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const section = searchParams.get("q");
  const tabTriggerRef = useRef<HTMLButtonElement>(null);
  const [tabTriggerWidth, setTabTriggerWidth] = useState<number>(0);
  const handleChangeSection = (section: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("q", section);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };
  const getActiveClass = (current: string | null, value: string) => {
    if (value == listTabs[0].value && !current) return "activeShadow";
    return current === value ? "activeShadow" : "text";
  };
  const foundTab = useMemo(() => {
    return listTabs.find((tab) => tab.value === section);
  }, [section]);
  useEffect(() => {
    if (!section) {
      const params = new URLSearchParams(searchParams);
      params.set("q", listTabs[0].value);
      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, [section, searchParams, pathname, router]);
  const renderCategory = (
    <div className="hidden md:flex gap-2 items-start bg-[#F1F3F5] w-fit rounded-full mx-auto p-1">
      {listTabs.map((tab, index) => (
        <Button
          key={index}
          variant={getActiveClass(section, tab.value)}
          onClick={() => handleChangeSection(tab.value)}
        >
          {tab.name}
        </Button>
      ))}
    </div>
  );
  useEffect(() => {
    if (!tabTriggerRef.current) return;
    const updateWidth = () => {
      if (tabTriggerRef.current) {
        setTabTriggerWidth(tabTriggerRef.current.offsetWidth);
      }
    };

    // Initial height
    updateWidth();

    // Create ResizeObserver to track height changes
    const observer = new ResizeObserver(() => {
      updateWidth();
    });

    observer.observe(tabTriggerRef.current);

    // Clean up observer on unmount
    return () => {
      observer.disconnect();
    };
  }, []);
  return (
    <div className="flex flex-col gap-10">
      <Image
        src={"/images/about/about-banner.png"}
        alt="research banner"
        width={0}
        height={0}
        className="hidden md:flex  w-full h-auto"
        sizes="(max-width: 768px) 100vw, (min-width: 1024px) 80vw"
      />

      {renderCategory}
      <div className="container !py-0 md:hidden">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger
            ref={tabTriggerRef}
            className="w-full bg-screen border border-border-button rounded-xl text-start p-3.5 flex justify-between items-center"
          >
            <Typography variant="input-medium" classname="text-emphasize">
              {foundTab?.name}
            </Typography>
            <ChevronDown className="text-icon-placeholder size-5" />
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-0 bg-screen rounded-xl">
            <DropdownMenuGroup
              className="p-3.5"
              style={{
                width: tabTriggerWidth,
              }}
            >
              {listTabs.map((tab, index) => (
                <DropdownMenuItem
                  className="px-0"
                  key={index}
                  onClick={() => handleChangeSection(tab.value)}
                >
                  <Typography variant="input-medium" classname="text-emphasize">
                    {tab.name}
                  </Typography>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {section === "about" && (
        <AboutView
          itemsVision={ABOUT_VISION_ICON_BOX}
          itemsDestiny={ABOUT_DESTINY_ICON_BOX}
          itemsAwards={AWARDS_DATA}
        />
      )}
      {section === "team" && <TeamView items={ABOUT_STAFF} />}
      {section === "people" && <PeopleView items={ABOUT_PEOPLE_ICON_BOX} />}
      {section === "list_jobs" && <ListJobView items={ABOUT_JOB_DOCUMENTS} />}
    </div>
  );
}

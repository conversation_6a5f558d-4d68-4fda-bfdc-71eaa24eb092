type PieChartIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const PieChartIcon = ({
  size = 24,
  className,
  color = "#00C694",
}: PieChartIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.2388 2.0258C13.6169 1.88148 13.0271 2.36106 13.0181 3.00559C13.0091 3.71981 12.987 9.60335 12.987 10.6855C12.991 11.0868 13.0291 11.3109 13.5819 11.6021C13.652 11.642 16.8025 13.1749 17.4625 13.4984C17.6007 13.5662 19.6486 14.6234 20.2475 14.9521C20.8293 15.2717 21.5704 14.9408 21.7186 14.2884C21.8948 13.5134 22 12.6318 22 11.8865C22 7.1488 18.7523 3.07265 14.2388 2.0258ZM15.0069 4.39196C17.9882 5.59992 19.9971 8.5652 19.9971 11.8865C19.9971 12.0683 19.9941 12.2999 19.9781 12.5015C19.5404 12.2779 19.1839 12.1009 18.6201 11.8233C18.4879 11.7583 15.4986 10.3044 14.9899 10.0534C14.9919 8.74977 14.9989 6.07788 15.0069 4.39196ZM11.547 13.2455C11.4929 13.2726 4.59296 16.7033 4.50583 16.7536C3.96906 17.0636 3.83386 17.7864 4.22442 18.2706C6.11414 20.6145 8.9342 22 11.9856 22C15.047 22 17.8891 20.5963 19.7778 18.239C20.1744 17.7448 20.0211 17.0206 19.4653 16.722C19.3302 16.6494 12.4783 13.2725 12.4242 13.2455C12.1458 13.1083 11.8254 13.1073 11.547 13.2455ZM11.9856 15.2682C12.6185 15.5807 15.6999 17.0968 17.3443 17.9114C15.8341 19.2637 13.9634 19.9773 11.9856 19.9773C10.0067 19.9773 8.13706 19.262 6.65593 17.9216C8.26124 17.1211 11.3457 15.587 11.9856 15.2682Z"
        fill="#959A9E"
      />
      <path
        d="M9.75 2.02563C5.241 3.06463 2 7.11803 2 11.8313C2 12.5635 2.10801 13.4542 2.28101 14.2198C2.42801 14.867 3.13699 15.1929 3.71899 14.8798C4.12499 14.6615 5.27999 14.0748 6.93799 13.2455C6.95999 13.2341 10.384 11.5123 10.438 11.4855C10.779 11.316 11 10.9576 11 10.5742C11 10.5108 10.976 3.6581 10.969 2.99996C10.962 2.35812 10.372 1.88232 9.75 2.02563ZM8.992 4.37467C9 6.23603 8.998 9.18175 9 9.97706C8.268 10.3431 5.64402 11.6447 4.02802 12.4608C4.01202 12.2599 4 12.0096 4 11.8313C4 8.53226 5.967 5.65322 8.992 4.37467Z"
        fill={color}
      />
    </svg>
  );
};

import type { Schema, Struct } from "@strapi/strapi";

export interface CardsOverlayCard extends Struct.ComponentSchema {
  collectionName: "components_cards_overlay_cards";
  info: {
    displayName: "Overlay_Card";
  };
  attributes: {
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Media<"images">;
    overlay_content: Schema.Attribute.Blocks;
    title: Schema.Attribute.String;
  };
}

export interface CardsToggleCard extends Struct.ComponentSchema {
  collectionName: "components_cards_toggle_cards";
  info: {
    displayName: "Toggle_Card";
  };
  attributes: {
    content: Schema.Attribute.Blocks;
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Media<"images">;
    title: Schema.Attribute.String;
  };
}

export interface DynamicZoneAnalyticsCenter extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_analytics_centers";
  info: {
    displayName: "Analytics_Center";
  };
  attributes: {
    featured_categories: Schema.Attribute.Relation<
      "oneToMany",
      "api::analysis-category.analysis-category"
    >;
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneBecomePartnerGuide extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_become_partner_guides";
  info: {
    displayName: "Become_Partner_Guide";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
    steps: Schema.Attribute.Component<"items.info-item", true>;
  };
}

export interface DynamicZoneBranchList extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_branch_lists";
  info: {
    displayName: "Branch_List";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneCertificationList extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_certification_lists";
  info: {
    displayName: "Certification_List";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneCta extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_ctas";
  info: {
    description: "";
    displayName: "CTA";
    icon: "cursor";
  };
  attributes: {
    ctas: Schema.Attribute.Component<"shared.button", true>;
    heading: Schema.Attribute.String;
    sub_heading: Schema.Attribute.String;
  };
}

export interface DynamicZoneCtaSection extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_cta_sections";
  info: {
    displayName: "CTA_Section";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneFaqs extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_faqs";
  info: {
    displayName: "Faqs";
    icon: "bulletList";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    items: Schema.Attribute.Component<"items.faq-item", true>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneFeaturedNewsEvents extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_featured_news_events";
  info: {
    displayName: "featured_news_events";
    icon: "calendar";
  };
  attributes: {
    award: Schema.Attribute.Relation<"oneToOne", "api::award.award">;
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    news: Schema.Attribute.Relation<"oneToMany", "api::article.article">;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneIconBoxGrid extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_icon_box_grids";
  info: {
    displayName: "Icon_Box_Grid";
    icon: "apps";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    icon_box_list: Schema.Attribute.Component<
      "elementals.icon-box-list",
      false
    >;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneIconBoxSlider extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_icon_box_sliders";
  info: {
    displayName: "Icon_Box_Slider";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    items: Schema.Attribute.Component<"items.icon-box-items", true>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneInvestmentPortfolio extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_investment_portfolios";
  info: {
    displayName: "Investment_Portfolio";
    icon: "calendar";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    items: Schema.Attribute.Component<"investment.investment-item", true>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneInvestmentSimulator extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_investment_simulators";
  info: {
    displayName: "Investment_Simulator_Tool";
    icon: "chartCircle";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
    simulator_tool: Schema.Attribute.Component<
      "elementals.investment-simulator-details",
      false
    >;
  };
}

export interface DynamicZoneKPointRatio extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_k_point_ratios";
  info: {
    displayName: "K_Point_Ratio";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    ratio_details: Schema.Attribute.Component<
      "elementals.k-point-ratio-details",
      false
    >;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneKPointTool extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_k_point_tools";
  info: {
    displayName: "K_Point_Tool";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
    tool_details: Schema.Attribute.Component<
      "elementals.k-point-tool-details",
      false
    >;
  };
}

export interface DynamicZoneKafiPartnersSection extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_kafi_partners_sections";
  info: {
    displayName: "Kafi_Partners_Section";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    kafi_partners: Schema.Attribute.Component<
      "elementals.kafi-partners",
      false
    >;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneKafiPodcast extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_kafi_podcasts";
  info: {
    displayName: "Kafi_Podcast";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    podcast: Schema.Attribute.Component<"elementals.podcast", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneKnowledgeList extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_knowledge_lists";
  info: {
    displayName: "Knowledge_List";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneLatestMarketInsights
  extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_latest_market_insights";
  info: {
    displayName: "Latest_Market_Insights";
  };
  attributes: {
    categories: Schema.Attribute.Relation<
      "oneToMany",
      "api::analysis-category.analysis-category"
    >;
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneLatestNewsEvents extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_latest_news_events";
  info: {
    displayName: "Latest_News_Events";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneMainPageHero extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_main_page_heroes";
  info: {
    displayName: "Main_Page_Hero";
  };
  attributes: {
    buttons: Schema.Attribute.Component<"shared.button", true>;
    heading: Schema.Attribute.Blocks;
    image: Schema.Attribute.Component<"styles.image-styles", false>;
    stat_items: Schema.Attribute.Component<"items.stat-item", true>;
    sub_heading: Schema.Attribute.Blocks;
  };
}

export interface DynamicZoneMarginTrading extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_margin_tradings";
  info: {
    displayName: "Margin_Trading";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneNewsEventsList extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_news_events_lists";
  info: {
    displayName: "News_Events_List";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneOutstandingAwards extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_outstanding_awards";
  info: {
    displayName: "Outstanding_Awards";
  };
  attributes: {
    awards: Schema.Attribute.Relation<"oneToMany", "api::award.award">;
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    image: Schema.Attribute.Media<"images">;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZonePageBanner extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_page_banners";
  info: {
    displayName: "Page_Banner";
  };
  attributes: {
    banner: Schema.Attribute.Media<"images">;
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
  };
}

export interface DynamicZonePromotionBanner extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_promotion_banners";
  info: {
    displayName: "Home_Promotion_Banners";
  };
  attributes: {
    banner: Schema.Attribute.Component<
      "elementals.promotion-banner-elemental",
      false
    >;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZonePromotionList extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_promotion_lists";
  info: {
    displayName: "Promotion_List";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneRecruitmentSection extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_recruitment_sections";
  info: {
    displayName: "Recruitment_Section";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneSupportCenter extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_support_centers";
  info: {
    displayName: "Support_Center";
    icon: "user";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneSupportSection extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_support_sections";
  info: {
    displayName: "Support_Section";
    icon: "phone";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneTeamMembers extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_team_members";
  info: {
    displayName: "Team_Members";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneTermsAndConditions extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_terms_and_conditions";
  info: {
    displayName: "Terms_And_Conditions";
    icon: "calendar";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneToggleCards extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_toggle_cards";
  info: {
    displayName: "Toggle_Cards";
    icon: "apps";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    items: Schema.Attribute.Component<"cards.toggle-card", true>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneTradingPlatforms extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_trading_platforms";
  info: {
    displayName: "Trading_Platforms";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    platforms: Schema.Attribute.Component<"items.platform-item", true>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface DynamicZoneVideoSection extends Struct.ComponentSchema {
  collectionName: "components_dynamic_zone_video_sections";
  info: {
    displayName: "Video_Section";
  };
  attributes: {
    heading: Schema.Attribute.Component<"elementals.section-heading", false>;
    media_video: Schema.Attribute.Component<"elementals.media-video", false>;
    section_styles: Schema.Attribute.Component<
      "elementals.section-styles",
      false
    >;
  };
}

export interface ElementalsIconBoxList extends Struct.ComponentSchema {
  collectionName: "components_elementals_icon_box_lists";
  info: {
    displayName: "Icon_Box_List";
    icon: "apps";
  };
  attributes: {
    desktop_item_count: Schema.Attribute.Integer;
    items: Schema.Attribute.Component<"items.icon-box-items", true>;
    mobile_item_count: Schema.Attribute.Integer;
    tablet_item_count: Schema.Attribute.Integer;
  };
}

export interface ElementalsInvestmentSimulatorDetails
  extends Struct.ComponentSchema {
  collectionName: "components_elementals_investment_simulator_details";
  info: {
    displayName: "investment_slider_block";
    icon: "command";
  };
  attributes: {
    defaultValue: Schema.Attribute.Integer;
    label: Schema.Attribute.Text;
    max_value: Schema.Attribute.Integer;
    min_value: Schema.Attribute.Integer;
    step: Schema.Attribute.Integer;
  };
}

export interface ElementalsKPointRatioDetails extends Struct.ComponentSchema {
  collectionName: "components_elementals_k_point_ratio_details";
  info: {
    displayName: "K_Point_Ratio_Details";
  };
  attributes: {
    modes: Schema.Attribute.Relation<
      "oneToMany",
      "api::kpoint-mode.kpoint-mode"
    >;
    note: Schema.Attribute.Blocks;
    overview: Schema.Attribute.Component<"items.info-item", false>;
  };
}

export interface ElementalsKPointToolDetails extends Struct.ComponentSchema {
  collectionName: "components_elementals_k_point_tool_details";
  info: {
    displayName: "K_Point_Tool_Details";
  };
  attributes: {
    initial_transaction_value: Schema.Attribute.Integer;
    modes: Schema.Attribute.Relation<
      "oneToMany",
      "api::kpoint-mode.kpoint-mode"
    >;
    note: Schema.Attribute.Blocks;
    transaction_value_max: Schema.Attribute.Integer;
    transaction_value_min: Schema.Attribute.Integer &
      Schema.Attribute.DefaultTo<0>;
  };
}

export interface ElementalsKafiPartners extends Struct.ComponentSchema {
  collectionName: "components_elementals_kafi_partners";
  info: {
    displayName: "kafi_partners";
  };
  attributes: {
    avatars: Schema.Attribute.Media<"images", true>;
    features: Schema.Attribute.Component<"items.icon-box-items", true>;
    statistic_items: Schema.Attribute.Component<"items.stat-item", true>;
  };
}

export interface ElementalsMediaVideo extends Struct.ComponentSchema {
  collectionName: "components_elementals_media_videos";
  info: {
    displayName: "Media_Video";
  };
  attributes: {
    open_type: Schema.Attribute.Enumeration<["playin", "dialog"]> &
      Schema.Attribute.DefaultTo<"playin">;
    videos: Schema.Attribute.Component<"shared.video", true>;
  };
}

export interface ElementalsPodcast extends Struct.ComponentSchema {
  collectionName: "components_elementals_podcasts";
  info: {
    displayName: "podcast";
  };
  attributes: {
    auto_filter: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    auto_filter_limit: Schema.Attribute.Integer;
    items: Schema.Attribute.Relation<"oneToMany", "api::podcast.podcast">;
    social_links: Schema.Attribute.Component<"shared.social-link", true>;
  };
}

export interface ElementalsPromotionBanner extends Struct.ComponentSchema {
  collectionName: "components_elementals_promotion_banners";
  info: {
    displayName: "Promotion_Banner";
    icon: "landscape";
  };
  attributes: {
    banner_background: Schema.Attribute.Media<"images">;
    banner_cta: Schema.Attribute.Component<"shared.button", true>;
    banner_logo: Schema.Attribute.Media<"images">;
    banner_subtitle: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        "plugin::ckeditor5.CKEditor",
        {
          preset: "defaultHtml";
        }
      >;
    banner_title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        "plugin::ckeditor5.CKEditor",
        {
          preset: "defaultHtml";
        }
      >;
    illustration_image: Schema.Attribute.Media<"images">;
  };
}

export interface ElementalsPromotionBannerElemental
  extends Struct.ComponentSchema {
  collectionName: "components_elementals_promotion_banner_elementals";
  info: {
    displayName: "promotion_banner_elemental";
  };
  attributes: {
    background: Schema.Attribute.Media<"images">;
    promotions: Schema.Attribute.Component<"items.promotion-item", true>;
  };
}

export interface ElementalsSectionHeading extends Struct.ComponentSchema {
  collectionName: "components_elementals_section_headings";
  info: {
    displayName: "Section_Heading";
  };
  attributes: {
    buttons: Schema.Attribute.Component<"shared.button", true>;
    heading: Schema.Attribute.Text;
    heading_align: Schema.Attribute.Enumeration<["left", "center", "right"]> &
      Schema.Attribute.DefaultTo<"center">;
    max_width: Schema.Attribute.Integer;
    sub_heading: Schema.Attribute.Text;
  };
}

export interface ElementalsSectionStyles extends Struct.ComponentSchema {
  collectionName: "components_elementals_section_styles";
  info: {
    displayName: "Section_Styles";
  };
  attributes: {
    content_max_width: Schema.Attribute.Enumeration<
      [
        "content_1600",
        "content_1280",
        "content_944",
        "content_664",
        "content_327",
      ]
    > &
      Schema.Attribute.DefaultTo<"content_1280">;
    padding: Schema.Attribute.Component<"styles.spacing", false>;
  };
}

export interface GlobalFooter extends Struct.ComponentSchema {
  collectionName: "components_global_footers";
  info: {
    description: "";
    displayName: "Footer";
    icon: "apps";
  };
  attributes: {
    copyright: Schema.Attribute.Text;
    description: Schema.Attribute.Text;
    footer_nav: Schema.Attribute.Component<"items.footer-nav-columns", true>;
    logo: Schema.Attribute.Media<"images">;
    secondary_nav: Schema.Attribute.Component<"shared.link", true>;
    social_media_links: Schema.Attribute.Component<"shared.link", true>;
    terms_of_use: Schema.Attribute.Blocks;
  };
}

export interface GlobalNavbar extends Struct.ComponentSchema {
  collectionName: "components_global_navbars";
  info: {
    displayName: "Navbar";
    icon: "bold";
  };
  attributes: {
    logo: Schema.Attribute.Media<"images">;
  };
}

export interface InvestmentExpectedReturn extends Struct.ComponentSchema {
  collectionName: "components_investment_expected_returns";
  info: {
    displayName: "investment_projection";
    icon: "connector";
  };
  attributes: {
    achievement_rate: Schema.Attribute.Decimal;
    achievement_rate_label: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<"C\u00F3 th\u1EC3 \u0111\u1EA1t \u0111\u01B0\u1EE3c">;
    elapsed_duration: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<12>;
    expected_return_label: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<"L\u1EE3i nhu\u1EADn d\u1EF1 ki\u1EBFn">;
    expected_return_range: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<"2-4%/n\u0103m">;
    projected_investment_label: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<"T\u1ED5ng gi\u00E1 tr\u1ECB d\u1EF1 ki\u1EBFn">;
    projected_investment_value: Schema.Attribute.BigInteger;
  };
}

export interface InvestmentInvestmentFund extends Struct.ComponentSchema {
  collectionName: "components_investment_investment_funds";
  info: {
    displayName: "Investment_Fund";
    icon: "calendar";
  };
  attributes: {
    allocated_amount: Schema.Attribute.BigInteger;
    allocation_percentage: Schema.Attribute.Integer;
    fund_name: Schema.Attribute.String;
  };
}

export interface InvestmentInvestmentItem extends Struct.ComponentSchema {
  collectionName: "components_investment_investment_items";
  info: {
    displayName: "investment_item";
    icon: "command";
  };
  attributes: {
    description: Schema.Attribute.Text;
    investment_funds: Schema.Attribute.Component<
      "investment.investment-fund",
      false
    >;
    investment_projection: Schema.Attribute.Component<
      "investment.expected-return",
      false
    >;
    risk_profile: Schema.Attribute.Component<"investment.risk-profile", false>;
    title: Schema.Attribute.String;
  };
}

export interface InvestmentRiskProfile extends Struct.ComponentSchema {
  collectionName: "components_investment_risk_profiles";
  info: {
    displayName: "risk_profile";
  };
  attributes: {
    risk_label: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<"M\u1EE9c \u0111\u1ED9 r\u1EE7i ro">;
    risk_level: Schema.Attribute.Enumeration<
      ["Th\u1EA5p", "Trung b\u00ECnh", "Cao"]
    >;
    risk_level_value: Schema.Attribute.Integer;
  };
}

export interface ItemsDocumentItem extends Struct.ComponentSchema {
  collectionName: "components_items_document_items";
  info: {
    displayName: "Document_Item";
  };
  attributes: {
    file: Schema.Attribute.Media<"files">;
    title: Schema.Attribute.Text & Schema.Attribute.Required;
  };
}

export interface ItemsFaqItem extends Struct.ComponentSchema {
  collectionName: "components_items_faq_items";
  info: {
    displayName: "Faq_Item";
  };
  attributes: {
    answer: Schema.Attribute.Blocks;
    question: Schema.Attribute.Text;
  };
}

export interface ItemsFooterLinkGroup extends Struct.ComponentSchema {
  collectionName: "components_items_footer_link_groups";
  info: {
    displayName: "Footer_Link_Group";
    icon: "bulletList";
  };
  attributes: {
    links: Schema.Attribute.Component<"shared.link", true>;
    title: Schema.Attribute.String;
  };
}

export interface ItemsFooterNavColumns extends Struct.ComponentSchema {
  collectionName: "components_items_footer_nav_columns";
  info: {
    displayName: "Footer_Nav_Columns";
  };
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private;
    link_groups: Schema.Attribute.Component<"items.footer-link-group", true>;
  };
}

export interface ItemsIconBoxItems extends Struct.ComponentSchema {
  collectionName: "components_items_icon_box_items";
  info: {
    displayName: "Icon_Box_Item";
  };
  attributes: {
    description: Schema.Attribute.Text;
    icon: Schema.Attribute.Media<"images">;
    title: Schema.Attribute.Text;
  };
}

export interface ItemsIconTextItem extends Struct.ComponentSchema {
  collectionName: "components_items_icon_text_items";
  info: {
    displayName: "icon_text_item";
  };
  attributes: {
    icon: Schema.Attribute.Media<"images">;
    text: Schema.Attribute.String;
  };
}

export interface ItemsInfoItem extends Struct.ComponentSchema {
  collectionName: "components_items_info_items";
  info: {
    displayName: "Info_Item";
  };
  attributes: {
    description: Schema.Attribute.Blocks;
    image: Schema.Attribute.Media<"images">;
    order_number: Schema.Attribute.Integer;
    title: Schema.Attribute.String;
  };
}

export interface ItemsPlatformItem extends Struct.ComponentSchema {
  collectionName: "components_items_platform_items";
  info: {
    displayName: "Platform_Item";
  };
  attributes: {
    buttons: Schema.Attribute.Component<"shared.button", true>;
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Media<"images">;
    order_number: Schema.Attribute.Integer;
    qr_code: Schema.Attribute.Media<"images">;
    title: Schema.Attribute.String;
  };
}

export interface ItemsPromotionItem extends Struct.ComponentSchema {
  collectionName: "components_items_promotion_items";
  info: {
    displayName: "promotion_item";
  };
  attributes: {
    buttons: Schema.Attribute.Component<"shared.button", true>;
    feature_items: Schema.Attribute.Component<"items.icon-text-item", true>;
    image: Schema.Attribute.Media<"images">;
    title: Schema.Attribute.Text;
  };
}

export interface ItemsStatItem extends Struct.ComponentSchema {
  collectionName: "components_items_stat_items";
  info: {
    displayName: "Stat_Item";
    icon: "bulletList";
  };
  attributes: {
    description: Schema.Attribute.Text;
    number: Schema.Attribute.Integer;
    number_prefix: Schema.Attribute.String;
    number_suffix: Schema.Attribute.String;
  };
}

export interface SharedButton extends Struct.ComponentSchema {
  collectionName: "components_shared_buttons";
  info: {
    description: "";
    displayName: "Button";
    icon: "cursor";
  };
  attributes: {
    target: Schema.Attribute.Enumeration<
      ["_blank", "_self", "_parent", "_top"]
    > &
      Schema.Attribute.DefaultTo<"_self">;
    text: Schema.Attribute.String;
    url: Schema.Attribute.String;
    variant: Schema.Attribute.Enumeration<
      ["primary", "outline-on-light", "outline-on-dark", "text"]
    > &
      Schema.Attribute.DefaultTo<"primary">;
  };
}

export interface SharedLink extends Struct.ComponentSchema {
  collectionName: "components_shared_links";
  info: {
    displayName: "Link";
    icon: "link";
  };
  attributes: {
    target: Schema.Attribute.Enumeration<
      ["_blank", "_self", "_parent", "_top"]
    > &
      Schema.Attribute.DefaultTo<"_self">;
    text: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface SharedMedia extends Struct.ComponentSchema {
  collectionName: "components_shared_media";
  info: {
    displayName: "Media";
    icon: "file-video";
  };
  attributes: {
    file: Schema.Attribute.Media<"images" | "files" | "videos">;
  };
}

export interface SharedQuote extends Struct.ComponentSchema {
  collectionName: "components_shared_quotes";
  info: {
    displayName: "Quote";
    icon: "indent";
  };
  attributes: {
    body: Schema.Attribute.Text;
    title: Schema.Attribute.String;
  };
}

export interface SharedRichText extends Struct.ComponentSchema {
  collectionName: "components_shared_rich_texts";
  info: {
    description: "";
    displayName: "Rich text";
    icon: "align-justify";
  };
  attributes: {
    body: Schema.Attribute.RichText;
  };
}

export interface SharedSeo extends Struct.ComponentSchema {
  collectionName: "components_shared_seos";
  info: {
    description: "";
    displayName: "Seo";
    icon: "allergies";
    name: "Seo";
  };
  attributes: {
    canonicalURL: Schema.Attribute.String;
    keywords: Schema.Attribute.Text;
    metaDescription: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 50;
      }>;
    metaImage: Schema.Attribute.Media<"images">;
    metaRobots: Schema.Attribute.String;
    metaTitle: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    metaViewport: Schema.Attribute.String;
    structuredData: Schema.Attribute.JSON;
  };
}

export interface SharedSlider extends Struct.ComponentSchema {
  collectionName: "components_shared_sliders";
  info: {
    description: "";
    displayName: "Slider";
    icon: "address-book";
  };
  attributes: {
    files: Schema.Attribute.Media<"images", true>;
  };
}

export interface SharedSocialLink extends Struct.ComponentSchema {
  collectionName: "components_shared_social_links";
  info: {
    displayName: "Social_Link";
  };
  attributes: {
    custom_icon: Schema.Attribute.Media<"images">;
    type: Schema.Attribute.Enumeration<
      ["facebook", "spotify", "instagram", "linkedin", "youtube", "tiktok"]
    >;
    url: Schema.Attribute.String;
  };
}

export interface SharedVideo extends Struct.ComponentSchema {
  collectionName: "components_shared_videos";
  info: {
    displayName: "video";
  };
  attributes: {
    description: Schema.Attribute.Text;
    embed: Schema.Attribute.Text;
    image_poster: Schema.Attribute.Media<"images">;
    video: Schema.Attribute.Media<"videos">;
  };
}

export interface StylesBackgroundStyles extends Struct.ComponentSchema {
  collectionName: "components_styles_background_styles";
  info: {
    displayName: "Background_Styles";
  };
  attributes: {
    color: Schema.Attribute.String &
      Schema.Attribute.CustomField<"plugin::color-picker.color">;
    image: Schema.Attribute.Media<"images">;
    opacity: Schema.Attribute.Decimal &
      Schema.Attribute.SetMinMax<
        {
          max: 1;
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<1>;
    repeat: Schema.Attribute.Enumeration<
      ["no-repeat", "repeat", "repeat-x", "repeat-y"]
    > &
      Schema.Attribute.DefaultTo<"no-repeat">;
    size: Schema.Attribute.Enumeration<["cover", "contain", "auto"]> &
      Schema.Attribute.DefaultTo<"cover">;
  };
}

export interface StylesImageStyles extends Struct.ComponentSchema {
  collectionName: "components_styles_image_styles";
  info: {
    displayName: "Image_Styles";
    icon: "picture";
  };
  attributes: {
    image: Schema.Attribute.Media<"images">;
    max_width: Schema.Attribute.Integer;
  };
}

export interface StylesSpacing extends Struct.ComponentSchema {
  collectionName: "components_styles_spacings";
  info: {
    displayName: "Spacing";
  };
  attributes: {
    bottom: Schema.Attribute.Integer;
    left: Schema.Attribute.Integer;
    right: Schema.Attribute.Integer;
    top: Schema.Attribute.Integer;
    unit: Schema.Attribute.Enumeration<["px", "percent_%", "rem", "em"]> &
      Schema.Attribute.DefaultTo<"px">;
  };
}

export interface StylesTextStyles extends Struct.ComponentSchema {
  collectionName: "components_styles_text_styles";
  info: {
    displayName: "Text_Styles";
  };
  attributes: {
    color: Schema.Attribute.String &
      Schema.Attribute.CustomField<"plugin::color-picker.color">;
    text: Schema.Attribute.Text;
    variant: Schema.Attribute.Enumeration<
      ["h1", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "caption"]
    >;
  };
}

declare module "@strapi/strapi" {
  export namespace Public {
    export interface ComponentSchemas {
      "cards.overlay-card": CardsOverlayCard;
      "cards.toggle-card": CardsToggleCard;
      "dynamic-zone.analytics-center": DynamicZoneAnalyticsCenter;
      "dynamic-zone.become-partner-guide": DynamicZoneBecomePartnerGuide;
      "dynamic-zone.branch-list": DynamicZoneBranchList;
      "dynamic-zone.certification-list": DynamicZoneCertificationList;
      "dynamic-zone.cta": DynamicZoneCta;
      "dynamic-zone.cta-section": DynamicZoneCtaSection;
      "dynamic-zone.faqs": DynamicZoneFaqs;
      "dynamic-zone.featured-news-events": DynamicZoneFeaturedNewsEvents;
      "dynamic-zone.icon-box-grid": DynamicZoneIconBoxGrid;
      "dynamic-zone.icon-box-slider": DynamicZoneIconBoxSlider;
      "dynamic-zone.investment-portfolio": DynamicZoneInvestmentPortfolio;
      "dynamic-zone.investment-simulator": DynamicZoneInvestmentSimulator;
      "dynamic-zone.k-point-ratio": DynamicZoneKPointRatio;
      "dynamic-zone.k-point-tool": DynamicZoneKPointTool;
      "dynamic-zone.kafi-partners-section": DynamicZoneKafiPartnersSection;
      "dynamic-zone.kafi-podcast": DynamicZoneKafiPodcast;
      "dynamic-zone.knowledge-list": DynamicZoneKnowledgeList;
      "dynamic-zone.latest-market-insights": DynamicZoneLatestMarketInsights;
      "dynamic-zone.latest-news-events": DynamicZoneLatestNewsEvents;
      "dynamic-zone.main-page-hero": DynamicZoneMainPageHero;
      "dynamic-zone.margin-trading": DynamicZoneMarginTrading;
      "dynamic-zone.news-events-list": DynamicZoneNewsEventsList;
      "dynamic-zone.outstanding-awards": DynamicZoneOutstandingAwards;
      "dynamic-zone.page-banner": DynamicZonePageBanner;
      "dynamic-zone.promotion-banner": DynamicZonePromotionBanner;
      "dynamic-zone.promotion-list": DynamicZonePromotionList;
      "dynamic-zone.recruitment-section": DynamicZoneRecruitmentSection;
      "dynamic-zone.support-center": DynamicZoneSupportCenter;
      "dynamic-zone.support-section": DynamicZoneSupportSection;
      "dynamic-zone.team-members": DynamicZoneTeamMembers;
      "dynamic-zone.terms-and-conditions": DynamicZoneTermsAndConditions;
      "dynamic-zone.toggle-cards": DynamicZoneToggleCards;
      "dynamic-zone.trading-platforms": DynamicZoneTradingPlatforms;
      "dynamic-zone.video-section": DynamicZoneVideoSection;
      "elementals.icon-box-list": ElementalsIconBoxList;
      "elementals.investment-simulator-details": ElementalsInvestmentSimulatorDetails;
      "elementals.k-point-ratio-details": ElementalsKPointRatioDetails;
      "elementals.k-point-tool-details": ElementalsKPointToolDetails;
      "elementals.kafi-partners": ElementalsKafiPartners;
      "elementals.media-video": ElementalsMediaVideo;
      "elementals.podcast": ElementalsPodcast;
      "elementals.promotion-banner": ElementalsPromotionBanner;
      "elementals.promotion-banner-elemental": ElementalsPromotionBannerElemental;
      "elementals.section-heading": ElementalsSectionHeading;
      "elementals.section-styles": ElementalsSectionStyles;
      "global.footer": GlobalFooter;
      "global.navbar": GlobalNavbar;
      "investment.expected-return": InvestmentExpectedReturn;
      "investment.investment-fund": InvestmentInvestmentFund;
      "investment.investment-item": InvestmentInvestmentItem;
      "investment.risk-profile": InvestmentRiskProfile;
      "items.document-item": ItemsDocumentItem;
      "items.faq-item": ItemsFaqItem;
      "items.footer-link-group": ItemsFooterLinkGroup;
      "items.footer-nav-columns": ItemsFooterNavColumns;
      "items.icon-box-items": ItemsIconBoxItems;
      "items.icon-text-item": ItemsIconTextItem;
      "items.info-item": ItemsInfoItem;
      "items.platform-item": ItemsPlatformItem;
      "items.promotion-item": ItemsPromotionItem;
      "items.stat-item": ItemsStatItem;
      "shared.button": SharedButton;
      "shared.link": SharedLink;
      "shared.media": SharedMedia;
      "shared.quote": SharedQuote;
      "shared.rich-text": SharedRichText;
      "shared.seo": SharedSeo;
      "shared.slider": SharedSlider;
      "shared.social-link": SharedSocialLink;
      "shared.video": SharedVideo;
      "styles.background-styles": StylesBackgroundStyles;
      "styles.image-styles": StylesImageStyles;
      "styles.spacing": StylesSpacing;
      "styles.text-styles": StylesTextStyles;
    }
  }
}

"use client";
import { cn } from "@/lib/utils";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronRight } from "lucide-react";
import * as React from "react";

function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" {...props} />;
}

function AccordionItem({
  className,
  style,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn("border-b last:border-b-0", className)}
      {...props}
      style={{
        transition: "all 0.5s ease",
        ...style,
      }}
    />
  );
}

interface AccordionTriggerProps
  extends React.ComponentProps<typeof AccordionPrimitive.Trigger> {
  showIcon?: boolean;
  showIconActive?: boolean;
  width?: number;
  height?: number;
  arrowIcon?: boolean;
}

function AccordionTrigger({
  className,
  children,
  showIcon = true,
  showIconActive = true,
  width = 32,
  height = 32,
  arrowIcon = false,
  ...props
}: AccordionTriggerProps) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        data-slot="accordion-trigger"
        className={cn(
          "group flex flex-1 items-center justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none duration-500 hover:underline disabled:pointer-events-none disabled:opacity-50 cursor-pointer",
          className,
          showIconActive && "[&[data-state=open]>svg]:rotate-180",
        )}
        {...props}
      >
        {children}
        <div className="flex items-center justify-center shrink-0">
          {showIcon && (
            <svg
              width={width}
              height={height}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-transform duration-300 ease-in-out group-data-[state=open]:rotate-45 text-icon-placeholder"
            >
              {/* Horizontal line */}
              <path d="M6 12H18" />
              {/* Vertical line */}
              <path d="M12 6V18" />
            </svg>
          )}
          {arrowIcon && (
            <ChevronRight className="size-8 text-emphasize hidden group-data-[state=open]:flex" />
          )}
        </div>
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      style={{
        transition: "max-height 0.5s ease, opacity 0.5s ease",
      }}
      {...props}
    >
      <div className={cn("pt-0 pb-4", className)}>{children}</div>
    </AccordionPrimitive.Content>
  );
}

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger };

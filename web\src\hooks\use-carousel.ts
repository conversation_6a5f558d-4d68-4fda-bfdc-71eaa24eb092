import { CarouselApi } from "@/components/ui/carousel";
import * as React from "react";

export function useCarousel() {
  const [api, setApi] = React.useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const [scrollSnaps, setScrollSnaps] = React.useState<number[]>([]);

  React.useEffect(() => {
    if (!api) return;

    setScrollSnaps(api.scrollSnapList());
    setSelectedIndex(api.selectedScrollSnap());

    const onSelect = () => setSelectedIndex(api.selectedScrollSnap());
    api.on("select", onSelect);

    return () => {
      api.off("select", onSelect);
    };
  }, [api]);

  return {
    setApi, // truyền vào <Carousel setApi={setApi} />
    selectedIndex,
    scrollSnaps,
    isAtEnd: selectedIndex === scrollSnaps.length - 1,
    isAtStart: selectedIndex === 0,
    canScrollNext: selectedIndex < scrollSnaps.length - 1,
    canScrollPrev: selectedIndex > 0,

    // ✅ thêm 2 hàm này để điều khiển carousel
    scrollNext: () => api?.scrollNext(),
    scrollPrev: () => api?.scrollPrev(),
  };
}

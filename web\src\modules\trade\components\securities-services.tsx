"use client";
import { But<PERSON>, Typography } from "@/components/ui";
import { CustomIcon } from "../../home/<USER>/icons/custom-icon";
import { usePathname } from "next/navigation";
import { cn } from "@/lib";

//----------------------------------------------------------------------------------
type TProps = {
  data: any;
};

//----------------------------------------------------------------------------------
export default function SecuritiesServices({ data }: TProps) {
  const { heading, sub_heading, buttons, icon_box_list } = data || {};
  const pathname = usePathname();
  return (
    <section className="container !py-0">
      <div className="flex flex-col items-center gap-6 md:gap-10">
        <div className="flex flex-col gap-6 max-w-[635px] text-center">
          {heading ? (
            <Typography
              variant="large-title"
              classname="max-md:text-2xl max-lg:text-3xl"
            >
              {heading}
            </Typography>
          ) : null}
          {sub_heading ? (
            <Typography variant="body-medium">{sub_heading}</Typography>
          ) : null}
        </div>

        {buttons?.length ? (
          <div className="flex items-center gap-x-6">
            {buttons?.map((item: any, idx: number) => (
              <Button key={idx} variant={"outline"}>
                {item?.text}
              </Button>
            ))}
          </div>
        ) : null}

        <div
          className={cn(
            "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ",
            pathname.includes("trade") && "lg:grid-cols-4",
          )}
        >
          {icon_box_list?.map((item: any, idx: number) => (
            <CardItem key={idx} item={item} />
          ))}
        </div>
      </div>
    </section>
  );
}
//
function CardItem({ item }: { item: any }) {
  return (
    <div className="h-full flex flex-col gap-10 max-w-[405px] group">
      <div className="w-full flex justify-center md:justify-start">
        <CustomIcon path={item?.icon} className="group-hover:text-brand" />
      </div>
      <div className="flex flex-col gap-4 text-center md:text-left">
        <Typography
          variant={"title-3"}
          className="font-semibold text-[#1E2328]"
        >
          {item?.title}
        </Typography>
        <Typography variant={"body-medium"} className="text-[#1E2328]">
          {item?.sub_title}
        </Typography>
      </div>
    </div>
  );
}

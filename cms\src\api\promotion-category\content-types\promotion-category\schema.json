{"kind": "collectionType", "collectionName": "promotion_categories", "info": {"singularName": "promotion-category", "pluralName": "promotion-categories", "displayName": "Promotion - Categories"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "promotions": {"type": "relation", "relation": "oneToMany", "target": "api::promotion.promotion", "mappedBy": "category"}}}
"use client";

import { useState } from "react";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Typography } from "@/components/ui/typography";

// Zod schema for validation
const jobApplicationSchema = z.object({
  fullName: z
    .string()
    .min(2, "Họ tên phải có ít nhất 2 ký tự")
    .max(50, "Họ tên không được quá 50 ký tự"),
  email: z.string().email("Email không hợp lệ").min(1, "<PERSON>ail là bắt buộc"),
  phone: z
    .string()
    .min(10, "Số điện thoại phải có ít nhất 10 số")
    .max(11, "Số điện thoại không được quá 11 số")
    .regex(/^[0-9]+$/, "<PERSON><PERSON> điện thoại chỉ được chứa số"),
  position: z.string().min(1, "Vị trí ứng tuyển là bắt buộc"),
  experience: z
    .string()
    .min(10, "Kinh nghiệm phải có ít nhất 10 ký tự")
    .max(500, "Kinh nghiệm không được quá 500 ký tự"),
  coverLetter: z
    .string()
    .min(50, "Thư xin việc phải có ít nhất 50 ký tự")
    .max(1000, "Thư xin việc không được quá 1000 ký tự"),
});

type JobApplicationFormData = z.infer<typeof jobApplicationSchema>;

type FormErrors = Partial<Record<keyof JobApplicationFormData, string>>;

interface JobApplicationFormProps {
  jobId?: string;
  jobTitle?: string;
}

export default function JobApplicationForm({
  jobTitle = "Vị trí ứng tuyển",
}: JobApplicationFormProps) {
  const [formData, setFormData] = useState<JobApplicationFormData>({
    fullName: "",
    email: "",
    phone: "",
    position: jobTitle,
    experience: "",
    coverLetter: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate single field
  const validateField = (name: keyof JobApplicationFormData, value: string) => {
    try {
      jobApplicationSchema.shape[name].parse(value);
      setErrors((prev) => ({ ...prev, [name]: undefined }));
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors((prev) => ({ ...prev, [name]: error.issues[0]?.message }));
      }
      return false;
    }
  };

  // Validate entire form
  const validateForm = () => {
    try {
      jobApplicationSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.issues.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof JobApplicationFormData] =
              err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  // Handle input change
  const handleChange = (name: keyof JobApplicationFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Validate on change
    if (value.trim()) {
      validateField(name, value);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Call API to submit application
      console.log("Form submitted:", formData);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      alert("Đơn ứng tuyển đã được gửi thành công!");
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Có lỗi xảy ra khi gửi đơn ứng tuyển!");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if form is valid and complete
  const isFormComplete = Object.values(formData).every(
    (value) => value.trim() !== "",
  );
  const hasErrors = Object.values(errors).some((error) => error !== undefined);
  const isFormValid = isFormComplete && !hasErrors;

  return (
    <Card className="max-w-2xl mx-auto p-8">
      <div className="mb-8">
        <Typography variant="title-1" className="text-gray-900 mb-2">
          Ứng tuyển vị trí
        </Typography>
        <Typography variant="body-regular" className="text-gray-600">
          Vui lòng điền đầy đủ thông tin để ứng tuyển vào vị trí {jobTitle}
        </Typography>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Full Name Field */}
        <div className="space-y-2">
          <label
            htmlFor="fullName"
            className="text-sm font-medium text-gray-700"
          >
            Họ và tên <span className="text-red-500">*</span>
          </label>
          <Input
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={(e) => handleChange("fullName", e.target.value)}
            placeholder="Nhập họ và tên của bạn"
            className={errors.fullName ? "border-red-500" : ""}
          />
          {errors.fullName && (
            <p className="text-sm text-red-500">{errors.fullName}</p>
          )}
        </div>

        {/* Email Field */}
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email <span className="text-red-500">*</span>
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleChange("email", e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email}</p>
          )}
        </div>

        {/* Phone Field */}
        <div className="space-y-2">
          <label htmlFor="phone" className="text-sm font-medium text-gray-700">
            Số điện thoại <span className="text-red-500">*</span>
          </label>
          <Input
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
            placeholder="0123456789"
            className={errors.phone ? "border-red-500" : ""}
          />
          {errors.phone && (
            <p className="text-sm text-red-500">{errors.phone}</p>
          )}
        </div>

        {/* Position Field */}
        <div className="space-y-2">
          <label
            htmlFor="position"
            className="text-sm font-medium text-gray-700"
          >
            Vị trí ứng tuyển <span className="text-red-500">*</span>
          </label>
          <Input
            id="position"
            name="position"
            value={formData.position}
            onChange={(e) => handleChange("position", e.target.value)}
            placeholder="Nhập vị trí bạn muốn ứng tuyển"
            className={errors.position ? "border-red-500" : ""}
          />
          {errors.position && (
            <p className="text-sm text-red-500">{errors.position}</p>
          )}
        </div>

        {/* Experience Field */}
        <div className="space-y-2">
          <label
            htmlFor="experience"
            className="text-sm font-medium text-gray-700"
          >
            Kinh nghiệm làm việc <span className="text-red-500">*</span>
          </label>
          <textarea
            id="experience"
            name="experience"
            value={formData.experience}
            onChange={(e) => handleChange("experience", e.target.value)}
            placeholder="Mô tả kinh nghiệm làm việc của bạn..."
            rows={4}
            className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.experience ? "border-red-500" : ""
            }`}
          />
          {errors.experience && (
            <p className="text-sm text-red-500">{errors.experience}</p>
          )}
        </div>

        {/* Cover Letter Field */}
        <div className="space-y-2">
          <label
            htmlFor="coverLetter"
            className="text-sm font-medium text-gray-700"
          >
            Thư xin việc <span className="text-red-500">*</span>
          </label>
          <textarea
            id="coverLetter"
            name="coverLetter"
            value={formData.coverLetter}
            onChange={(e) => handleChange("coverLetter", e.target.value)}
            placeholder="Viết thư xin việc của bạn..."
            rows={6}
            className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.coverLetter ? "border-red-500" : ""
            }`}
          />
          {errors.coverLetter && (
            <p className="text-sm text-red-500">{errors.coverLetter}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            variant={isFormValid ? "default" : "outline"}
            className={`w-full ${
              !isFormValid
                ? "pointer-events-none opacity-60"
                : "pointer-events-auto"
            }`}
          >
            {isSubmitting ? "Đang gửi..." : "Gửi đơn ứng tuyển"}
          </Button>
        </div>
      </form>
    </Card>
  );
}

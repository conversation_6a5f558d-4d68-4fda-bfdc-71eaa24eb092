{"kind": "collectionType", "collectionName": "podcasts", "info": {"singularName": "podcast", "pluralName": "podcasts", "displayName": "Podcasts"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "file": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["audios"]}, "episode": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "program_name": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "broadcast_info_text": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "broadcast_info_socials": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.social-link", "repeatable": true}}}
"use client";

import { memo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
  CartesianGrid,
  <PERSON>,
  Scatter,
} from "recharts";
import { curveNatural, curveBasis } from "d3-shape";

type TGBIChartProps = {
  currentMonth: number;
};

const GBIChart = ({ currentMonth }: TGBIChartProps) => {
  // Tạo dữ liệu dựa trên thiết kế SVG
  const createDetailedData = () => {
    const baseData = [
      { x: 0, y_achieved: 0, y_suggested: 0, y_target: 0, y_forecast: 0 },
      { x: 1, y_achieved: 15, y_suggested: 12, y_target: 10, y_forecast: 18 },
      { x: 10, y_achieved: 60, y_suggested: 55, y_target: 50, y_forecast: 65 },
      { x: 12, y_achieved: 75, y_suggested: 70, y_target: 65, y_forecast: 80, isMilestone: true },
      { x: 36, y_achieved: 1000, y_suggested: 950, y_target: 900, y_forecast: 1050 },
      { x: 60, y_achieved: 1500, y_suggested: 1400, y_target: 1300, y_forecast: 1600 },
    ];

    // Interpolate more data points for smoother curves
    const interpolated = [];
    for (let i = 0; i < baseData.length - 1; i++) {
      const current = baseData[i];
      const next = baseData[i + 1];

      interpolated.push(current);

      // Add intermediate points between each pair
      for (let j = 1; j <= 3; j++) {
        const t = j / 4;
        interpolated.push({
          x: current.x + (next.x - current.x) * t,
          y_achieved: current.y_achieved + (next.y_achieved - current.y_achieved) * t,
          y_suggested: current.y_suggested + (next.y_suggested - current.y_suggested) * t,
          y_target: current.y_target + (next.y_target - current.y_target) * t,
          y_forecast: current.y_forecast + (next.y_forecast - current.y_forecast) * t,
        });
      }
    }
    interpolated.push(baseData[baseData.length - 1]);
    return interpolated;
  };

  const data = createDetailedData();

  // Tìm điểm milestone (12 tháng)
  const milestonePoint = data.find(point => point.x === 12);

  return (
    <ResponsiveContainer width="100%" height="90%" className="w-full h-full">
      <ComposedChart
        data={data}
        margin={{ top: 20, right: 30, left: 0, bottom: 20 }}
      >
        <defs>
          {/* Gradient cho đường tăng trưởng chính - giống SVG */}
          <linearGradient id="achievedGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#19C574" stopOpacity={0.25} />
            <stop offset="100%" stopColor="#19C574" stopOpacity={0} />
          </linearGradient>

          {/* Gradient cho đường stroke chính - giống SVG */}
          <linearGradient id="achievedStroke" x1="0" y1="0" x2="1" y2="0">
            <stop offset="0%" stopColor="#19C574" />
            <stop offset="100%" stopColor="#2AAB8A" />
          </linearGradient>

          {/* Gradient cho đường mục tiêu - giống SVG */}
          <linearGradient id="targetGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#C9C9C9" stopOpacity={0.6} />
            <stop offset="100%" stopColor="#FAFAFA" stopOpacity={0} />
          </linearGradient>

          {/* Gradient cho đường dự báo */}
          <linearGradient id="forecastGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#0C6070" stopOpacity={0.3} />
            <stop offset="100%" stopColor="#0C6070" stopOpacity={0} />
          </linearGradient>
        </defs>

        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />

        <XAxis
          dataKey="x"
          tickLine={false}
          axisLine={false}
          ticks={[0, 1, 10, 12, 36, 60]}
          tickFormatter={(x) => {
            if (x === 0) return "Hôm nay";
            if (x === 1) return "1 tháng";
            if (x === 10) return "10 tháng";
            if (x === 12) return "12 tháng";
            if (x === 36) return "36 tháng";
            if (x === 60) return "60 tháng";
            return `${x} tháng`;
          }}
        />

        <YAxis
          dataKey="y_achieved"
          tickLine={false}
          axisLine={false}
          tickSize={4}
          tickCount={6}
          tickFormatter={(val) => {
            if (val >= 1e9) return `${(val / 1e9).toFixed(1)}tỷ`;
            if (val >= 1e6) return `${(val / 1e6).toFixed(1)}tr`;
            if (val >= 1000) return `${(val / 1000).toFixed(0)}k`;
            return `${val}%`;
          }}
        />

        <Tooltip
          contentStyle={{
            backgroundColor: "#12B88E",
            color: "#fff",
            border: "none",
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
          }}
          itemStyle={{
            color: "#fff",
          }}
          formatter={(val: number, name: string) => {
            const formattedValue = val >= 1e9
              ? `${(val / 1e9).toFixed(1)} tỷ`
              : val >= 1e6
                ? `${(val / 1e6).toFixed(1)} triệu`
                : `${val}%`;
            return [formattedValue, name];
          }}
          labelFormatter={(label) => `${label} tháng`}
        />

        {/* Đường mục tiêu (xám nhạt) - giống SVG */}
        <Area
          type={curveBasis}
          dataKey="y_target"
          stroke="#E2E2E2"
          fill="url(#targetGradient)"
          strokeWidth={3}
          strokeLinecap="round"
          dot={false}
          name="Mục tiêu"
        />

        {/* Đường dự báo (đứt nét) - giống SVG */}
        <Line
          type={curveBasis}
          dataKey="y_forecast"
          stroke="#0C6070"
          strokeWidth={3}
          strokeLinecap="round"
          strokeDasharray="7.82 15.65"
          dot={false}
          name="Dự báo"
        />

        {/* Đường tăng trưởng chính (xanh lá) - giống SVG */}
        <Area
          type={curveNatural}
          dataKey="y_achieved"
          stroke="url(#achievedStroke)"
          fill="url(#achievedGradient)"
          strokeWidth={3}
          strokeLinecap="round"
          dot={false}
          name="Tăng trưởng chính"
        />

        {/* Điểm đánh dấu các mốc quan trọng - giống SVG */}
        <Scatter
          dataKey="y_achieved"
          fill="white"
          stroke="url(#achievedStroke)"
          strokeWidth={3.91}
          r={7.82}
          data={data.filter(point => [1, 10, 12, 36, 60].includes(point.x))}
        />

        {/* Điểm milestone đặc biệt (12 tháng) - giống SVG */}
        {milestonePoint && (
          <Scatter
            dataKey="y_achieved"
            fill="white"
            stroke="url(#achievedStroke)"
            strokeWidth={3.91}
            r={7.82}
            data={[milestonePoint]}
          />
        )}

        {/* Reference line cho tháng hiện tại */}
        <ReferenceLine
          x={currentMonth}
          stroke="url(#achievedStroke)"
          strokeWidth={2}
          strokeDasharray="3 3"
          label={{
            value: `${currentMonth} tháng`,
            position: "top",
            style: {
              backgroundColor: "#19C574",
              color: "#fff",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "12px",
            },
          }}
        />

        {/* Chú thích cho milestone */}
        {milestonePoint && (
          <ReferenceLine
            x={12}
            stroke="#FF6B6B"
            strokeWidth={2}
            label={{
              value: "Dự kiến tháng",
              position: "bottom",
              style: {
                backgroundColor: "#FF6B6B",
                color: "#fff",
                padding: "4px 8px",
                borderRadius: "4px",
                fontSize: "12px",
              },
            }}
          />
        )}

        {/* Chú thích cho các gói */}
        <ReferenceLine
          x={36}
          stroke="#19C574"
          strokeWidth={1}
          label={{
            value: "Gói 36 tháng",
            position: "bottom",
            style: {
              backgroundColor: "#19C574",
              color: "#fff",
              padding: "2px 6px",
              borderRadius: "4px",
              fontSize: "10px",
            },
          }}
        />

        <ReferenceLine
          x={60}
          stroke="#19C574"
          strokeWidth={1}
          label={{
            value: "Gói 60 tháng",
            position: "bottom",
            style: {
              backgroundColor: "#19C574",
              color: "#fff",
              padding: "2px 6px",
              borderRadius: "4px",
              fontSize: "10px",
            },
          }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default memo(GBIChart);

"use client";

import { memo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  ReferenceLine,
  Composed<PERSON>hart,
  CartesianGrid,
} from "recharts";
import { curveNatural, curveBasis } from "d3-shape";
import { fNumber } from "@/utils/number-format";

type TGBIChartProps = {
  series: {
    x: number;
    y_achieved?: number | null;
    y_suggested: number;
  }[];
  currentMonth: number;
};

const GBIChart = ({ series, currentMonth }: TGBIChartProps) => {
  // Interpolate more data points for smoother curves
  const interpolateData = (data: any[]) => {
    const interpolated = [];
    for (let i = 0; i < data.length - 1; i++) {
      const current = data[i];
      const next = data[i + 1];

      interpolated.push(current);

      // Add 3 intermediate points between each pair
      for (let j = 1; j <= 3; j++) {
        const t = j / 4;
        interpolated.push({
          x: current.x + (next.x - current.x) * t,
          y_achieved:
            current.y_achieved + (next.y_achieved - current.y_achieved) * t,
          y_suggested:
            current.y_suggested + (next.y_suggested - current.y_suggested) * t,
        });
      }
    }
    interpolated.push(data[data.length - 1]);
    return interpolated;
  };

  const baseData = [
    {
      x: 0,
      y_achieved: 0,
      y_suggested: 0,
    },
    ...series,
  ];

  const data = interpolateData(baseData);

  return (
    <ResponsiveContainer width="100%" height="90%" className="w-full h-full">
      <ComposedChart
        data={data}
        margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
      >
        <defs>
          {/* Gradient for achieved line */}
          <linearGradient id="achievedGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#19C574" stopOpacity={0.25} />
            <stop offset="100%" stopColor="#19C574" stopOpacity={0} />
          </linearGradient>

          {/* Gradient for suggested line */}
          <linearGradient id="suggestedGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#C9C9C9" stopOpacity={0.6} />
            <stop offset="100%" stopColor="#FAFAFA" stopOpacity={0} />
          </linearGradient>

          {/* Gradient for achieved stroke */}
          <linearGradient id="achievedStroke" x1="0" y1="0" x2="1" y2="0">
            <stop offset="0%" stopColor="#19C574" />
            <stop offset="100%" stopColor="#2AAB8A" />
          </linearGradient>
        </defs>

        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />

        <XAxis
          dataKey="x"
          tickLine={false}
          axisLine={false}
          ticks={[0, 36]}
          tickFormatter={(x) => (x === 0 ? "Hôm nay" : `${x} tháng`)}
        />

        <YAxis
          dataKey="y_achieved"
          tickLine={false}
          axisLine={false}
          tickSize={4}
          tickCount={4}
          tickFormatter={(val) =>
            val >= 1e9
              ? `${(val / 1e9).toFixed(1)}tỷ`
              : val >= 1e6
                ? `${(val / 1e6).toFixed(1)}tr`
                : val
          }
        />

        <Tooltip
          contentStyle={{
            backgroundColor: "#12B88E",
            color: "#fff",
            border: "none",
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
          }}
          itemStyle={{
            color: "#fff",
          }}
          formatter={(val: number) => fNumber(val)}
          labelFormatter={(label) => `${label} tháng`}
        />

        {/* Achieved area with natural curve */}
        <Area
          type={curveNatural}
          dataKey="y_achieved"
          stroke="url(#achievedStroke)"
          fill="url(#achievedGradient)"
          strokeWidth={3}
          dot={false}
          name="Dự kiến đạt được"
        />

        {/* Suggested area with basis curve */}
        <Area
          type={curveBasis}
          dataKey="y_suggested"
          stroke="#A4BBBF"
          fill="url(#suggestedGradient)"
          strokeWidth={3}
          strokeDasharray="7.82 15.65"
          dot={false}
          name="Gợi ý"
        />

        {/* Current month reference line */}
        <ReferenceLine
          x={currentMonth + 1}
          stroke="url(#achievedStroke)"
          strokeWidth={3}
          label={{
            value: `${currentMonth} tháng`,
            position: "top",
            style: {
              backgroundColor: "#19C574",
              color: "#fff",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "12px",
            },
          }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default memo(GBIChart);

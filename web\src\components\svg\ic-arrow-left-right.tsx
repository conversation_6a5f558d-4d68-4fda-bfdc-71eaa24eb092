type ArrowLeftRightIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const ArrowLeftRightIcon = ({
  size = 24,
  className,
  color = "#00C694",
}: ArrowLeftRightIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 18C3.44772 18 3 17.5523 3 17C3 16.4477 3.44772 16 4 16H16.5859L17.5858 16.9999L16.5857 18H4ZM7.41431 8L6.41421 6.99991L7.41412 6L20 6C20.5523 6 21 6.44772 21 7C21 7.55228 20.5523 8 20 8H7.41431Z"
        fill="#959A9E"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.29289 7.70726C3.90237 7.31673 3.90237 6.68357 4.29289 6.29304L9 1.58594L10.4142 3.00015L6.41421 7.00015L10.4142 11.0002L9 12.4144L4.29289 7.70726ZM19.7071 17.7073C20.0976 17.3167 20.0976 16.6836 19.7071 16.293L15 11.5859L13.5858 13.0002L17.5858 17.0002L13.5858 21.0002L15 22.4144L19.7071 17.7073Z"
        fill={color}
      />
    </svg>
  );
};

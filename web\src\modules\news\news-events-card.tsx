import { cn } from "#/src/lib";
import { TNewsEvents } from "#/src/types/news-event";
import { fDate } from "#/src/utils/date-format";
import { AspectRatio, Separator, Typography } from "@/components/ui";
import Image from "next/image";
import Link from "next/link";

type TProps = {
  item: TNewsEvents;
  isFeatured?: boolean;
  largeTitle?: boolean;
  type?: "vertical" | "horizontal" | "text-only";
  hideDescription?: boolean;
  classname?: string;
  contentClassname?: string;
  handle?: string;
  searchKeyword?: string;
};

const NewsEventsCard = (props: TProps) => {
  const {
    item,
    type = "vertical",
    hideDescription = false,
    isFeatured = false,
    largeTitle = false,
    classname,
    contentClassname,
    handle = "/news-events",
    searchKeyword,
  } = props;
  const { title, description, category, image, created_at } = item;

  const isHorizontal = type === "horizontal";
  const isTextOnly = type === "text-only";
  const isVertical = type === "vertical";

  const ratio = isHorizontal ? 3 / 2 : 2 / 1;

  const _highlightKeyword = (text: string, keyword?: string) => {
    if (!keyword || !keyword.trim()) {
      return text;
    }

    const regex = new RegExp(`(${keyword.trim()})`, "gi");
    const parts = text.split(regex);

    return parts.map((part, index) => {
      if (part.toLowerCase() === keyword.toLowerCase()) {
        return (
          <span key={index} className="text-brand">
            {part}
          </span>
        );
      }
      return part;
    });
  };

  const renderImage = (
    <div className="w-full">
      <AspectRatio ratio={ratio}>
        <Image
          src={image.src}
          alt={title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={cn("object-cover", isHorizontal && "rounded-xl")}
        />
      </AspectRatio>
    </div>
  );
  // Render Content Horiziontal
  const renderContentHorizontal = (
    <div
      className={cn(
        "flex flex-col gap-3",
        "w-full",
        isTextOnly ? "!p-6" : "!py-4 !px-0 container-inner",
        contentClassname,
      )}
    >
      <Typography variant="body-bold" className="text-placeholder">
        {category?.name}
      </Typography>
      <Typography
        variant={isFeatured || largeTitle ? "title-0" : "title-3"}
        className={cn(
          "group-hover:text-brand transition-all duration-300 ",
          isTextOnly ? "line-clamp-2" : "line-clamp-1",
        )}
      >
        {_highlightKeyword(title, searchKeyword)}
      </Typography>

      {!hideDescription && (
        <Typography className="text-placeholder line-clamp-2">
          {description}
        </Typography>
      )}

      <Typography variant="body-bold" className="text-placeholder">
        {fDate(created_at)}
      </Typography>
    </div>
  );

  const renderContentVertical = (
    <div
      className={cn(
        "flex flex-col gap-2",
        isTextOnly ? "py-6" : " container-inner",
        contentClassname,
      )}
    >
      <Typography variant="body-bold" className="text-placeholder">
        {category?.name}
      </Typography>
      <Typography
        variant={isFeatured || largeTitle ? "title-0" : "title-3"}
        className={cn(
          "group-hover:text-brand transition-all duration-300 line-clamp-2",
        )}
      >
        {_highlightKeyword(title, searchKeyword)}
      </Typography>

      {!hideDescription && (
        <Typography className="text-placeholder line-clamp-2">
          {description}
        </Typography>
      )}

      <Typography variant="body-bold" className="text-placeholder mt-4">
        {fDate(created_at)}
      </Typography>
    </div>
  );

  const renderHorizontal = (
    <>
      <div className="w-full py-6">
        <Link href={`${handle}/${item.slug}`}>
          <div className="grid grid-cols-[5fr_8fr] gap-8">
            {renderImage}

            {renderContentHorizontal}
          </div>
        </Link>
      </div>

      <Separator className="my-[0.5]" />
    </>
  );

  const renderVertical = (
    <Link href={`${handle}/${item.slug}`}>
      {renderImage}

      {renderContentVertical}
    </Link>
  );

  const renderTextOnly = (
    <>
      <Link href={`${handle}/${item.slug}`}>{renderContentHorizontal}</Link>
    </>
  );

  const viewMap = {
    vertical: renderVertical,
    horizontal: renderHorizontal,
    "text-only": renderTextOnly,
  };

  return (
    <div
      className={cn(
        "rounded-lg overflow-hidden h-full group",
        isVertical && "bg-white",
        classname,
      )}
    >
      {viewMap[type as keyof typeof viewMap]}
    </div>
  );
};

export default NewsEventsCard;

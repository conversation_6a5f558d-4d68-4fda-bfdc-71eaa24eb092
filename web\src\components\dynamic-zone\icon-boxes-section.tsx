"use client";

import {
  Button,
  Carousel,
  CarouselContent,
  CarouselItem,
  Typography,
} from "@components/ui";
import { useCarousel } from "@/hooks";
import { ArrowLeft, ArrowRight } from "lucide-react";
import IconBoxCard from "@components/card/icon-box-card";

//----------------------------------------------------------------------------------
type TProps = {
  data: any;
};

//----------------------------------------------------------------------------------
export default function IconBoxesSection({ data }: TProps) {
  const { heading, sub_heading, buttons, icon_box_list } = data || {};
  // const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }));

  const {
    setApi,
    isAtEnd,
    scrollNext,
    scrollPrev,
    canScrollNext,
    canScrollPrev,
  } = useCarousel();

  return (
    <section className="relative container !py-0">
      <div className="flex flex-col items-center gap-6 md:gap-10">
        <div className="flex flex-col gap-6 max-w-[635px] text-center">
          {heading ? (
            <Typography
              variant="large-title"
              classname="text-center max-md:text-2xl max-lg:text-3xl"
            >
              {heading}
            </Typography>
          ) : null}
          {sub_heading ? <Typography>{sub_heading}</Typography> : null}
        </div>
        {buttons?.length ? (
          <div className="flex items-center gap-x-6">
            {buttons?.map((item: any, idx: number) => (
              <Button key={idx} variant={"outline"}>
                {item?.text}
              </Button>
            ))}
          </div>
        ) : null}

        <div className="ml-[calc((100vw-100%)/2)] w-[calc(100%+((100vw-100%)/2))] h-full">
          <Carousel
            setApi={setApi}
            // plugins={[plugin.current]}
            opts={{
              align: "start",
              loop: false,
            }}
            // onMouseEnter={plugin.current.stop}
            // onMouseLeave={plugin.current.reset}
            className="w-full relative"
          >
            <CarouselContent className="gap-4">
              {icon_box_list?.map((item: any, idx: number) => (
                <CarouselItem
                  key={idx}
                  className="basis-3/4 md:basis-3/8 lg:basis-4/15"
                >
                  <IconBoxCard item={item} />
                </CarouselItem>
              ))}
            </CarouselContent>

            {!isAtEnd ? (
              <div
                className="hidden md:block absolute z-20 h-[480px] w-20 right-0 top-1/2 -translate-y-1/2"
                style={{
                  background:
                    "linear-gradient(270deg, #F6F7F8 46.59%, rgba(246, 247, 248, 0) 100%)",
                }}
              ></div>
            ) : null}
          </Carousel>
        </div>
        <div className="w-full flex items-center justify-end gap-4">
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={scrollPrev}
            disabled={!canScrollPrev}
            className="bg-[#F1F3F5] hover:bg-black hover:text-white rounded-full w-12 h-12"
          >
            <ArrowLeft className="size-6" />
          </Button>
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={scrollNext}
            disabled={!canScrollNext}
            className="bg-[#F1F3F5] hover:bg-black hover:text-white rounded-full w-12 h-12"
          >
            <ArrowRight className="size-6" />
          </Button>
        </div>
      </div>
    </section>
  );
}

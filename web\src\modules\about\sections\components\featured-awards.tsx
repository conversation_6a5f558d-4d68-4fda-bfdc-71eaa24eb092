import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "#/src/components/ui";
import Image from "next/image";
import Link from "next/link";

export default function FeaturedAwards({ items }: { items: any }) {
  const newItems = items.slice(0, 3);
  return (
    <div className="container !py-0">
      <div className="relative w-full h-full rounded-2xl overflow-hidden min-h-[600px] md:min-h-[446px] p-6 lg:p-10">
        {/* desktop*/}
        <div
          className="w-full h-full absolute top-0 left-0 z-10 hidden lg:flex max-h-[446px]"
          style={{
            background:
              "linear-gradient(124deg, rgba(255, 255, 255, 0.20) 26.44%, rgba(239, 239, 239, 0.20) 74.01%, rgba(170, 170, 170, 0.20) 90.27%)",
          }}
        ></div>
        <div
          className="w-full h-full absolute top-0 left-0 z-5 hidden lg:flex"
          style={{
            background: `
      linear-gradient(33deg, rgba(255, 255, 255, 0.00) 55.24%, rgba(255, 255, 255, 0.20) 109.51%),
      linear-gradient(275deg, rgba(255, 255, 255, 0.00) 22.27%, #FFF 68.98%),
      url('/images/about/awards-banner.png') lightgray 50% / cover no-repeat
    `,
            backgroundColor: "#FCFCFC",
          }}
        ></div>
        {/* tablet*/}
        <div
          className="w-full h-1/2 absolute bottom-0 left-0 z-5 max-[744px]:hidden lg:hidden"
          style={{
            background: `
      linear-gradient(33deg, rgba(255, 255, 255, 0.00) 55.24%, rgba(255, 255, 255, 0.20) 109.51%),
      linear-gradient(275deg, rgba(255, 255, 255, 0.00) 22.27%, #FFF 68.98%),
      url('/images/about/awards-banner.png') lightgray -100.875px -0.172px / 133.511% 100.179% no-repeat
    `,
            backgroundColor: "#FCFCFC",
          }}
        ></div>
        <div
          className="absolute w-full h-full top-0 left-0  max-[744px]:hidden lg:hidden"
          style={{
            background: "linear-gradient(180deg, #FFF 0%, #FBFBFB 36.01%)",
          }}
        ></div>
        <div
          className="absolute w-full h-full top-0 left-0 z-10  max-[744px]:hidden lg:hidden"
          style={{
            background:
              "linear-gradient(180deg, rgba(255, 255, 255, 0.20) -14.72%, rgba(239, 239, 239, 0.20) 81.79%, rgba(170, 170, 170, 0.20) 114.79%)",
          }}
        ></div>
        {/* mobile*/}
        <div
          className="absolute w-full h-full top-0 left-0 z-10 md:hidden"
          style={{
            background:
              "linear-gradient(180deg, rgba(255, 255, 255, 0.20) -14.72%, rgba(239, 239, 239, 0.20) 81.79%, rgba(170, 170, 170, 0.20) 114.79%)",
          }}
        ></div>
        <Image
          src={"/images/about/awards-banner-mobile.png"}
          alt="awards banner"
          sizes="100vw"
          width={0}
          height={0}
          className="w-full h-full object-cover md:hidden absolute top-0 left-0"
        />
        <div className="flex flex-col gap-6 relative z-12 max-w-[540px]">
          <div className="flex flex-col gap-6 ">
            <Typography variant="title-1" classname="text-default font-bold">
              Giải thưởng nổi bật
            </Typography>
            <div className="flex flex-col gap-4">
              {newItems.map((item: any, index: number) => (
                <div key={index} className="flex flex-col gap-4">
                  <div className="flex flex-col gap-1">
                    <Typography
                      variant="title-3"
                      classname="text-default max-lg:text-base max-lg:leading-6"
                    >
                      {item.title}
                    </Typography>
                    <Typography
                      variant="body-medium"
                      classname="text-placeholder"
                    >
                      {item.category}
                    </Typography>
                  </div>
                  {index !== newItems.length - 1 && (
                    <Separator
                      orientation="horizontal"
                      className="w-full h-[1px] bg-subtle"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
          <Link href={"/awards"}>
            <Button
              variant={"outline"}
              className="group !border-0 hover:!bg-transparent w-fit px-0 py-2"
            >
              <Typography
                variant="body-medium"
                classname="text-emphasize group-hover:text-brand"
              >
                Xem thêm
              </Typography>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

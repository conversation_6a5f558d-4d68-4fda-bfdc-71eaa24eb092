"use client";

import React, { createContext, useContext, useReducer } from "react";

type State = {
  locale: string;
  globalData: any;
  navigationData: any;
  topbarData: any;
};

type Action = {
  type: "SET_GLOBAL_DATA" | "SET_NAVIGATION_DATA" | "SET_TOPBAR_DATA";
  locale: string;
  globalData?: any;
  navigationData?: any;
  topbarData?: any;
};

const SiteContext = createContext<{
  state: State;
  dispatch: React.Dispatch<Action>;
} | null>(null);

const siteReducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "SET_GLOBAL_DATA":
      return {
        ...state,
        globalData: {
          ...state.globalData,
          ...action.globalData,
        },
      };
    case "SET_NAVIGATION_DATA":
      return {
        ...state,
        navigationData: {
          ...state.navigationData,
          ...action.navigationData,
        },
      };
    case "SET_TOPBAR_DATA":
      return {
        ...state,
        topbarData: {
          ...state.topbarData,
          ...action.topbarData,
        },
      };
    default:
      return state;
  }
};

const initialState: State = {
  locale: "",
  globalData: {},
  navigationData: {},
  topbarData: {},
};

export const SiteProvider = ({ children }: { children: React.ReactNode }) => {
  const [state, dispatch] = useReducer(siteReducer, initialState);

  return (
    <SiteContext.Provider value={{ state, dispatch }}>
      {children}
    </SiteContext.Provider>
  );
};

export const useSiteContext = () => {
  const context = useContext(SiteContext);
  if (!context) {
    throw new Error("useSiteContext must be used within a SiteProvider");
  }
  return context;
};

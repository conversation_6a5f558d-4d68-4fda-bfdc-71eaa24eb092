"use client";
import { RESEARCH_CARD, SEARCH_CONTENT } from "#/src/__mock__/research";
import NoToggleCard from "#/src/components/card/no-toggle-card";
import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Typography,
} from "#/src/components/ui";
import { cn } from "#/src/lib";
import { useSearchStore } from "#/src/store/use-open-search-store";
import { Search } from "lucide-react";
import { useEffect, useRef, useState } from "react";

const AnalysisCenter = () => {
  const searchRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<number>(0);
  const { openSearch, setOpenSearch } = useSearchStore();
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [debouncedSearchKeyword, setDebouncedSearchKeyword] =
    useState<string>("");
  const [searchContents, setSearchContents] = useState(SEARCH_CONTENT);
  useEffect(() => {
    document.body.style.overflow = openSearch ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [openSearch]);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchKeyword(searchKeyword);
    }, 300);
    return () => clearTimeout(handler);
  }, [searchKeyword]);
  useEffect(() => {
    if (debouncedSearchKeyword.length === 0) {
      setSearchContents(SEARCH_CONTENT);
      return;
    }
    const filteredSearchContent = SEARCH_CONTENT.filter((content) =>
      content.title
        .toLowerCase()
        .includes(debouncedSearchKeyword.toLowerCase()),
    );
    if (filteredSearchContent) {
      setSearchContents(filteredSearchContent);
    } else {
      setSearchContents([]);
    }
  }, [debouncedSearchKeyword]);
  useEffect(() => {
    if (searchRef.current) {
      setWidth(searchRef.current.offsetWidth);
    }
  }, []);
  return (
    <div className="w-full flex flex-col gap-10 container !max-w-[1066px] mx-auto">
      {openSearch && (
        <div className="w-screen h-screen fixed inset-0 bg-black/50 z-20"></div>
      )}
      <div className="flex flex-col gap-6 text-center ">
        <div className="flex flex-col gap-4">
          <Typography
            variant="special-title"
            classname="text-black max-lg:text-3xl max-md:text-4xl"
          >
            Trung tâm phân tích
          </Typography>
          <Typography variant="body-regular" classname="text-black">
            Cung cấp phân tích chuyên sâu từ nhiều góc nhìn về thị trường và
            doanh nghiệp
          </Typography>
        </div>

        <Popover>
          <PopoverTrigger
            asChild
            className="focus-visible:ring-brand focus-visible:ring-[1px]"
          >
            <div
              ref={searchRef}
              className={cn(
                "w-full max-w-[454px] mx-auto h-fit relative z-10 ",
                openSearch && "z-50",
              )}
              onClick={(e) => {
                e.stopPropagation();
                setOpenSearch(true);
              }}
            >
              <Input
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="w-full min-h-14 border-border-button rounded-xl pl-[51px] bg-white  relative z-20 placeholder:text-sm placeholder:text-disabled placeholder:font-medium"
                placeholder="Tìm kiếm"
              />
              <Search
                className={cn(
                  "size-5 text-icon-placeholder top-1/2 -translate-y-1/2 absolute left-4 z-30",
                )}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent
            className="bg-white"
            onClick={(e) => e.stopPropagation()}
            style={{
              width,
            }}
          >
            <div className="hide-scrollbar-arrows w-full h-auto max-h-[400px] lg:max-h-[430px] 2xl:max-h-[628px] overflow-y-auto rounded-b-xl py-6 px-4 flex flex-col gap-6 bg-white border-0 z-10  text-start">
              <Typography variant="body-medium" classname="text-placeholder">
                Gợi ý
              </Typography>
              {searchContents.length === 0 ? (
                <div className="w-full py-6 flex justify-center items-center">
                  <Typography
                    variant="subheadline-regular"
                    classname="text-default"
                  >
                    Không tìm thấy dữ liệu
                  </Typography>
                </div>
              ) : (
                searchContents.map((content, index) => (
                  <SearchContentItem key={index} {...content} />
                ))
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
        {RESEARCH_CARD.map((research, index) => (
          <NoToggleCard key={index} item={research} />
        ))}
      </div>
    </div>
  );
};

const SearchContentItem = ({
  title,
  publishedDate,
}: {
  title: string;
  publishedDate: string;
}) => {
  return (
    <div className="flex flex-col gap-2">
      <Typography variant="headline" classname="text-default">
        {title}
      </Typography>
      <Typography variant="subheadline-regular" classname="text-default">
        {publishedDate}
      </Typography>
    </div>
  );
};

export default AnalysisCenter;

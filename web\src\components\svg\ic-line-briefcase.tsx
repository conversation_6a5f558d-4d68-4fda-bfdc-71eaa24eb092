import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: "round" | "butt" | "square" | "inherit";
  strokeLinejoin?: "round" | "inherit" | "miter" | "bevel";
  className?: string;
};

const IcLineBriefcase = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  strokeWidth = 1,
  strokeLinecap = "round",
  strokeLinejoin = "round",
  className,
}: TProps) => {
  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M20 52.5V17.5C20 15.1751 20 14.0126 20.2556 13.0589C20.9491 10.4707 22.9707 8.44906 25.5589 7.75556C26.5126 7.5 27.6751 7.5 30 7.5C32.3249 7.5 33.4874 7.5 34.4411 7.75556C37.0293 8.44906 39.0509 10.4707 39.7444 13.0589C40 14.0126 40 15.1751 40 17.5V52.5M13 52.5H47C49.8003 52.5 51.2004 52.5 52.27 51.955C53.2108 51.4757 53.9757 50.7108 54.455 49.77C55 48.7004 55 47.3003 55 44.5V25.5C55 22.6997 55 21.2996 54.455 20.23C53.9757 19.2892 53.2108 18.5243 52.27 18.045C51.2004 17.5 49.8003 17.5 47 17.5H13C10.1997 17.5 8.79961 17.5 7.73005 18.045C6.78924 18.5243 6.02433 19.2892 5.54497 20.23C5 21.2996 5 22.6997 5 25.5V44.5C5 47.3003 5 48.7004 5.54497 49.77C6.02433 50.7108 6.78924 51.4757 7.73005 51.955C8.79961 52.5 10.1997 52.5 13 52.5Z"
        stroke="currentColor"
        strokeWidth={strokeWidth}
        strokeLinecap={strokeLinecap}
        strokeLinejoin={strokeLinejoin}
      />
    </SvgIcon>
  );
};

export default IcLineBriefcase;

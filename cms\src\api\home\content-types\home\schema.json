{"kind": "singleType", "collectionName": "homes", "info": {"singularName": "home", "pluralName": "homes", "displayName": "/home"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string"}, "dynamic_zone": {"type": "dynamiczone", "components": ["dynamic-zone.trading-platforms", "dynamic-zone.toggle-cards", "dynamic-zone.terms-and-conditions", "dynamic-zone.team-members", "dynamic-zone.support-section", "dynamic-zone.support-center", "dynamic-zone.promotion-banner", "dynamic-zone.promotion-list", "dynamic-zone.page-banner", "dynamic-zone.outstanding-awards", "dynamic-zone.news-events-list", "dynamic-zone.margin-trading", "dynamic-zone.main-page-hero", "dynamic-zone.latest-news-events", "dynamic-zone.latest-market-insights", "dynamic-zone.knowledge-list", "dynamic-zone.kafi-podcast", "dynamic-zone.kafi-partners-section", "dynamic-zone.k-point-tool", "dynamic-zone.k-point-ratio", "dynamic-zone.investment-simulator", "dynamic-zone.investment-portfolio", "dynamic-zone.icon-box-slider", "dynamic-zone.icon-box-grid", "dynamic-zone.featured-news-events", "dynamic-zone.faqs", "dynamic-zone.cta-section", "dynamic-zone.certification-list", "dynamic-zone.branch-list", "dynamic-zone.become-partner-guide", "dynamic-zone.analytics-center", "dynamic-zone.video-section", "dynamic-zone.recruitment-section"]}, "seo": {"type": "component", "component": "shared.seo", "repeatable": false}}}
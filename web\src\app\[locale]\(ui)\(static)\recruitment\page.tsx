import { RecruitmentView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Recruitment - <PERSON><PERSON>",
  description: "Recruitment - <PERSON><PERSON>",
};

const RecruitmentPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <RecruitmentView />
    </Suspense>
  );
};

export default RecruitmentPage;

export const dynamic = "force-dynamic";

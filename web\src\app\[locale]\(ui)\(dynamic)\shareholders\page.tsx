import ShareholdersView from "#/src/modules/shareholders/shareholders-view";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";
//-----------------------------------------------------------
export const metadata: Metadata = {
  title: "Quan hệ cổ đông - Kafi",
  description: "Quan hệ cổ đông - Kafi",
};
//-----------------------------------------------------------

export default async function ShareholdersPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <ShareholdersView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

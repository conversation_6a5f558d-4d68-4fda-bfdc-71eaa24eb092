{"kind": "collectionType", "collectionName": "researches", "info": {"singularName": "research", "pluralName": "researches", "displayName": "Research"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title", "required": true}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "document": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": true, "allowedTypes": ["images", "videos", "audios", "files"]}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "research_category": {"type": "relation", "relation": "manyToOne", "target": "api::research-category.research-category", "inversedBy": "research"}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}}}
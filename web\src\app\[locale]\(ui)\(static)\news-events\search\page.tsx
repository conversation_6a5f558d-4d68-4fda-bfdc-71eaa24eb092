import { NewsEventsSearchView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "News & Events Search - Kafi",
  description: "News & Events Search - Kafi",
};

const NewsEventsSearchPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <NewsEventsSearchView />
    </Suspense>
  );
};

export default NewsEventsSearchPage;

export const dynamic = "force-dynamic";

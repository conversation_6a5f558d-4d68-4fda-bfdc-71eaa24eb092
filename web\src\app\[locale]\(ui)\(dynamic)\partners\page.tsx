import { PartnerView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Partners - Ka<PERSON>",
  description: "Partners - Kafi",
};

export default async function PartnersPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <PartnerView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

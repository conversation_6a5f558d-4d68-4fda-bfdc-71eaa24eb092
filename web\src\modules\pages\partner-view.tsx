import { PARTNER_FAQ, PARTNER_HERO_SECTION } from "#/src/__mock__";
import PageHeroSection from "#/src/components/dynamic-zone/page-hero-section";
import {
  KPointRatio,
  KPointsCalculator,
  BecomePartner,
  PartnerPageBenefit,
} from "../partners";
import { SupportInfo, Faqs } from "../shared";

export default function PartnerView() {
  return (
    <div className="flex flex-col gap-20">
      <PageHeroSection data={PARTNER_HERO_SECTION} />

      <PartnerPageBenefit />

      <KPointRatio />

      <div data-id="k-points-calculator-section">
        <KPointsCalculator />
      </div>

      <BecomePartner />

      <Faqs dataFAQ={PARTNER_FAQ} />

      <SupportInfo />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

import { Typography } from "#/src/components/ui";
import Image from "next/image";

interface ContactInfoProp {
  name: string;
  phone: string;
  email: string;
  text: string;
  icon: string;
}

export default function ContactInfoView({
  contactList,
}: {
  contactList: ContactInfoProp[];
}) {
  return (
    <div className="flex flex-col container !py-0 gap-10">
      <Typography
        variant="large-title"
        classname="text-emphasize max-lg:text-3xl max-md:text-2xl max-md:font-semibold"
      >
        Thông tin liên hệ
      </Typography>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {contactList.map((contact, index) => (
          <ContactCard key={index} {...contact} />
        ))}
      </div>
    </div>
  );
}

const ContactCard = ({ name, phone, email, text, icon }: ContactInfoProp) => {
  const hasPhone = phone && phone.length > 0;
  const hasEmail = email && email.length > 0;
  return (
    <div className="p-6 border border-subtle rounded-3xl">
      <div className="flex flex-col">
        <div className="w-full flex justify-between items-center py-3">
          <Typography variant="title-3" classname="text-emphasize">
            {name}
          </Typography>
          <Image
            src={icon}
            alt={name}
            width={0}
            height={0}
            sizes="100vw"
            className="w-8 h-auto aspect-square"
          />
        </div>
        <div className="py-3 flex items-center gap-2 group">
          {hasPhone && hasEmail && (
            <>
              <Typography
                variant="button-label"
                classname="text-emphasize group-hover:text-brand transition-colors duration-200"
              >
                {phone}
              </Typography>
              <Typography
                variant="body-regular"
                classname="text-placeholder group-hover:text-brand transition-colors duration-200"
              >
                |
              </Typography>
              <Typography
                variant="button-label"
                classname="text-emphasize group-hover:text-brand transition-colors duration-200"
              >
                {email}
              </Typography>
            </>
          )}
          <Typography
            variant="button-label"
            classname="text-emphasize group-hover:text-brand transition-colors duration-200"
          >
            {text}
          </Typography>
        </div>
      </div>
    </div>
  );
};

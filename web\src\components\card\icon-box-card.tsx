import { Typography } from "@components/ui";
import Image from "next/image";

//----------------------------------------------------------------------------------
type CardProps = {
  item: any;
};
//----------------------------------------------------------------------------------
export default function IconBoxCard({ item }: CardProps) {
  const renderIcon = (
    <div className="w-full flex">
      <Image
        src={item?.icon}
        alt="icon"
        width={0}
        height={0}
        sizes="100vw"
        className="w-15 aspect-square h-auto"
      />
    </div>
  );

  return (
    <div className="h-full p-8 rounded-[20px] bg-white flex flex-col justify-between gap-20 relative">
      {renderIcon}
      <div className="w-full flex flex-col gap-5">
        <Typography variant={"title-2"}>{item?.title}</Typography>
        <Typography>{item?.sub_title}</Typography>
      </div>
      <div
        className="w-[280px] h-auto aspect-square absolute rounded-full "
        style={{
          left: "90px",
          top: "-90px",
          opacity: 0.05,
          background:
            "radial-gradient(circle at center, rgba(198, 0, 172, 0.5) 0%, rgba(18, 184, 142, 0.5) 50%, transparent 80%)",
          // filter: "blur(107.80000305175781px)"
        }}
      ></div>
    </div>
  );
}

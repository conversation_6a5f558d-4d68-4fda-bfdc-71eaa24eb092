{"kind": "collectionType", "collectionName": "term_condition_documents", "info": {"singularName": "term-condition-document", "pluralName": "term-condition-documents", "displayName": "Term Condition-Document"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "issued_date": {"type": "date", "pluginOptions": {"i18n": {"localized": true}}}, "file": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["files"]}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::term-condition-category.term-condition-category", "inversedBy": "documents"}}}
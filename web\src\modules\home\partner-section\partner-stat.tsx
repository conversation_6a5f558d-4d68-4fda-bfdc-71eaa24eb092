"use client";

import {
  <PERSON><PERSON>,
  AvatarFallback,
  AvatarImage,
  <PERSON>ton,
  Separator,
  Typo<PERSON>,
} from "@components/ui";
import { useMediaQuery } from "@/hooks";
import Link from "next/link";

const stats = {
  payment: {
    value: "5,552",
    suffix: "",
    description: "<PERSON><PERSON><PERSON><PERSON> khách giới thiệu trong tháng",
  },
  user: {
    value: "4.4",
    suffix: "tỷ",
    description: "K-Points trung bình thanh toán tháng",
  },
};

const PartnerStat = () => {
  const isDesktop = useMediaQuery("(min-width: 1024px)");

  return (
    <div className="flex justify-center w-full flex-col gap-6 md:max-w-[427px] mx-auto lg:max-w-[601px]">
      {/*use headeer section later */}
      <Typography
        variant="large-title"
        classname="text-center max-lg:text-3xl max-md:text-2xl"
      >
        Ch<PERSON><PERSON><PERSON> tr<PERSON><PERSON>
      </Typography>
      <div className="*:data-[slot=avatar]:ring-background flex -space-x-7 lg:-space-x-14 justify-center">
        <Avatar className="w-[78px] h-[78px] lg:w-32 lg:h-32">
          <AvatarImage
            src="/images/avatars/avatar-1.png"
            alt="partner-avatar"
          />
          <AvatarFallback>AV</AvatarFallback>
        </Avatar>
        <Avatar className="w-[78px] h-[78px] lg:w-32 lg:h-32">
          <AvatarImage
            src="/images/avatars/avatar-2.png"
            alt="partner-avatar"
          />
          <AvatarFallback>AV</AvatarFallback>
        </Avatar>
        <Avatar className="w-[78px] h-[78px] lg:w-32 lg:h-32">
          <AvatarImage
            src="/images/avatars/avatar-3.png"
            alt="partner-avatar"
          />
          <AvatarFallback>AV</AvatarFallback>
        </Avatar>
        <Avatar className="w-[78px] h-[78px] lg:w-32 lg:h-32">
          <AvatarImage
            src="/images/avatars/avatar-4.png"
            alt="partner-avatar"
          />
          <AvatarFallback>AV</AvatarFallback>
        </Avatar>
        <Avatar className="w-[78px] h-[78px] lg:w-32 lg:h-32">
          <AvatarImage
            src="/images/avatars/avatar-plus.png"
            alt="partner-avatar"
          />
          <AvatarFallback>AV</AvatarFallback>
        </Avatar>
      </div>
      {/* <Image
        alt="partners avatar"
        src={"/images/home/<USER>"}
        sizes="100vw"
        width={0}
        height={0}
        className="w-full max-w-[262px] lg:max-w-[433px] h-auto mx-auto"
      /> */}
      <div
        style={{
          boxShadow: "0px 6.79px 19.24px -5.66px #3A4DE926",
        }}
        className="w-full h-full py-4 grid grid-cols-2 rounded-2xl px-5.5 gap-6 bg-white lg:grid-cols-1 lg:gap-3.5 min-h-[131px]"
      >
        <div className="flex flex-col gap-2.5 h-full lg:grid lg:grid-cols-6 lg:px-4">
          <Typography
            variant="title-1"
            className="text-black max-lg:text-2xl font-semibold lg:col-span-2"
          >
            {stats.payment.value} {stats.payment.suffix}
          </Typography>
          <div className="w-full h-full lg:col-span-4 lg:flex lg:items-center">
            <Typography className="font-normal text-sm text-black opacity-50 text-start text-balance">
              {stats.payment.description}{" "}
            </Typography>
          </div>
        </div>
        {isDesktop && (
          <div className="col-span-1">
            <Separator
              orientation={"horizontal"}
              className="h-full mx-auto lg:!h-[0.5px] lg:w-full"
            />
          </div>
        )}
        <div className="flex flex-col gap-2.5 h-full lg:grid lg:grid-cols-6 border-l border-[#AFAFAF] lg:border-0 pl-6 md:px-4">
          <Typography
            variant="title-1"
            className="text-black max-lg:text-2xl font-semibold lg:col-span-2"
          >
            {stats.user.value} {stats.user.suffix}
          </Typography>
          <div className="w-full h-full lg:col-span-4 lg:flex lg:items-center">
            <Typography className="font-normal text-sm text-black opacity-50 text-start ">
              {stats.user.description}{" "}
            </Typography>
          </div>
        </div>
      </div>
      <Link href={"#"} className=" mx-auto">
        <Button variant="outline">Tìm hiểu thêm</Button>
      </Link>
    </div>
  );
};

export default PartnerStat;

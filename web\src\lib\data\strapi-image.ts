import { unstable_noStore as noStore } from "next/cache";
import { APP_CONFIG } from "@/config-global";

const DEFAULT_API_URL = APP_CONFIG.apiUrl;

export function strapiImage(url: string, apiUrl?: string): string {
  noStore();

  if (!url) return "";

  // If URL is already absolute, return as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // If URL is relative (starts with /), prepend API URL
  if (url.startsWith("/")) {
    const baseUrl =
      apiUrl ||
      (typeof process !== "undefined" && DEFAULT_API_URL) ||
      DEFAULT_API_URL;
    return baseUrl + url;
  }

  // Return URL as is for other cases
  return url;
}

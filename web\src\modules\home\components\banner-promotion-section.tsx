"use client";

import {
  ArrowLeftRightIcon,
  BarChartIcon,
  LineArrowNarrowLeftIcon,
  LineArrowNarrowRightIcon,
  PieChartIcon,
  StackIcon,
} from "@components/svg";
import { useIsMobile } from "@/hooks";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import { useEffect, useState } from "react";
import { cn } from "#/src/lib";
import { Button, Typography } from "#/src/components/ui";

interface FeatureItem {
  icon: React.ReactNode;
  title: string;
}

interface SlideData {
  title: string;
  image: string;
  features: FeatureItem[];
}

// FIXME: move to mock
const slidesData: SlideData[] = [
  {
    title: "Thông tin thị trường chuyên sâu trên hệ thống Kafi",
    image: "/images/home/<USER>",
    features: [
      {
        icon: <BarChartIcon size={24} />,
        title: "Phân tích thị trường chuyên sâu",
      },
      {
        icon: <ArrowLeftRightIcon size={24} />,
        title: "Giao dịch nhanh chóng 24/7",
      },
      {
        icon: <PieChartIcon size={24} />,
        title: "Thông tin tài sản trực quan",
      },
      {
        icon: <StackIcon size={24} />,
        title: "Công cụ đầu tư đa dạng",
      },
    ],
  },
  {
    title:
      'Kafi <span class="text-brand">One</span>, nền tảng đầu tư </br> thông minh',
    image: "/images/home/<USER>",
    features: [
      {
        icon: <BarChartIcon size={24} />,
        title: "Phân tích thị trường chuyên sâu",
      },
      {
        icon: <ArrowLeftRightIcon size={24} />,
        title: "Thông tin tài sản trực quan",
      },
      {
        icon: <PieChartIcon size={24} />,
        title: "Giao dịch nhanh chóng 24/7",
      },
      {
        icon: <StackIcon size={24} />,
        title: "Công cụ đầu tư đa dạng",
      },
    ],
  },
];

export default function BannerPromotionSection() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const totalSlides = 2;
  const isMobile = useIsMobile();

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % totalSlides);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const currentSlideData = slidesData[currentSlide] || slidesData[0]!;

  const renderNavigation = () => (
    <div className="flex items-center gap-2 text-white">
      <div className="flex items-center justify-center text-sm bg-white/10 px-3 h-[30px] rounded-full">
        {currentSlide + 1}/{totalSlides}
      </div>
      <button
        onClick={currentSlide === 0 ? nextSlide : prevSlide}
        className="h-[30px] w-[30px] flex items-center justify-center bg-white/10 rounded-full transition-colors hover:bg-white group duration-250 cursor-pointer"
      >
        {currentSlide === 0 ? (
          <LineArrowNarrowRightIcon
            size={20}
            className="group-hover:stroke-black"
          />
        ) : (
          <LineArrowNarrowLeftIcon
            size={20}
            className="group-hover:stroke-black"
          />
        )}
      </button>
    </div>
  );

  const renderTitle = () => (
    <AnimatePresence mode="wait">
      <motion.h2
        key={currentSlide}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className={`font-bold leading-tight max-w-md ${
          isMobile ? "text-[22px]" : "text-2xl lg:text-[30px]"
        }`}
        dangerouslySetInnerHTML={{ __html: currentSlideData.title }}
      />
    </AnimatePresence>
  );

  const renderFeatures = () => (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      initial="hidden"
      animate="visible"
      variants={{
        hidden: {},
        visible: {
          transition: {
            staggerChildren: 0.1,
          },
        },
      }}
    >
      {currentSlideData.features.map((feature, index) => (
        <motion.div
          key={`${currentSlide}-${index}`}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0 },
          }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="flex items-center gap-3"
        >
          <div className="flex-shrink-0 ">{feature.icon}</div>
          <span className="text-white/90 leading-relaxed text-sm">
            {feature.title}
          </span>
        </motion.div>
      ))}
    </motion.div>
  );

  const renderButtonsOrLogo = () => {
    if (currentSlide === 0) {
      return (
        <div className="flex gap-4">
          <Button>Đăng ký</Button>
          <Button variant="outline-dark">Tìm hiểu thêm</Button>
        </div>
      );
    }
    if (currentSlide === 1) {
      return (
        <div className="flex items-center gap-4">
          <Image
            src="/images/home/<USER>"
            alt="K QR"
            width={65}
            height={65}
          />
          <div>
            <Typography classname="text-sm">Tải app </Typography>
            <Typography classname="text-sm">Kafi One </Typography>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderImage = () => (
    <AnimatePresence mode="wait">
      <motion.div
        key={currentSlide}
        initial={{ opacity: 0, scale: 1.1 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="absolute top-20 xl:top-20 xl:-right-14 w-[508px] h-[397px] xl:w-[534px] xl:h-[418px]"
      >
        <Image
          src={currentSlideData.image}
          alt="Kafi trading platform interface"
          fill
          className="object-contain"
          sizes={isMobile ? "100vw" : "(max-width: 1024px) 100vw, 600px"}
          priority
        />
      </motion.div>
    </AnimatePresence>
  );

  const renderMobile = () => {
    return (
      <div className="text-white py-6 px-4 flex flex-col justify-between">
        <div className="flex flex-col gap-[30px]">
          {/* Navigation */}
          {renderNavigation()}
          {/* Content */}
          {/* Title */}
          <div className="flex flex-col gap-9">
            {renderTitle()}
            <div className="relative flex min-h-[400px] mb-3">
              {/* renderFeatures */}
              <div className="flex flex-col gap-6 w-1/2">
                {currentSlideData.features.map((feature, index) => (
                  <div
                    key={`${currentSlide}-${index}`}
                    className="flex items-start gap-3"
                  >
                    <div className="flex-shrink-0 mt-1">{feature.icon}</div>
                    <span className="text-white/90 leading-relaxed">
                      {feature.title}
                    </span>
                  </div>
                ))}
              </div>
              {/* Image - Right side */}
              <div className="relative w-1/2 h-full">
                <div
                  key={currentSlide}
                  className="absolute top-0 left-4 w-[508px] h-[397px]"
                >
                  <Image
                    src={currentSlideData.image}
                    alt="Kafi trading platform interface"
                    width={508}
                    height={397}
                    className="object-contain w-full h-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Buttons */}
        {renderButtonsOrLogo()}
      </div>
    );
  };

  const renderTabletAndDesktop = () => {
    return (
      <div className="relative z-2 grid grid-cols-[3fr_2fr] gap-8 md:gap-12 min-h-[380px]">
        {/* Left Content */}
        <div className="text-white relative p-7 xl:py-16 xl:pl-24 flex flex-col justify-between gap-4 xl:gap-12">
          <div className="flex flex-col gap-4 xl:gap-10">
            {/* Title - Tablet/Desktop */}
            {renderTitle()}
            {/* Features Grid */}
            {renderFeatures()}
          </div>

          {/* CTA Buttons */}
          {renderButtonsOrLogo()}
        </div>

        {/* Right Content - Tablet/Desktop */}
        <div className="relative">
          {/* Navigation - Desktop Only */}
          <div className="absolute top-6 right-6 z-20 ">
            {renderNavigation()}
          </div>

          {/* Image Container - Tablet/Desktop */}
          <div className="relative w-full h-full overflow-hidden">
            {renderImage()}
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className={cn(isMobile ? "" : "container")}>
      <div
        className="relative z-10 overflow-hidden md:rounded-2xl"
        style={{ background: "#101820" }}
      >
        {isMobile ? renderMobile() : renderTabletAndDesktop()}
        {/* Background Ellipse Patterns */}
        <div
          className="absolute -bottom-64 -left-64 rounded-full opacity-20"
          style={{
            width: "1158px",
            height: "571px",
            background: "#00C694",
            filter: "blur(300px)",
          }}
        />
      </div>
    </section>
  );
}

const ComponentsPage = () => {
  return (
    <div className="container flex flex-col gap-4">
      <h1 className="text-3xl font-bold underline">Hello World</h1>

      <div className="flex flex-col gap-4">
        <h2 className="text-2xl font-bold">Typography</h2>

        <div className="flex flex-col gap-2">
          <p className="text-brand-primary">Text Brand</p>
          <p className="text-primary">Text Primary</p>
          <p className="text-emphasize">Text Emphasize</p>
          <p className="text-placeholder">Text Placeholder</p>
          <p className="text-icon">Text Icon</p>
          <p className="text-branch">Text Branch</p>
          <p className="text-muted">Text Muted</p>
          <p className="text-accent">Text Accent</p>
          <p className="text-destructive">Text Destructive</p>
          <p className="text-input">Text Input</p>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <h2 className="text-2xl font-bold">Palette</h2>

        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <div className="w-10 h-10 bg-primary-500"></div>
          </div>
        </div>
      </div>

      {/*
      <div className="flex flex-col gap-4">
        <h2 className="text-2xl font-bold">
          Forms
        </h2>

        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <input type="text" className="w-full" />
          </div>
        </div>
      </div> */}

      <div className="flex flex-col gap-4">
        <h2 className="text-2xl font-bold">Cards</h2>

        <div className="flex gap-4">
          <div className="card bg-gradient-primary p-4 rounded-lg">
            Card Primary
          </div>
          <div className="card bg-gradient-dark p-4 rounded-lg">
            <span className="z-10 ">Card Dark</span>
          </div>

          <div className="card bg-gradient-light p-4 rounded-lg">
            Card Light
          </div>

          <div className="card bg-gradient-lighter p-4 rounded-lg">
            Card Lighter
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentsPage;

export const dynamic = "force-dynamic";

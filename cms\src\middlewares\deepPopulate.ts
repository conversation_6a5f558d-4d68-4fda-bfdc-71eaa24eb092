/**
 * `deepPopulate` middleware
 */

import type { Core } from "@strapi/strapi";
import { UID } from "@strapi/types";
import { contentTypes } from "@strapi/utils";
import pluralize from "pluralize";

interface Options {
  /**
   * Fields to select when populating relations
   */
  relationalFields?: string[];
}

// const { CREATED_BY_ATTRIBUTE, UPDATED_BY_ATTRIBUTE } = contentTypes.constants;

const extractPathSegment = (url: string) => {
  // Extract the path segment from a URL like:
  // http://localhost:1338/api/pages/yg5frig6iol64n62m1aq68o0 -> "pages"
  // http://localhost:1338/api/pages -> "pages"
  // http://localhost:1338/api/home?filters%5Blocale%5D=vi -> "home"
  const urlParts = url.split("/");
  const lastPart = urlParts[urlParts.length - 1];
  const pathSegment = lastPart.split("?")[0];
  const cleanPath = pathSegment.split("/")[0];
  return cleanPath;
};

const getDeepPopulate = (uid: UID.Schema, opts: Options = {}) => {
  const model = strapi.getModel(uid);

  if (!model) return {};

  const attributes = Object.entries(model.attributes);

  if (attributes?.length === 0) return {};

  return attributes.reduce(
    (acc: any, [attributeName, attribute]: [string, any]) => {
      switch (attribute.type) {
        case "relation": {
          const isMorphRelation = attribute.relation
            .toLowerCase()
            .startsWith("morph");
          if (isMorphRelation) {
            break;
          }

          // Ignore not visible fields other than createdBy and updatedBy
          const isVisible = contentTypes.isVisibleAttribute(
            model,
            attributeName,
          );
          // const isCreatorField = [
          // 	CREATED_BY_ATTRIBUTE,
          // 	UPDATED_BY_ATTRIBUTE,
          // ].includes(attributeName);

          if (isVisible) {
            if (attributeName === "testimonials") {
              acc[attributeName] = { populate: "user.image" };
            } else {
              acc[attributeName] = { populate: "*" };
            }
          }

          break;
        }

        case "media": {
          acc[attributeName] = { populate: "*" };
          break;
        }

        case "component": {
          const populate = getDeepPopulate(attribute.component, opts);
          acc[attributeName] = { populate };
          break;
        }

        case "dynamiczone": {
          // Use fragments to populate the dynamic zone components
          const populatedComponents = (attribute.components || []).reduce(
            (acc: any, componentUID: UID.Component) => {
              acc[componentUID] = {
                populate: getDeepPopulate(componentUID, opts),
              };

              return acc;
            },
            {},
          );

          acc[attributeName] = { on: populatedComponents };
          break;
        }
        default:
          break;
      }

      return acc;
    },
    {},
  );
};

export default (_config, { strapi }: { strapi: Core.Strapi }) => {
  return async (ctx, next) => {
    if (
      ctx.request.url.startsWith("/api/") &&
      ctx.request.method === "GET" &&
      !ctx.query.populate &&
      !ctx.request.url.includes("/api/users") &&
      !ctx.request.url.includes("/api/seo")
    ) {
      strapi.log.info("Using custom Dynamic-Zone population Middleware...");

      const contentType = extractPathSegment(ctx.request.url);
      const singular = pluralize.singular(contentType);
      const uid = `api::${singular}.${singular}`;

      const populate = {
        ...getDeepPopulate(uid as UID.Schema),
        ...(!ctx.request.url.includes("products") && {
          localizations: {},
        }),
      };

      ctx.query = {
        ...ctx.query,
        populate: {
          ...ctx.query.populate,
          ...populate,
        },
      };
    }
    await next();
  };
};

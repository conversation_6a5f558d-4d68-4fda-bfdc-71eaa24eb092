import Image from "next/image";

import { cn } from "@/lib/utils";

type AppLogoProps = {
  src?: string;
  width?: number;
  height?: number;
  className?: string;
};

export default function AppLogo({
  src = "/logo/logo-color.png",
  width = 200,
  height = 100,
  className,
}: AppLogoProps) {
  return (
    <Image
      src={src}
      alt={"logo"}
      width={width}
      height={height}
      className={cn("object-contain", className)}
    />
  );
}

import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: "round" | "butt" | "square" | "inherit";
  strokeLinejoin?: "round" | "inherit" | "miter" | "bevel";
  className?: string;
};

const IcLineTelescope = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  strokeWidth = 1,
  strokeLinecap = "round",
  strokeLinejoin = "round",
  className,
}: TProps) => {
  const pathScale = Number(size) / 24;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M13.122 14.845L18 22M10.879 14.845L6 22M14 13.2C14 14.305 13.105 15.2 12 15.2C10.895 15.2 10 14.305 10 13.2C10 12.095 10.895 11.2 12 11.2C13.105 11.2 14 12.095 14 13.2ZM17.149 4.532L5.365 7.69C5.094 7.762 4.959 7.799 4.87 7.879C4.791 7.95 4.737 8.043 4.715 8.147C4.69 8.264 4.726 8.399 4.799 8.67L5.679 11.954C5.751 12.225 5.788 12.36 5.868 12.449C5.939 12.527 6.032 12.581 6.136 12.603C6.253 12.628 6.388 12.592 6.659 12.519L18.443 9.362L17.149 4.532ZM21.793 9.5C20.711 9.789 20.17 9.934 19.7 9.835C19.287 9.747 18.912 9.53 18.63 9.217C18.309 8.86 18.164 8.319 17.874 7.237L17.718 6.657C17.428 5.575 17.283 5.034 17.383 4.565C17.471 4.152 17.687 3.777 18.001 3.494C18.358 3.173 18.899 3.028 19.981 2.738C20.252 2.666 20.387 2.629 20.504 2.654C20.608 2.676 20.701 2.73 20.772 2.809C20.852 2.898 20.888 3.033 20.961 3.304L22.358 8.52C22.431 8.791 22.467 8.926 22.442 9.043C22.42 9.147 22.366 9.24 22.288 9.311C22.198 9.391 22.063 9.427 21.793 9.5ZM3.502 12.33L4.854 11.968C5.125 11.895 5.26 11.859 5.349 11.779C5.428 11.708 5.482 11.614 5.504 11.511C5.529 11.394 5.493 11.258 5.42 10.988L5.058 9.636C4.985 9.365 4.949 9.229 4.869 9.14C4.798 9.062 4.704 9.008 4.601 8.986C4.484 8.961 4.348 8.997 4.078 9.07L2.726 9.432C2.455 9.505 2.32 9.541 2.231 9.621C2.152 9.692 2.098 9.786 2.076 9.889C2.051 10.006 2.088 10.142 2.16 10.412L2.522 11.764C2.595 12.035 2.631 12.17 2.711 12.259C2.782 12.338 2.876 12.392 2.979 12.414C3.096 12.439 3.232 12.403 3.502 12.33Z"
        stroke="currentColor"
        strokeWidth={strokeWidth}
        strokeLinecap={strokeLinecap}
        strokeLinejoin={strokeLinejoin}
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcLineTelescope;

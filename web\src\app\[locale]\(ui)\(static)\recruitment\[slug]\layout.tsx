import "@/styles/globals.css";

export default function RecrutmentLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="relative">
      <div className="z-0 bg-linear-to-b from-[#00C795]/3 to-[#00C795]/0 w-full h-screen absolute -top-16 xl:-top-24 right-0 left-0"></div>
      <main className="relative z-1">{children}</main>
    </div>
  );
}

export const dynamic = "force-dynamic";

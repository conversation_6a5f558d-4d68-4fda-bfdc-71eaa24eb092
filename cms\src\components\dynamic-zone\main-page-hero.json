{"collectionName": "components_dynamic_zone_main_page_heroes", "info": {"displayName": "Main_Page_Hero"}, "options": {}, "attributes": {"heading": {"type": "blocks"}, "sub_heading": {"type": "blocks"}, "buttons": {"type": "component", "component": "shared.button", "repeatable": true}, "stat_items": {"type": "component", "component": "items.stat-item", "repeatable": true}, "image": {"type": "component", "component": "styles.image-styles", "repeatable": false}}, "config": {}}
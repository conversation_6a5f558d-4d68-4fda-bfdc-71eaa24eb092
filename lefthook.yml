# EXAMPLE USAGE:
#
#   Refer for explanation to following link:
#   https://github.com/evilmartians/lefthook/blob/master/docs/configuration.md

# READ MORE: https://zenn.dev/kei1232/articles/191a5eb6dbaaff
#
# pre-commit:
#   commands:
#     check:
#       glob: '*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}'
#       run: npx @biomejs/biome check --no-errors-on-unmatched --files-ignore-unknown=true --colors=off {staged_files}
#     stylelint:
#       glob: '*.{css}'
#       run: stylelint --fix {staged_files}
# commit-msg:
#   commands:
#     commitlint:
#       run: pnpm dlx commitlint --edit
pre-commit:
  parallel: true
  commands:
    check:
      glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      run: npx @biomejs/biome check --no-errors-on-unmatched --files-ignore-unknown=true --colors=off {staged_files}
      # run:  npx @biomejs/biome check --write {staged_files} && git add {staged_files}
      stage_fixed: true
    format:
      glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      run: npx @biomejs/biome format --write {staged_files}
      stage_fixed: true
    # package:
    #   glob: web/**/*
    #   run: npx run build && git add .

#   scripts:
#     "hello.js":
#       runner: node
#     "any.go":
#       runner: go run

commit-msg:
  scripts:
    "commitlint.sh":
      runner: bash

# pre-push:
#   commands:
    # packages-audit:
    #   tags: frontend security
    #   skip:
    #     - merge
    #     - rebase
      # run: npm audit
    # test:
    #   run: bun run test
    # check:
      # glob: "*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}"
      # run: npx @biomejs/biome check --no-errors-on-unmatched --files-ignore-unknown=true --colors=off {push_files}

type MobileLogoProps = {
  size?: number;
  width?: number;
  height?: number;
  className?: string;
};

export const MobileLogo = ({
  width = 26,
  height,
  className,
}: MobileLogoProps) => {
  const _computedHeight = height ?? (width / 26) * 29;
  return (
    <svg
      width={width}
      height={_computedHeight}
      viewBox="0 0 26 29"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24.3444 28.3095H17.6498C17.4209 28.3094 17.1955 28.2532 16.9933 28.1458C16.7911 28.0384 16.6183 27.8831 16.4899 27.6934L0 3.23083V1.90212C0 1.58077 0.127588 1.27258 0.354709 1.04536C0.58183 0.818129 0.889873 0.690477 1.21107 0.690477H6.38593C6.61493 0.690177 6.84049 0.746208 7.04275 0.853637C7.24501 0.961067 7.41777 1.1166 7.54581 1.30654L24.9616 27.1388C25.0392 27.2511 25.0845 27.3825 25.0926 27.5187C25.1007 27.6549 25.0714 27.7907 25.0078 27.9114C24.9441 28.0321 24.8486 28.133 24.7317 28.2032C24.6147 28.2734 24.4808 28.3102 24.3444 28.3095Z"
        fill="url(#paint0_linear_2687_9116)"
      />
      <path
        d="M6.38447 0.690475H1.21108C0.542217 0.690475 0 1.23295 0 1.90212V27.092C0 27.7612 0.542217 28.3037 1.21108 28.3037H6.38447C7.05333 28.3037 7.59555 27.7612 7.59555 27.092V1.90212C7.59555 1.23295 7.05333 0.690475 6.38447 0.690475Z"
        fill="url(#paint1_linear_2687_9116)"
      />
      <path
        d="M24.9616 1.85237L7.5385 27.6876C7.41059 27.8773 7.23807 28.0327 7.03608 28.1402C6.8341 28.2476 6.60883 28.3037 6.38008 28.3037H1.21107C0.889874 28.3037 0.58183 28.176 0.354709 27.9488C0.127588 27.7216 0 27.4134 0 27.092V25.7589L16.4855 1.30654C16.6134 1.11681 16.786 0.961402 16.988 0.853982C17.1899 0.746563 17.4152 0.690418 17.644 0.690478H24.3429C24.4782 0.69014 24.6111 0.726697 24.7271 0.796227C24.8432 0.865756 24.9382 0.965629 25.0018 1.0851C25.0654 1.20458 25.0953 1.33914 25.0882 1.47432C25.0811 1.60951 25.0374 1.74021 24.9616 1.85237Z"
        fill="url(#paint2_linear_2687_9116)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2687_9116"
          x1="21.0168"
          y1="28.4427"
          x2="3.54704"
          y2="1.33743"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.15" stopColor="#0C6070" />
          <stop offset="0.51" stopColor="#096B6E" />
          <stop offset="0.99" stopColor="#07756D" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2687_9116"
          x1="3.7985"
          y1="0.829493"
          x2="3.7985"
          y2="27.5061"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.14" stopColor="#07756D" />
          <stop offset="0.35" stopColor="#058977" />
          <stop offset="0.76" stopColor="#01BB8F" />
          <stop offset="0.85" stopColor="#00C795" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_2687_9116"
          x1="3.21052"
          y1="27.9291"
          x2="20.8265"
          y2="1.11512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00C795" />
          <stop offset="0.19" stopColor="#02CB94" />
          <stop offset="0.39" stopColor="#09D892" />
          <stop offset="0.59" stopColor="#13EC8F" />
          <stop offset="0.66" stopColor="#18F58E" />
        </linearGradient>
      </defs>
    </svg>
  );
};

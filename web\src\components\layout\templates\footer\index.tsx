"use client";

import { motion } from "framer-motion";
import { ChevronUp } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { FooterLinkSection, FooterPolicy } from "./components";

const arrowVariants = {
  initial: { y: 0 },
  hoverUp: { y: -4 }, // move up 4px
  hoverDown: { y: 4 }, // move down 4px
};

const Footer = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth", // enables smooth scrolling
    });
  };
  return (
    <div className={cn("bg-white pt-10  mt-20 gap-0 md:gap-6 md:py-10")}>
      <div className="container relative flex flex-col gap-2.5 lg:gap-5">
        <FooterLinkSection />

        <FooterPolicy />

        <motion.div
          onClick={handleScrollToTop}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className="absolute right-0 top-[-58] md:top-[-68] w-full max-w-8 md:max-w-12 overflow-hidden aspect-square h-auto bg-[#00C694] rounded-full cursor-pointer "
        >
          <div className="w-full h-full relative">
            <motion.div
              animate={isHovered ? "hoverUp" : "initial"}
              variants={arrowVariants}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2"
            >
              <ChevronUp className={cn("text-white w-4 md:w-6 h-auto ")} />
            </motion.div>
            <motion.div
              animate={isHovered ? "hoverDown" : "initial"}
              variants={arrowVariants}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2"
            >
              <ChevronUp className="text-white w-4 md:w-6 h-auto " />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Footer;

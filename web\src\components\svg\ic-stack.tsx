type StackIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const StackIcon = ({
  size = 24,
  className,
  color = "#00C694",
}: StackIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.75551 11.0374C4.5087 11.1047 4.26788 11.2661 4.13099 11.5056C3.8572 11.9845 4.02707 12.6053 4.5057 12.879L11.5003 16.8743C11.807 17.0498 12.1917 17.0498 12.4995 16.8743L19.4941 12.879C19.9737 12.6053 20.1426 11.9845 19.8688 11.5056C19.595 11.0267 18.9745 10.8574 18.4949 11.131L11.9999 14.8455L5.50493 11.131C5.26511 10.9942 5.00232 10.9701 4.75551 11.0374ZM4.75551 16.0315C4.5087 16.0988 4.26788 16.2602 4.13099 16.4997C3.8572 16.9786 4.02707 17.5994 4.5057 17.8731L11.5003 21.8684C11.807 22.0439 12.1917 22.0439 12.4995 21.8684L19.4941 17.8731C19.9737 17.5994 20.1426 16.9786 19.8688 16.4997C19.595 16.0208 18.9745 15.8515 18.4949 16.1252L11.9999 19.8396L5.50493 16.1252C5.26511 15.9883 5.00232 15.9642 4.75551 16.0315Z"
        fill="#959A9E"
      />
      <path
        d="M11.5001 2.1316L4.50556 6.12618C3.83408 6.50956 3.83408 7.49044 4.50556 7.87382C5.37989 8.37314 10.6258 11.3691 11.5001 11.8684C11.8069 12.0439 12.1916 12.0439 12.4994 11.8684L19.4939 7.87382C20.1654 7.49044 20.1654 6.50956 19.4939 6.12618L12.4994 2.1316C12.1926 1.95613 11.8079 1.95613 11.5001 2.1316ZM11.9998 4.16005L16.9649 7L11.9998 9.83995C10.9686 9.25095 8.71631 7.9605 7.03461 7L11.9998 4.16005Z"
        fill={color}
      />
    </svg>
  );
};

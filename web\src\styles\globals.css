@import "tailwindcss";
@import "tw-animate-css";

@import "./theme.css";

@layer base {
  body {
    @apply text-base font-normal text-emphasize bg-canvas;
  }
}

/* -----------------------------------------
   UTILITIES
------------------------------------------ */
@layer utilities {
  .card {
    @apply text-white rounded-lg p-4 cursor-pointer transition-all;

    &:hover {
      transform: scale(1.02);
    }
  }

  /* -----------------------------------------
    CONTAINER
  ------------------------------------------ */
  .container {
    @apply w-theme-sm xs:p-5 sm:p-5 sm:w-full md:w-theme-md lg:w-theme-lg xl:w-theme-xl 2xl:w-theme-2xl mx-auto;
    /* if larger than 2xl, then 2xl */
    @media (min-width: 1920px) {
      @apply w-theme-2xl mx-auto;
    }
  }

  .container-inner {
    @apply p-padding-sm md:p-padding-md lg:p-padding-lg xl:p-padding-xl 2xl:p-padding-2xl 3xl:p-padding-3xl w-full;
    @media (min-width: 1920px) {
      @apply p-padding-3xl w-full;
    }
  }

  .container-inner-sm {
    @apply max-w-4xl mx-auto;
  }

  /* .content {
    @apply p-padding-sm md:p-padding-md lg:p-padding-lg xl:p-padding-xl 2xl:p-padding-2xl 3xl:p-padding-3xl w-full;
    @media (min-width: 1920px) {
      @apply p-padding-3xl w-full;
    }
  } */

  /* -----------------------------------------
    GRADIENTS
  ------------------------------------------ */
  .bg-gradient-left {
    @apply text-emphasize;
    background: linear-gradient(
      90deg,
      var(--color-gradient-start) 0%,
      var(--color-gradient-end) 100%
    );
  }

  .bg-gradient-right {
    @apply text-emphasize;
    background: linear-gradient(
      90deg,
      var(--color-gradient-end) 0%,
      var(--bg-gradient-start) 100%
    );
  }

  .bg-gradient-top {
    @apply text-emphasize;
    background: linear-gradient(
      360deg,
      var(--color-gradient-start) 0%,
      var(--color-gradient-end) 100%
    );
  }

  .bg-gradient-bottom {
    @apply text-emphasize;
    background: linear-gradient(
      360deg,
      var(--color-gradient-end) 0%,
      var(--color-gradient-start) 100%
    );
  }
  /*  GRADIENTS - DARK */
  .bg-gradient-dark-left {
    background: linear-gradient(
      90deg,
      #03777d 0%,
      rgba(0, 64, 62, 0.92549) 100%
    );
  }

  .bg-gradient-dark-right {
    background: linear-gradient(
      270deg,
      #03777d 0%,
      rgba(0, 64, 62, 0.92549) 100%
    );
  }

  .bg-gradient-dark-top {
    background: linear-gradient(
      360deg,
      #03777d 0%,
      rgba(0, 64, 62, 0.92549) 100%
    );
  }

  .bg-gradient-dark-bottom {
    background: linear-gradient(
      180deg,
      #03777d 0%,
      rgba(0, 64, 62, 0.92549) 100%
    );
  }

  .bg-gradient-primary {
    background: linear-gradient(360deg, #0dd5a2 0%, #04a87e 100%);
  }

  .bg-gradient-dark {
    background: #000;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      filter: blur(40px);
      z-index: 0;
      pointer-events: none;
      background: #00c694;
      width: 100%;
      height: 100%;
      position: absolute;
      bottom: -40%;
      left: -40%;
    }

    > * {
      position: relative;
      z-index: 2;
    }
  }

  .bg-gradient-light {
    @apply text-emphasize;
    background: linear-gradient(180deg, #f0faf9 1.56%, #e3ecec 100%);
  }

  .bg-gradient-lighter {
    @apply text-emphasize;
    background: linear-gradient(180deg, #f6f6f6 0%, #eeeeee 100%);
  }

  /* -----------------------------------------
    BUTTONS
  ------------------------------------------ */

  .btn-primary {
    @apply text-base font-semibold bg-brand text-white;
    @apply relative overflow-hidden z-1;

    &::before {
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-image: linear-gradient(
        180deg,
        #03777d 0%,
        rgba(0, 64, 62, 0.92549) 100%
      );
      z-index: -1;
      transition: all 0.3s ease-in-out;
      opacity: 0;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  .btn-text {
    @apply text-base font-normal bg-transparent text-[#313941] transition-all duration-300;
    @apply hover:text-brand;
  }

  .btn-outline {
    @apply text-base font-semibold bg-transparent border border-neutral-100 text-emphasize transition-all duration-300;
    @apply hover:bg-subtle hover:border-subtle;
  }

  .btn-outline-dark {
    @apply text-base font-semibold bg-transparent border border-neutral-100 text-white transition-all duration-300;
    @apply hover:border-subtle hover:bg-subtle hover:text-emphasize;
  }

  .btn-ghost {
    @apply text-base font-semibold bg-transparent text-emphasize transition-all duration-300;
    @apply hover:bg-brand-subtle hover:text-brand;
  }

  .btn-link {
    @apply text-base font-semibold bg-transparent text-emphasize transition-all duration-300 relative;
    @apply hover:text-brand;
    &::after {
      content: "";
      width: 80%;
      height: 1px;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      background-color: currentColor;
      transform-origin: center;
      transition: transform 0.3s ease;
    }
    &:hover::after {
      transform: translateX(-50%) scaleX(1);
      transform-origin: center;
    }
  }
  .btn-active-shadow {
    @apply bg-canvas relative text-emphasize hover:text-brand;
    --btn-shadow-width: 80%;
    --btn-shadow-height: 80%;
  }

  .btn-active-shadow::before {
    content: "";
    position: absolute;
    width: var(--btn-shadow-width);
    height: var(--btn-shadow-height);
    background: white;
    transform: translate(-50%, -50%);
    box-shadow:
      0px 4px 6px -2px #1018280d,
      0px 12px 16px -4px #1018281a;
    left: 50%;
    top: 50%;
    z-index: 1;
    opacity: 1;
    transition: opacity 0.3s ease;
    border-radius: 100px;
  }

  .btn-active-shadow > * {
    position: relative;
    z-index: 2;
  }

  .btn-shadow-custom {
    --btn-shadow-width: 90%;
    --btn-shadow-height: 90%;
  }

  .btn-label {
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: linear-gradient(90deg, #19c574 0%, #2aab8a 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    border: none;
    cursor: pointer;
  }

  .btn-label:hover {
    background: linear-gradient(90deg, #19c574 0%, #2aab8a 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transform: scale(1.05);
  }

  /* -----------------------------------------
    CARDS
  ------------------------------------------ */
  .card {
    @apply rounded-lg cursor-pointer transition-all p-padding-sm md:p-padding-md lg:p-padding-lg xl:p-padding-xl 2xl:p-padding-2xl 3xl:p-padding-3xl;
  }

  .list-decimal-no-dot {
    list-style: none;
    counter-reset: item;
  }
  .list-decimal-no-dot li {
    counter-increment: item;
  }
  .list-decimal-no-dot li::marker {
    content: counter(item) "  ";
    margin-right: 0.5rem;
  }
}
/* -----------------------------------------
    SCROLLBAR
  ------------------------------------------ */
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
  background: #e0e2e3;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #1e2328;
  border-radius: 9999px;
}

/* -----------------------------------------
   COMPONENTS
------------------------------------------ */
@layer components {
  /* BUTTONS */
  /* @typography {
    h1 {
      @apply text-4xl font-bold;
    }
    h2 {
      @apply text-3xl font-bold;
    }
    h3 {
      @apply text-2xl font-bold;
    }
    h4 {
      @apply text-xl font-bold;
    }
    h5 {
      @apply text-lg font-bold;
    }
    h6 {
      @apply text-base font-bold;
    }
    p {
      @apply text-base font-normal;
    }
    span {
      @apply text-base font-normal;
    }
    small {
      @apply text-sm font-normal;
    }
    label {
      @apply text-sm font-normal;
    }
    input {
      @apply text-sm font-normal;
    }
  } */
}

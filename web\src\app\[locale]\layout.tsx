import linguiConfig from "#/lingui.config";
import { allMessages, getI18nInstance } from "@/i18n/i18n-server";
import { initLingui, type PageLangParam } from "@/i18n/init-lingui";
import { LinguiClientProvider } from "@/providers/lingui-client-provider";

export async function generateStaticParams() {
  return linguiConfig.locales.map((locale) => ({ locale }));
}

export async function generateMetadata(props: PageLangParam) {
  const i18n = getI18nInstance((await props.params).locale);

  return {
    title: i18n._("Kafi"),
  };
}

type TProps = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export default async function Layout({ children, params }: TProps) {
  const lang = (await params).locale;
  initLingui(lang);

  return (
    <LinguiClientProvider
      initialLocale={lang}
      initialMessages={allMessages[lang]!}
    >
      {children}
    </LinguiClientProvider>
  );
}

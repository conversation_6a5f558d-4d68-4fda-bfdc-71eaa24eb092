{"kind": "collectionType", "collectionName": "margin_products", "info": {"singularName": "margin-product", "pluralName": "margin-products", "displayName": "Margin - Products"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "label": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "enum": ["new", "hot"]}, "description": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "interest_rate_value": {"type": "decimal", "pluginOptions": {"i18n": {"localized": true}}}, "conditions": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "registration_type": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "interest_rate_term": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "default": "year", "enum": ["year", "month", "week"]}, "is_active": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}}}}
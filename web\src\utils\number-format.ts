export const fCurrency = (value: number, locale: string = "vi-VN") => {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: "VND",
  }).format(value);
};

export const fNumber = (
  value: number,
  decimal: number = 0,
  locale: string = "vi-VN",
) => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  }).format(value);
};

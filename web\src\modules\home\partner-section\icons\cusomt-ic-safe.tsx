import { cn } from "#/src/lib/utils";

interface ICompassBoxProps {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
}

export const CustomIcSafe: React.FC<ICompassBoxProps> = ({
  color = "text-icon-placeholder",
  strokeWidth = "3",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}) => {
  return (
    <svg
      width="48"
      height="49"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "cursor-pointer transition-all duration-300",
        color,
        `hover:${hoverColor}`,
        `focus:${activeColor}`,
        className,
      )}
    >
      <path
        d="M12.6665 42H18.6665M30.6665 42H36.6665M35.6665 13V29M6.6665 12.4L6.6665 29.6C6.6665 31.8402 6.6665 32.9603 7.10248 33.816C7.48597 34.5686 8.09789 35.1805 8.85054 35.564C9.70619 36 10.8263 36 13.0665 36L36.2665 36C38.5067 36 39.6268 36 40.4825 35.564C41.2351 35.1805 41.847 34.5686 42.2305 33.816C42.6665 32.9603 42.6665 31.8402 42.6665 29.6V12.4C42.6665 10.1598 42.6665 9.03969 42.2305 8.18404C41.847 7.43139 41.2351 6.81947 40.4825 6.43598C39.6268 6 38.5067 6 36.2665 6L13.0665 6C10.8263 6 9.70619 6 8.85054 6.43597C8.09789 6.81947 7.48597 7.43139 7.10248 8.18404C6.6665 9.03968 6.6665 10.1598 6.6665 12.4ZM23.6665 21C23.6665 23.7614 21.4279 26 18.6665 26C15.9051 26 13.6665 23.7614 13.6665 21C13.6665 18.2386 15.9051 16 18.6665 16C21.4279 16 23.6665 18.2386 23.6665 21Z"
        stroke={"currentColor"}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

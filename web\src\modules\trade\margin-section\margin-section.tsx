"use client";
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Typography,
  Carousel,
  CarouselContent,
  CarouselItem,
  TableFooter,
} from "@/components/ui";

import { useCarousel } from "@/hooks/use-carousel";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { MarginProductCard } from "./components";

// const section_styles = {
//   background: {
//     color: "#FFFF",
//     opacity: 1,
//   },
//   padding: {
//     top: 40,
//     bottom: 40,
//   },
// };

const mockProducts = [
  {
    value: "item-1",
    category: "Margin",
    name: "M65",
    description:
      "Lãi suất margin 6,5%/năm, áp dụng từng ngày khi thỏa điều kiện. Dư nợ Margin cuối ngày không vượt 5 tỷ đồng (≤ 5 tỷ)",
    interest_rate: "6.5%/năm",
    condition:
      "Mở tài khoản chứng kho<PERSON> từ ngày 23/06/2025 đến hết ngày 30/9/2025",
    method: "Tự động áp dụng cho tất cả khách hàng có điều kiện",
    newly_added: true,
  },
  {
    value: "item-2",
    category: "Margin",
    name: "S25",
    description:
      "Lãi suất Margin 8%/năm, áp dụng cho khách hàng đầu tư theo danh mục S25 và có dư nợ cuối mỗi ngày ≤ 10 tỷ VND",
    interest_rate: "6.5%/năm",
    condition:
      "Tỷ trọng danh mục S25 từ 75% trở lên và có dư nợ cuối mỗi ngày ≤ 10 tỷ VND",
    method: "Trực tuyến",
    newly_added: false,
  },
  {
    value: "item-3",
    category: "Margin",
    name: "B10",
    description:
      "Lãi suất Margin 9%/năm, áp dụng từng ngày khi thỏa điều kiện. Dư nợ Margin cuối ngày đạt từ 10 tỷ đồng (≥ 10 tỷ)",
    interest_rate: "6.5%/năm",
    condition: "Khách hàng có dư nợ gốc cuối mỗi ngày từ 10 tỷ VND",
    method: "Trực tuyến",
    newly_added: false,
  },
  {
    value: "item-4",
    category: "Margin",
    name: "Zero",
    description:
      "Lãi suất Margin 0%/năm, áp dụng từng ngày khi thỏa điều kiện. Dư nợ Margin cuối ngày không vượt 100 triệu đồng (≤ 100 triệu)",
    interest_rate: "6.5%/năm",
    condition: "Khách hàng có dư nợ gốc cuối mỗi ngày đến 100 triệu VND",
    method: "Trực tuyến",
    newly_added: false,
  },
];

const signedFund = [
  {
    code: "AAA",
    place: "HOSE",
    company: "CTCP Nhựa An Phát Xanh",
    loan_rate: "50%",
  },
  {
    code: "ACB",
    place: "HOSE",
    company: "Ngân hàng TMCP Á Châu",
    loan_rate: "50%",
  },
  {
    code: "ACC",
    place: "HOSE",
    company: "CTCP Đầu tư và Xây dựng Bình Dương ACC",
    loan_rate: "40%",
  },
  {
    code: "AGG",
    place: "HOSE",
    company: "CTCP Đầu tư và Phát triển Bất động sản An Gia",
    loan_rate: "40%",
  },
  {
    code: "ANV",
    place: "HOSE",
    company: "CTCP Nam Việt",
    loan_rate: "40%",
  },
  {
    code: "ASM",
    place: "HOSE",
    company: "CTCP Tập đoàn Sao Mai",
    loan_rate: "50%",
  },
  {
    code: "AST",
    place: "HOSE",
    company: "CTCP Dịch vụ Hàng không Taseco",
    loan_rate: "50%",
  },
  {
    code: "BAF",
    place: "HOSE",
    company: "CTCP Nông nghiệp BAF Việt Nam",
    loan_rate: "50%",
  },
  {
    code: "BCM",
    place: "HOSE",
    company: "Tổng Công ty Đầu tư và Phát triển Công nghiệp",
    loan_rate: "50%",
  },
  {
    code: "BFC",
    place: "HOSE",
    company: "CTCP Phân bón Bình Điền",
    loan_rate: "50%",
  },
];

const fundHeader = [
  {
    name: "STT",
    width: 5,
  },
  {
    name: "Mã",
    width: 5,
  },
  {
    name: "Sàn",
    width: 5,
  },
  {
    name: "Công ty",
    width: 80,
  },
  {
    name: "Tỷ lệ cho vay",
    width: 5,
  },
];

const MarginSection = () => {
  const { setApi, scrollNext, scrollPrev, canScrollNext, canScrollPrev } =
    useCarousel();
  return (
    <div className="bg-canvas lg:bg-white relative">
      <div className="container flex flex-col gap-6 py-10">
        <Typography
          variant={"large-title"}
          className="md:text-start lg:text-center max-md:text-2xl max-lg:text-3xl"
        >
          Tài trợ giao dịch
        </Typography>
        <Tabs defaultValue="button-1" className="w-full flex flex-col gap-6">
          <TabsList className="w-[300px] h-[56px] grid grid-cols-5 bg-[#F1F3F5] p-1 rounded-3xl gap-1 lg:mx-auto">
            <TabsTrigger
              value="button-1"
              className="cursor-pointer h-full col-span-2 rounded-3xl px-6 py-3 text-icon-placeholder data-[state=active]:bg-white data-[state=active]:text-icon-emphasize border-0 data-[state=active]:shadow-lg"
            >
              <Typography variant={"body-medium"}>Sản phẩm</Typography>
            </TabsTrigger>
            <TabsTrigger
              value="button-2"
              className="cursor-pointer h-full col-span-3 w-[177px] px-6 py-3 rounded-3xl text-icon-placeholder data-[state=active]:bg-white data-[state=active]:text-icon-emphasize border-0 data-[state=active]:shadow-lg"
            >
              <Typography variant={"body-medium"}>Danh mục ký quỹ</Typography>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="button-1" className="flex flex-col gap-6">
            <div className="lg:w-[calc(100%+((100vw-100%)/2))]">
              <Carousel
                className="w-full relative flex flex-col gap-6"
                setApi={setApi}
              >
                <CarouselContent className="w-full md:-ml-8">
                  {mockProducts.map((product, index) => (
                    <CarouselItem
                      key={index}
                      className="basis-6/7 md:basis-1/2 lg:basis-3/11 md:pl-8 "
                    >
                      <MarginProductCard {...product} />
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <div
                  className="absolute z-10 h-full w-[144px] right-0 top-0 hidden lg:flex"
                  style={{
                    background:
                      "linear-gradient(270deg, #FFF 46.59%, rgba(255, 255, 255, 0.00) 100%)",
                  }}
                ></div>
              </Carousel>
            </div>
            <div className=" gap-4 flex items-center justify-end relative z-10">
              <Button
                variant={"ghost"}
                size={"icon"}
                onClick={scrollPrev}
                disabled={!canScrollPrev}
                className="bg-white lg:bg-transparent hover:text-brand rounded-full w-12 h-12"
              >
                <ArrowLeft className="size-6" />
              </Button>
              <Button
                variant={"ghost"}
                size={"icon"}
                onClick={scrollNext}
                disabled={!canScrollNext}
                className="bg-white lg:bg-transparent hover:text-brand rounded-full w-12 h-12"
              >
                <ArrowRight className="size-6" />
              </Button>
            </div>
          </TabsContent>
          <TabsContent value="button-2">
            <Table className="min-w-3xl">
              <TableHeader>
                <TableRow className="text-[#767C82] border-[#AFAFAF]">
                  {fundHeader.map((header, index) => (
                    <TableHead
                      className="p-0"
                      key={index}
                      style={{
                        width: `${header.width}%`,
                      }}
                    >
                      {header.name}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {signedFund.map((fund, index) => (
                  <TableRow key={index} className=" border-b border-[#AFAFAF]">
                    <TableCell className="px-0 py-4 w-[10%]">
                      {index + 1}
                    </TableCell>
                    <TableCell className="px-0 py-4 w-[10%]">
                      {fund.code}
                    </TableCell>
                    <TableCell className="px-0 py-4 w-[10%]">
                      {fund.place}
                    </TableCell>
                    <TableCell className="px-0 py-4 w-[65%]">
                      {fund.company}
                    </TableCell>
                    <TableCell className="px-0 py-4 text-right w-[5%]">
                      {fund.loan_rate}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter className="bg-transparent">
                <TableRow className="border-t border-[#AFAFAF]">
                  <TableCell colSpan={5} className="text-right text-[#767C82] ">
                    190 Công ty - Cập nhật ngày 09/06/2025
                  </TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          </TabsContent>
        </Tabs>
      </div>
      <div
        className="max-lg:hidden absolute w-[974px] h-auto aspect-square rounded-full left-[-257px] bottom-[-129px]"
        style={{
          opacity: 0.03,
          background: "linear-gradient(153deg, #C600AC -7.37%, #12B88E 80.62%)",
          filter: "blur(107.80000305175781px)",
        }}
      ></div>
    </div>
  );
};

export default MarginSection;

{"kind": "collectionType", "collectionName": "careers_applicants", "info": {"singularName": "careers-applicant", "pluralName": "careers-applicants", "displayName": "Careers - Applicants"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "phone_number": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "email": {"type": "email", "pluginOptions": {"i18n": {"localized": true}}}, "province": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "cv": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["files"]}, "cv_status": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "default": "new", "enum": ["new", "seen", "contacted", "ignored"]}}}
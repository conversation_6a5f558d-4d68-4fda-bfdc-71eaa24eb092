"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Typography,
} from "@components/ui";
import Image from "next/image";
import Link from "next/link";
import { useMediaQuery } from "@/hooks/use-media-query";
import { mockSocialIcons } from "#/src/__mock__";

const mockTrade = {
  title: "Trade",
  links: [
    {
      name: "Giới thiệu",
      url: "/trade",
    },
    {
      name: "Bảng giá",
      url: "https://trade.kafi.vn/",
    },
    {
      name: "<PERSON>ản phẩm",
      url: "/trade",
    },
    {
      name: "<PERSON><PERSON> quỹ",
      url: "/trade",
    },
    {
      name: "<PERSON>i<PERSON><PERSON> phí giao dịch",
      url: "/trade/fee",
    },
  ],
};

const mockGBI = {
  title: "GBI",
  links: [
    {
      name: "Giới thiệu",
      url: "/gbi",
    },
    {
      name: "<PERSON><PERSON> m<PERSON><PERSON> đầ<PERSON> tư",
      url: "/gbi",
    },
    {
      name: "<PERSON><PERSON><PERSON> tư tự động",
      url: "/gbi",
    },
  ],
};

const mockPartners = {
  title: "Partners",
  links: [
    {
      name: "Giới thiệu",
      url: "/partners",
    },
    {
      name: "Thông tin về K-Point",
      url: "/partners",
    },
    {
      name: "Công cụ tính K-Points",
      url: "/partners",
    },
  ],
};

const mockAnalysis = {
  title: "Phân tích",
  links: [
    {
      name: "Trung tâm Phân tích",
      url: "/research",
    },
    {
      name: "Báo cáo thị trường",
      url: "/research",
    },
    {
      name: "Báo cáo vĩ mô - chiến lược",
      url: "/research",
    },
    {
      name: "Báo cáo doanh nghiệp",
      url: "/research",
    },
  ],
};

const mockPreferential = {
  title: "Ưu đãi",
  links: [
    {
      name: "Ưu đãi nổi bật",
      url: "/promotions",
    },
  ],
};

const mockNews = {
  title: "Tin tức & sự kiện",
  links: [
    {
      name: "Tin tức",
      url: "/news-events",
    },
    {
      name: "Sự kiện",
      url: "/news-events",
    },
    {
      name: "Thông báo",
      url: "/news-events/notifications",
    },
  ],
};

const mockKnowledge = {
  title: "Kiến thức",
  links: [
    {
      name: "Chứng khoán",
      url: "/",
    },
    {
      name: "Đầu tư",
      url: "/",
    },
    {
      name: "Tài chính cá nhân",
      url: "/",
    },
  ],
};

const mockAboutUs = {
  title: "Về chúng tôi",
  links: [
    {
      name: "Thông tin chung",
      url: "/about",
    },
    {
      name: "Quan hệ cổ đông",
      url: "/shareholders",
    },
    {
      name: "Giải thưởng",
      url: "/awards",
    },
    {
      name: "Tuyển dụng",
      url: "/recruitment",
    },
    {
      name: "Điều khoản sử dụng",
      url: "/terms-and-conditions",
    },
  ],
};

const mockContact = {
  title: "Liên hệ, hỗ trợ",
  links: [
    {
      name: "Thông tin liên hệ",
      url: "/contact-location",
    },
    {
      name: "Trung tâm hỗ trợ",
      url: "/contact-location",
    },
  ],
};

// DEFINE COLUMNS

const _footerLink = [
  {
    id: 1,
    name: "Trade",
    items: [mockTrade],
  },
  {
    id: 2,
    name: "GBI",
    items: [mockGBI],
  },
  {
    id: 3,
    name: "Partners",
    items: [mockPartners],
  },
  {
    id: 4,
    name: "Phân tích",
    items: [mockAnalysis],
  },
  {
    id: 5,
    name: "Ưu đãi",
    items: [mockPreferential, mockNews, mockKnowledge],
  },
  {
    id: 6,
    name: "Về chúng tôi",
    items: [mockAboutUs, mockContact],
  },
];

const FooterLinkSection = () => {
  const isMobile = useMediaQuery("(max-width: 743px)");

  const renderLinks = (links: { name: string; url: string }[]) =>
    links.map((link, index) => {
      return (
        <Link href={link.url} key={index} className="group">
          <Typography
            variant="caption"
            className="text-black group-hover:text-brand transition-all duration-300"
          >
            {link.name}
          </Typography>
        </Link>
      );
    });

  const renderColumn = (
    title: string,
    links: { name: string; url: string }[],
  ) => (
    <div className="flex flex-col gap-[0.5] min-w-[145px]">
      <Typography variant="caption-semi-bold" className="text-black">
        {title}
      </Typography>

      {renderLinks(links)}
    </div>
  );

  const renderMobile = (
    <div className="flex flex-col gap-6 ">
      <div className="w-full flex justify-between">
        <Link href={"/"}>
          <Image
            src={"/logo/logo-color.png"}
            alt="logo"
            width={0}
            height={0}
            sizes="100vw"
            className="w-14 h-auto"
          />
        </Link>
        <div className="flex gap-4">
          {mockSocialIcons.map((icon, index) => (
            <Link href={"#"} key={index}>
              <Image
                src={icon}
                alt={`icon ${index + 1}`}
                width={0}
                height={0}
                sizes="100vw"
                className="w-4 h-auto"
              />
            </Link>
          ))}
        </div>
      </div>

      <Accordion type="single" collapsible className="w-full">
        {_footerLink.map((section, index) => (
          <AccordionItem
            key={index}
            value={`item-${index + 1}`}
            className=" border-[#E2E2E2]"
          >
            <AccordionTrigger width={16} height={16}>
              <Typography
                variant="input-medium"
                className="text-black group-hover:text-brand transition-all duration-300"
              >
                {section.name}
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-xs pb-6 ">
              {section.items.map((item, index) => (
                <div key={index} className="flex flex-col gap-4">
                  {renderLinks(item.links)}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );

  const renderDesktop = (
    <div className="w-full flex justify-between py-2 gap-4">
      <div>
        <Link href={"/"}>
          <Image
            src={"/logo/logo-color.png"}
            alt="logo"
            width={66}
            height={0}
            sizes="100vw"
            className="w-auto h-[18px] aspect-[19/6] lg:w-[86px] lg:h-auto lg:aspect-[3/1]"
          />
        </Link>
      </div>
      <div className="grid grid-cols-3 lg:grid-cols-6 gap-4">
        {_footerLink.map((item) => (
          <div key={item.id} className="flex flex-col gap-4">
            {item.items.map((item, index) => (
              <div key={index}>{renderColumn(item.title, item.links)}</div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );

  return isMobile ? renderMobile : renderDesktop;
};

export default FooterLinkSection;

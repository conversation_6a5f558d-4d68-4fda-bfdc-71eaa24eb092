import { AwardsView } from "#/src/modules/awards";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Awards - Kafi",
  description: "Awards - Kafi",
};

export default async function AwardsPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <AwardsView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

"use client";
import { Typography } from "#/src/components/ui";
import { useState } from "react";
import { DatePopover } from "../../pages/research/researches-detail-view";

export default function ListJobView({ items }: { items: any }) {
  const [fromDate, setFromDate] = useState<Date | undefined>(new Date());
  const [toDate, setToDate] = useState<Date | undefined>(new Date());
  const handleClickFile = () => {
    window.open(
      "https://file-examples.com/storage/fe1f31703e68825e999a328/2017/10/file-sample_150kB.pdf",
      "_blank",
    );
  };
  return (
    <div className="flex flex-col gap-10">
      <Typography
        variant="large-title"
        className="text-emphasize md:text-center max-md:text-2xl max-lg:text-3xl"
      >
        Danh sách hành ngh<PERSON> chứng kho<PERSON>
      </Typography>
      <div className="w-fit flex gap-6 mx-auto">
        <DatePopover
          label="Từ ngày"
          date={fromDate}
          setDate={setFromDate}
          triggerClassName="!bg-white max-w-[150px] md:max-w-[170px]"
        />
        <DatePopover
          label="Đến ngày"
          date={toDate}
          setDate={setToDate}
          triggerClassName="!bg-white max-w-[150px] md:max-w-[170px]"
        />
      </div>
      <div className="container !py-0">
        <div className="bg-screen flex flex-col gap-4 rounded-2xl p-6">
          {items.map((item: any, index: number) => (
            <div key={index} className="py-4 px-6 flex gap-3 items-start">
              <svg
                width="40"
                height="40"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="shrink-0"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M14.6131 2.08325H25.3867C26.7415 2.08324 27.8342 2.08323 28.719 2.15552C29.6301 2.22996 30.4303 2.38723 31.1707 2.76446C32.3467 3.36367 33.3028 4.3198 33.902 5.49581C34.2793 6.23618 34.4365 7.03641 34.511 7.94747C34.5833 8.83231 34.5832 9.925 34.5832 11.2797V11.6666C34.5832 12.3569 34.0236 12.9166 33.3332 12.9166C32.6429 12.9166 32.0832 12.3569 32.0832 11.6666V11.3333C32.0832 9.9125 32.0823 8.9221 32.0193 8.15105C31.9575 7.39459 31.8422 6.95998 31.6745 6.63079C31.315 5.92518 30.7413 5.3515 30.0357 4.99198C29.7065 4.82425 29.2719 4.70903 28.5154 4.64722C27.7444 4.58422 26.754 4.58325 25.3332 4.58325H14.6666C13.2458 4.58325 12.2554 4.58422 11.4844 4.64722C10.7279 4.70903 10.2933 4.82425 9.9641 4.99198C9.25849 5.3515 8.68482 5.92518 8.32529 6.63079C8.15756 6.95998 8.04234 7.39459 7.98054 8.15105C7.91754 8.9221 7.91657 9.9125 7.91657 11.3333V28.6666C7.91657 30.0873 7.91754 31.0777 7.98054 31.8488C8.04234 32.6052 8.15756 33.0399 8.32529 33.369C8.68482 34.0747 9.25849 34.6483 9.9641 35.0079C10.2933 35.1756 10.7279 35.2908 11.4844 35.3526C12.2554 35.4156 13.2458 35.4166 14.6666 35.4166H20.8332C21.5236 35.4166 22.0832 35.9762 22.0832 36.6666C22.0832 37.3569 21.5236 37.9166 20.8332 37.9166H14.613C13.2583 37.9166 12.1656 37.9166 11.2808 37.8443C10.3697 37.7699 9.56949 37.6126 8.82912 37.2354C7.65311 36.6362 6.69698 35.68 6.09777 34.504C5.72054 33.7637 5.56327 32.9634 5.48884 32.0524C5.41654 31.1675 5.41655 30.0748 5.41657 28.7201V11.2797C5.41655 9.92501 5.41654 8.83232 5.48884 7.94747C5.56327 7.03641 5.72054 6.23618 6.09777 5.49581C6.69698 4.3198 7.65311 3.36367 8.82913 2.76446C9.56949 2.38723 10.3697 2.22996 11.2808 2.15552C12.1656 2.08323 13.2583 2.08324 14.6131 2.08325ZM12.0832 11.6666C12.0832 10.9762 12.6429 10.4166 13.3332 10.4166H26.6666C27.3569 10.4166 27.9166 10.9762 27.9166 11.6666C27.9166 12.3569 27.3569 12.9166 26.6666 12.9166H13.3332C12.6429 12.9166 12.0832 12.3569 12.0832 11.6666ZM12.0832 18.3333C12.0832 17.6429 12.6429 17.0833 13.3332 17.0833H20.8332C21.5236 17.0833 22.0832 17.6429 22.0832 18.3333C22.0832 19.0236 21.5236 19.5833 20.8332 19.5833H13.3332C12.6429 19.5833 12.0832 19.0236 12.0832 18.3333ZM32.4999 19.5833C31.8095 19.5833 31.2499 20.1429 31.2499 20.8333V29.9999C31.2499 30.6903 30.6903 31.2499 29.9999 31.2499C29.3095 31.2499 28.7499 30.6903 28.7499 29.9999V20.8333C28.7499 18.7622 30.4288 17.0833 32.4999 17.0833C34.571 17.0833 36.2499 18.7622 36.2499 20.8333V29.9999C36.2499 33.4517 33.4517 36.2499 29.9999 36.2499C26.5481 36.2499 23.7499 33.4517 23.7499 29.9999V23.3333C23.7499 22.6429 24.3095 22.0833 24.9999 22.0833C25.6903 22.0833 26.2499 22.6429 26.2499 23.3333V29.9999C26.2499 32.071 27.9288 33.7499 29.9999 33.7499C32.071 33.7499 33.7499 32.071 33.7499 29.9999V20.8333C33.7499 20.1429 33.1903 19.5833 32.4999 19.5833ZM12.0832 24.9999C12.0832 24.3096 12.6429 23.7499 13.3332 23.7499H19.1666C19.8569 23.7499 20.4166 24.3096 20.4166 24.9999C20.4166 25.6903 19.8569 26.2499 19.1666 26.2499H13.3332C12.6429 26.2499 12.0832 25.6903 12.0832 24.9999Z"
                  fill="#313941"
                />
              </svg>

              <div
                className="flex flex-col gap-1 cursor-pointer"
                onClick={handleClickFile}
              >
                <Typography variant="headline" classname="text-emphasize">
                  {item.name}
                </Typography>
                <Typography variant="body-regular" classname="text-default">
                  {item.date}
                </Typography>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

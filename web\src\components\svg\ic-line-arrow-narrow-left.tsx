type LineArrowNarrowLeftIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const LineArrowNarrowLeftIcon = ({
  size = 16,
  className,
  color = "white",
}: LineArrowNarrowLeftIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3332 8H2.6665M2.6665 8L6.6665 12M2.6665 8L6.6665 4"
        stroke={color}
        className={className}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

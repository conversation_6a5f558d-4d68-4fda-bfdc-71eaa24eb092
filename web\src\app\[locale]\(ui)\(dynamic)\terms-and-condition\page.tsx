import { TermsAndConditionView } from "#/src/modules/terms-and-condition";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms & Condition - Kafi",
  description: "Terms & Condition - Kafi",
};

export default async function TermsAndConditionPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <TermsAndConditionView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

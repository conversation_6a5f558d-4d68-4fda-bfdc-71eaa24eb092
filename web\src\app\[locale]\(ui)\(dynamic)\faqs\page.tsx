import FaqsView from "#/src/modules/faqs/faqs-view";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";
//-----------------------------------------------------------
export const metadata: Metadata = {
  title: "FAQs - Kafi",
  description: "FAQs - Kafi",
};
//-----------------------------------------------------------

export default async function FaqsPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <FaqsView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

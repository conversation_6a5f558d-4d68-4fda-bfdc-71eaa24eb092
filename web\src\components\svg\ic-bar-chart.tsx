type BarChartIconProps = {
  size?: number;
  className?: string;
  color?: string;
};

export const BarChartIcon = ({
  size = 24,
  className,
  color = "#00C694",
}: BarChartIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 8C9.89543 8 9 8.89543 9 10V18C9 19.1046 9.89543 20 11 20H13C14.1046 20 15 19.1046 15 18V10C15 8.89543 14.1046 8 13 8H11ZM11 10L13 10V18H11V10ZM4 12C2.89543 12 2 12.8954 2 14V18C2 19.1046 2.89543 20 4 20H6C7.10457 20 8 19.1046 8 18V14C8 12.8954 7.10457 12 6 12H4ZM4 14H6V18H4V14Z"
        fill="#959A9E"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 6C16 4.89543 16.8954 4 18 4H20C21.1046 4 22 4.89543 22 6V18C22 19.1046 21.1046 20 20 20H18C16.8954 20 16 19.1046 16 18V6ZM20 6L18 6V18H20V6Z"
        fill={color}
      />
    </svg>
  );
};

{"kind": "collectionType", "collectionName": "article_categories", "info": {"singularName": "article-category", "pluralName": "article-categories", "displayName": "Article - Categories"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "type": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "enum": ["news", "event", "notification"]}, "title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::article-category.article-category", "mappedBy": "parent"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::article-category.article-category", "inversedBy": "children"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "category"}}}
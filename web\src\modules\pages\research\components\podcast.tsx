"use client";
import {
  PODCAST_AUDIOS,
  renderMedia<PERSON>utton,
  spotifyLink,
  youtubeLink,
} from "#/src/__mock__/research";
import { Typography } from "#/src/components/ui";
import { Slider } from "#/src/components/ui/slider";
import { Play } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useRef, useState } from "react";

interface PodcastAudioProps {
  title: string;
  episode: string;
  audio_src: string;
}

const Podcast = () => {
  const [selectedAudio, setSelectedAudio] = useState<PodcastAudioProps>(
    PODCAST_AUDIOS[0],
  );
  const audioRef = useRef<HTMLAudioElement>(null);
  const [audioIndex, setAudioIndex] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isPlay, setPlay] = useState(false);

  const handleLoadedData = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
      setCurrentTime(audioRef.current.currentTime);
      if (isPlay) audioRef.current.play();
    }
  }, [isPlay]);

  const handlePausePlayClick = useCallback(() => {
    if (audioRef.current) {
      if (isPlay) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setPlay(!isPlay);
    }
  }, [isPlay]);
  const handleChangeAudio = useCallback(
    (audio: PodcastAudioProps, index: number) => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setSelectedAudio(audio);
      setAudioIndex(index);
      setCurrentTime(0);
      setPlay(false);
    },
    [],
  );
  const handlePlayNext = useCallback(() => {
    const nextIndex = (audioIndex + 1) % PODCAST_AUDIOS.length;
    const nextAudio = PODCAST_AUDIOS[nextIndex];
    handleChangeAudio(nextAudio, nextIndex);
  }, [audioIndex, handleChangeAudio]);

  const handlePlayPrev = useCallback(() => {
    const prevIndex =
      (audioIndex - 1 + PODCAST_AUDIOS.length) % PODCAST_AUDIOS.length;
    const prevAudio = PODCAST_AUDIOS[prevIndex];
    handleChangeAudio(prevAudio, prevIndex);
  }, [audioIndex, handleChangeAudio]);
  return (
    <div className="container !max-w-[1066px] flex flex-col gap-6 mx-auto">
      <Typography variant="title-1" classname="text-start md:text-center">
        Kafi Podcast - Nhịp đập tài chính
      </Typography>
      <div className="flex gap-3 items-center md:mx-auto">
        <Typography variant="body-medium" classname="text-gray-900">
          Xem thêm tại
        </Typography>
        <Link href={youtubeLink}>
          <Image
            src={"/images/research/Youtube.png"}
            alt="youtube"
            sizes="100vw"
            width={0}
            height={0}
            className="w-8 h-auto aspect-square"
          />
        </Link>
        <Link href={spotifyLink}>
          <Image
            src={"/images/research/Spotify.png"}
            alt="youtube"
            sizes="100vw"
            width={0}
            height={0}
            className="w-8 h-auto aspect-square"
          />
        </Link>
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-14">
        <div className="w-full border-1 border-subtle rounded-lg overflow-hidden">
          <div
            className="w-full h-[155px] py-5 pl-5 md:py-6 md:px-10"
            style={{
              background:
                "linear-gradient(180deg, #F6FFFC 0%, #27DB99 101.09%)",
            }}
          >
            <div className="flex flex-col gap-2.5 pr-6">
              <Typography variant="button-label" classname="text-brand">
                {selectedAudio.title}
              </Typography>
              <Image
                src={"/images/research/nhp-dap-tai-chinh.png"}
                alt="nhip dap tai chinh"
                width={0}
                height={0}
                sizes="(max-width: 768px) 100vw, (min-width: 1025px) 50vw"
                className="w-[321px] h-auto aspect-[321/40]"
              />
            </div>
          </div>
          <div className="py-5 px-5 md:pl-10 flex flex-col gap-2">
            <p className="font-bold text-gray-600 text-sm leading-5">
              {selectedAudio.episode}
            </p>
            <div className="flex items-center gap-3">
              <div className="flex min-w-20 gap-1">
                {/* {renderMediaButton.map((media, index) => (
                  <Image
                    key={index}
                    src={media}
                    alt={`inde ${index + 1}`}
                    className="w-6 h-auto aspect-square"
                    sizes="100vw"
                    width={0}
                    height={0}
                  />
                ))} */}
                <Image
                  src={renderMediaButton[0]}
                  alt={"backward"}
                  className="w-6 h-auto aspect-square"
                  sizes="100vw"
                  width={0}
                  height={0}
                  onClick={handlePlayPrev}
                />

                <div onClick={handlePausePlayClick}>
                  {isPlay ? (
                    <Image
                      src={renderMediaButton[1]}
                      alt={"pause"}
                      className="w-6 h-auto aspect-square"
                      sizes="100vw"
                      width={0}
                      height={0}
                    />
                  ) : (
                    <Play
                      className="w-6 h-auto aspect-square text-brand"
                      fill="currentColor"
                    />
                  )}
                </div>
                <Image
                  src={renderMediaButton[2]}
                  alt={"forward"}
                  className="w-6 h-auto aspect-square"
                  sizes="100vw"
                  width={0}
                  height={0}
                  onClick={handlePlayNext}
                />
              </div>
              <Slider
                className="w-full data-[orientation=horizontal]:h-2"
                trackClassName="data-[orientation=horizontal]:h-2 bg-gray-200"
                thumbClassName="w-4 h-2 focus-visible:ring-0 hover:ring-0 bg-black"
                value={[currentTime]}
                max={duration}
                step={0.1}
                onValueChange={([val]) => {
                  if (audioRef.current) {
                    audioRef.current.currentTime = val;
                    setCurrentTime(val);
                  }
                }}
              />
            </div>
          </div>
        </div>
        <div className="w-full flex flex-col gap-8">
          {PODCAST_AUDIOS.filter(
            (audio) => audio.title !== selectedAudio.title,
          ).map((podcast, index) => (
            <div
              key={index}
              className="flex flex-col gap-4 pb-4 border-b-1 border-subtle last:border-b-0"
            >
              <div onClick={() => handleChangeAudio(podcast, index)}>
                <Typography
                  variant="title-3"
                  classname="text-emphasize min-h-[52px] line-clamp-2 text-wrap cursor-pointer hover:text-brand ease-in-out transition-all duration-500"
                >
                  {podcast.title}
                </Typography>
              </div>
              <Typography
                variant="body-regular"
                classname="text-emphasize line-clamp-2"
              >
                {podcast.episode}
              </Typography>
            </div>
          ))}
        </div>
      </div>
      <audio
        ref={audioRef}
        src={selectedAudio.audio_src}
        onLoadedData={handleLoadedData}
        onTimeUpdate={() => setCurrentTime(audioRef?.current?.currentTime ?? 0)}
        onEnded={() => setPlay(false)}
      >
        <track default kind="captions" src="" />
      </audio>
    </div>
  );
};

export default Podcast;

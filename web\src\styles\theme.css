@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans:
    "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --color-black: #000;
  --color-white: #fff;

  --color-card: oklch(0.145 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.145 0 0);
  --color-popover-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.269 0 0);
  --color-secondary-foreground: oklch(0.985 0 0);
  --color-muted: oklch(0.269 0 0);
  --color-muted-foreground: oklch(0.708 0 0);
  --color-accent: oklch(0.269 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-destructive: hsla(4, 86%, 60%, 1);
  --color-destructive-foreground: oklch(0.985 0 0);

  /* -----------------------------------------
       EFFECT COLORS
    ------------------------------------------ */
  /* Effect - Brand Colors */
  --color-brand-light: #16e684;
  --color-brand-light-foreground: oklch(0.985 0 0);
  --color-brand-middle: #00c694;
  --color-brand-middle-foreground: oklch(0.985 0 0);
  --color-brand-dark: #0c6070;
  --color-brand-dark-foreground: oklch(0.985 0 0);
  --color-brand-darker: #101820;
  --color-brand-darker-foreground: oklch(0.985 0 0);

  /* Effect - Primary Colors */
  --color-primary-25: hsla(0, 0%, 96%, 0.9);
  --color-primary-50: hsla(155, 81%, 96%, 1);
  --color-primary-100: hsla(151, 84%, 90%, 1);
  --color-primary-200: hsla(154, 78%, 80%, 1);
  --color-primary-300: hsla(158, 74%, 67%, 1);
  --color-primary-400: hsla(160, 67%, 48%, 1);
  --color-primary-500: hsla(162, 87%, 39%, 1);
  --color-primary-600: hsla(162, 88%, 32%, 1);
  --color-primary-700: hsla(162, 89%, 24%, 1);
  --color-primary-800: hsla(164, 93%, 16%, 1);
  --color-primary-900: hsla(166, 100%, 8%, 1);

  /* Effect - Neutral  Colors */
  --color-neutral-25: hsla(210, 13%, 97%, 1);
  --color-neutral-50: hsla(200, 5%, 88%, 1);
  --color-neutral-100: hsla(200, 3%, 80%, 1);
  --color-neutral-200: hsla(207, 4%, 60%, 1);
  --color-neutral-300: hsla(210, 5%, 49%, 1);
  --color-neutral-400: hsla(208, 8%, 37%, 1);
  --color-neutral-500: hsla(210, 14%, 25%, 1);
  --color-neutral-600: hsla(210, 14%, 22%, 1);
  --color-neutral-700: hsla(212, 13%, 19%, 1);
  --color-neutral-800: hsla(210, 12%, 16%, 1);
  --color-neutral-900: hsla(210, 14%, 14%, 1);

  /* Effect - Yellow Colors */
  --color-yellow-25: hsla(54, 100%, 98%, 1);
  --color-yellow-25-foreground: hsla(0, 0%, 96%, 0.9);
  --color-yellow-200: hsla(52, 100%, 76%, 1);
  --color-yellow-200-foreground: hsla(0, 0%, 96%, 0.9);
  --color-yellow-400: hsla(47, 100%, 55%, 1);
  --color-yellow-400-foreground: hsla(0, 0%, 96%, 0.9);
  --color-yellow-500: hsla(40, 100%, 50%, 1);
  --color-yellow-500-foreground: hsla(0, 0%, 96%, 0.9);
  --color-yellow-600: hsla(35, 100%, 44%, 1);
  --color-yellow-600-foreground: hsla(0, 0%, 96%, 0.9);
  --color-yellow-900: hsla(40, 100%, 20%, 1);
  --color-yellow-900-foreground: hsla(0, 0%, 96%, 0.9);

  /* Effect - Red Colors */
  --color-red-25: hsla(5, 86%, 97%, 1);
  --color-red-25-foreground: hsla(0, 0%, 96%, 0.9);
  --color-red-200: hsla(3, 96%, 89%, 1);
  --color-red-200-foreground: hsla(0, 0%, 96%, 0.9);
  --color-red-400: hsla(4, 92%, 69%, 1);
  --color-red-400-foreground: hsla(0, 0%, 96%, 0.9);
  --color-red-500: hsla(4, 86%, 60%, 1);
  --color-red-500-foreground: hsla(0, 0%, 96%, 0.9);
  --color-red-600: hsla(4, 74%, 51%, 1);
  --color-red-600-foreground: hsla(0, 0%, 96%, 0.9);
  --color-red-900: hsla(4, 57%, 24%, 1);
  --color-red-900-foreground: hsla(0, 0%, 96%, 0.9);
  /* -----------------------------------------
       THEME COLORS
    ------------------------------------------ */
  /* --primary: hsla(0, 0%, 0%, 1); */
  /* --primary-foreground: hsla(0, 0%, 96%, 0.9); */
  --color-canvas: hsla(210, 13%, 97%, 1);
  --color-canvas-foreground: hsla(210, 14%, 22%, 1);
  --color-screen: hsla(0, 0%, 100%, 1);
  --color-screen-foreground: hsla(0, 0%, 96%, 0.9);
  --color-subtle: hsla(220, 4%, 84%, 1);
  --color-subtle-foreground: hsla(0, 0%, 96%, 0.9);
  --color-brand: hsla(165, 100%, 39%, 1);
  --color-brand-foreground: hsla(0, 0%, 100%, 1);
  --color-brand-subtle: hsla(160, 69%, 97%, 1);
  --color-brand-subtle-foreground: hsla(165, 100%, 39%, 1);
  --color-error: hsla(4, 86%, 60%, 1);
  --color-error-foreground: hsla(0, 0%, 96%, 0.9);
  --color-warning: hsla(40, 100%, 50%, 1);
  --color-warning-foreground: hsla(0, 0%, 96%, 0.9);
  --color-success: hsla(162, 87%, 39%, 1);
  --color-success-foreground: hsla(0, 0%, 96%, 0.9);
  --color-gradient-start: hsla(152, 77%, 44%, 1);
  --color-gradient-start-foreground: hsla(0, 0%, 96%, 0.9);
  --color-gradient-end: hsla(165, 61%, 42%, 1);
  --color-gradient-end-foreground: hsla(0, 0%, 96%, 0.9);

  --color-icon-default: hsla(210, 14%, 22%, 1);
  --color-icon-emphasize: hsla(210, 14%, 14%, 1);
  --color-icon-placeholder: hsla(210, 5%, 49%, 1);
  --color-icon-disabled: hsla(200, 5%, 88%, 1);
  --color-icon-brand: hsla(165, 100%, 39%, 1);
  --color-icon-brand-light-subtle: hsla(151, 84%, 90%, 1);
  --color-icon-on-color: hsla(0, 0%, 100%, 1);
  --color-icon-on-white: hsla(210, 14%, 14%, 1);
  --color-icon-error: hsla(4, 86%, 60%, 1);
  --color-icon-warning: hsla(40, 100%, 50%, 1);
  --color-icon-success: hsla(162, 87%, 39%, 1);

  --color-default: hsla(210, 13%, 97%, 1);
  --color-subtle: hsla(220, 4%, 84%);
  --color-disabled: hsla(210, 13%, 97%, 1);
  --color-brand: hsla(165, 100%, 39%, 1);

  /* -----------------------------------------
       TEXT COLORS
    ------------------------------------------ */
  --color-primary: oklch(0 0% 0);
  --color-primary-foreground: hsla(0, 0%, 96%, 0.9);
  --color-default: #313941;
  --color-default-foreground: hsla(0, 0%, 96%, 0.9);
  --color-emphasize: #1e2328;
  --color-emphasize-foreground: hsla(0, 0%, 96%, 0.9);
  --color-placeholder: hsla(210, 5%, 49%, 1);
  --color-placeholder-foreground: hsla(0, 0%, 96%, 0.9);
  --color-disabled: hsla(200, 5%, 88%, 1);
  --color-disabled-foreground: hsla(0, 0%, 96%, 0.9);
  --color-brand: hsla(165, 100%, 39%, 1);
  --color-brand-foreground: hsla(0, 0%, 96%, 0.9);
  --color-on-color: hsla(0, 0%, 100%, 1);
  --color-on-color-foreground: hsla(0, 0%, 96%, 0.9);
  --color-on-white: hsla(210, 14%, 14%, 1);
  --color-on-white-foreground: hsla(0, 0%, 96%, 0.9);
  --color-error: hsla(4, 86%, 60%, 1);
  --color-error-foreground: hsla(0, 0%, 96%, 0.9);
  --color-warning: hsla(40, 100%, 50%, 1);
  --color-warning-foreground: hsla(0, 0%, 96%, 0.9);
  --color-success: hsla(162, 87%, 39%, 1);
  --color-success-foreground: hsla(0, 0%, 96%, 0.9);
  --color-border: hsla(0, 0.02%, 68.14%, 1);
  --color-border-default: hsla(210, 4%, 72%, 1);
  --color-border-button: hsla(200, 3%, 80%);
  --color-border-foreground: hsla(0, 0%, 96%, 0.9);
  --color-border-50: hsla(0, 0.02%, 68.14%, 0.5);
  --color-border-50-foreground: hsla(0, 0%, 96%, 0.9);

  /*
    Spacing
    spacing: 8px;
    line-height: 16px;
    */
  --spacing: 0.25rem; /* 4px */

  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --container-3xs: 16rem;
  --container-2xs: 18rem;
  --container-xs: 20rem;
  --container-sm: 24rem;
  --container-md: 28rem;
  --container-lg: 32rem;
  --container-xl: 36rem;
  --container-2xl: 42rem;
  --container-3xl: 48rem;
  --container-4xl: 56rem;
  --container-5xl: 64rem;
  --container-6xl: 72rem;
  --container-7xl: 80rem;

  /* Typography */
  /* Font base 16px */
  --text-base: 16px;
  --text-base--line-height: 24px;

  /*
    Special Title (H1)
    font-style: extrabold;
    font-size: 40px;
    line-height: 56px;
    letter-spacing: -1%;
    */
  --text-heading-0: 40px;
  --text-heading-0--line-height: 56px;
  --text-heading-0--font-weight: 700;
  --text-heading-0--letter-spacing: -1%;

  /*
    Large Title (H2)
    font-style: extrabold;
    font-size: 36px;
    line-height: 40px;
    letter-spacing: -1%;
    */
  --text-heading-1: 32px;
  --text-heading-1--line-height: 40px;
  --text-heading-1--font-weight: 700;
  --text-heading-1--letter-spacing: -1%;

  /*
    Title 0 (H3)
    font-style: bold;
    font-size: 30px;
    line-height: 36px;
    letter-spacing: -1%;
    */
  --text-heading-2: 30px;
  --text-heading-2--line-height: 36px;
  --text-heading-2--font-weight: 600;
  --text-heading-2--letter-spacing: -1%;

  /*
    Title 1 (H4)
    font-style: bold;
    font-size: 28px;
    line-height: 36px;
    letter-spacing: -1%;
    */
  --text-heading-3: 28px;
  --text-heading-3--line-height: 36px;
  --text-heading-3--font-weight: 600;
  --text-heading-3--letter-spacing: -1%;

  /*
    Title 2 (H5)
    font-style: semibold;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: -1%;
    */
  --text-heading-4: 24px;
  --text-heading-4--line-height: 32px;
  --text-heading-4--font-weight: 600;
  --text-heading-4--letter-spacing: -1%; /*
    Title 3 (H6)
    font-style: semibold;
    font-size: 20px;
    line-height: 26px;
    letter-spacing: -1%;
    */
  --text-heading-5: 20px;
  --text-heading-5--line-height: 26px;
  --text-heading-5--font-weight: 600;
  --text-heading-5--letter-spacing: -1%;

  /*
    Headline (p)
    font-style: semibold;
    font-size: 16px;
    line-height: 24px;
    */
  --text-heading-6: 18px;
  --text-heading-6--line-height: 24px;
  --text-heading-6--font-weight: 600;
  --text-heading-6--letter-spacing: -1%;

  /*
    Subheadline (p)
    font-size: 14px;
    line-height: 20px;
    */
  --text-subheadline: 18px;
  --text-subheadline--line-height: 24px;
  --text-subheadline--font-weight: 600;
  --text-subheadline--letter-spacing: -1%;

  /*
    Medium body
    font-size: 14px;
    line-height: 20px;
    */
  --text-sm: 14px;
  --text-sm--line-height: 20px;

  /*
      Small body
      font-size: 12px;
      line-height: 16px;
      */
  --text-xs: 12px;
  --text-xs--line-height: 16px;

  /*
      Button label
      font-size: 16px;
      line-height: 20px;
      */
  --text-button: 16px;
  --text-button--line-height: 20px;

  /*
      Input Medium
      font-size: 14px;
      line-height: 16px;
      */
  --text-input: 14px;
  --text-input--line-height: 16px;

  /*
      Caption:
      font-size: 12px;
      line-height: 16px;
      */
  --text-caption: 12px;
  --text-caption--line-height: 16px;

  /*
      Label:
      font-size: 10px;
      line-height: 16px;
      */
  --text-label: 10px;
  --text-label--line-height: 16px;

  /*
      mini label
      font-size: 8px;
      line-height: 12px;
      */
  --text-mini-label: 8px;
  --text-mini-label--line-height: 12px;

  /* Breakpoints */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 375px;
  --breakpoint-md: 744px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1440px;
  --breakpoint-2xl: 1920px;
  --breakpoint-3xl: 2560px;

  /* Body content: 327px
    Margin left, right: 24px
    Padding: 16px
    Header margin left right: 24px */
  /* Spacing */
  /* --space-content-sm: 327px;
  --space-content-md: 664px;
  --space-content-lg: 944px;
  --space-content-xl: 1280px;
  --space-content-2xl: 1600px; */

  --container-theme-sm: 327px;
  --container-theme-md: 664px;
  --container-theme-lg: 944px;
  --container-theme-xl: 1280px;
  --container-theme-2xl: 1600px;

  /* --spacing-theme-sm: 16px;
  --spacing-theme-md: 24px;
  --spacing-theme-lg: 32px;
  --spacing-theme-xl: 40px;
  --spacing-theme-2xl: 48px;
  --spacing-theme-3xl: 56px;
  --spacing-theme-4xl: 64px;
  --spacing-theme-5xl: 72px;
  --spacing-theme-6xl: 80px;
  --spacing-theme-7xl: 160px;
  --spacing-theme-8xl: 268px; */

  --spacing-padding-sm: 16px;
  --spacing-padding-md: 24px;
  --spacing-padding-lg: 24px;
  --spacing-padding-xl: 24px;
  --spacing-padding-2xl: 32px;
  --spacing-padding-3xl: 40px;

  --spacing-margin-sm: 24px;
  --spacing-margin-md: 40px;
  --spacing-margin-lg: 40px;
  --spacing-margin-xl: 80px;
  --spacing-margin-2xl: 160px;
  --spacing-margin-3xl: 268px;

  /* Shadows */
  --shadow-sm: 0px 6.79px 19.24px -5.66px #3a4de926;
  --shadow-md: 0px 4px 6px -2px #1018280d;
  --shadow-lg: 0px 12px 16px -4px #1018281a;
  --shadow-3:
    0px 12px 16px -4px rgba(16, 24, 40, 0.1), 0px 4px 6px -2px
    rgba(16, 24, 40, 0.05);
  /*
  mini label
  font-size: 8px;
  line-height: 12px;
  */
  --text-3xs: 0.5rem;
  --text-3xs--line-height: calc(1 / 0.5);
  /*
    Label:
    font-size: 10px;
    line-height: 16px;
    */
  --text-2xs: 0.625rem;
  --text-2xs--line-height: calc(1 / 0.625);
  /*
    Caption / Small body
    font-size: 12px;
    line-height: 16px;
    */
  --text-xs: 0.75rem;
  --text-xs--line-height: calc(1 / 0.75);

  /*
    Input Medium / Medium body / Subheadline
    font-size: 14px;
    line-height: 16px;
    */
  --text-sm: 0.875rem;
  --text-sm--line-height: calc(1.25 / 0.875);

  /*
    Base / Headline
    font-size: 16px;
    line-height: 24px;
    */
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);

  --text-lg: 1.125rem;
  --text-lg--line-height: calc(1.75 / 1.125);

  /*
  Title 3 (H6)
  font-style: semibold;
  font-size: 20px;
  line-height: 26px;
  letter-spacing: -1%;
  */
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);

  /*
      Title 2 (H5)
      font-style: semibold;
      font-size: 24px;
      line-height: 32px;
      letter-spacing: -1%;
     */
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);

  /*
      Title 1 (H4)
      font-style: bold;
      font-size: 28px;
      line-height: 36px;
      letter-spacing: -1%;
     */
  --text-3xl: 1.75rem;
  --text-3xl--line-height: calc(2.25 / 1.75);
  /*
      Title 0 (H3)
      font-style: bold;
      font-size: 30px;
      line-height: 36px;
      letter-spacing: -1%;
     */
  --text-4xl: 1.875rem;
  --text-4xl--line-height: calc(2.25 / 1.875);

  /*
      Large Title (H2)
      font-style: extrabold;
      font-size: 36px;
      line-height: 40px;
      letter-spacing: -1%;
     */
  --text-5xl: 2.25rem;
  --text-5xl--line-height: calc(2.5 / 2.25);
  /*
      Special Title (H1)
      font-style: extrabold;
      font-size: 40px;
      line-height: 56px;
      letter-spacing: -1%;
     */

  --text-6xl: 2.5rem;
  --text-6xl--line-height: calc(3.5 / 2.5);
  --text-7xl: 3rem;
  --text-7xl--line-height: 1;
  --text-8xl: 3.75rem;
  --text-8xl--line-height: 1;
  --text-9xl: 4.5rem;
  --text-9xl--line-height: 1;
  --text-10xl: 6rem;
  --text-10xl--line-height: 1;
  --text-11xl: 8rem;
  --text-11xl--line-height: 1;
  --text-12xl: 10rem;
  --text-12xl--line-height: 1;

  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;

  --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl:
    0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);
  --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);
  --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);

  --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);
  --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
  --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
  --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
  --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);
  --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --perspective-dramatic: 100px;
  --perspective-near: 300px;
  --perspective-normal: 500px;
  --perspective-midrange: 800px;
  --perspective-distant: 1200px;
  --aspect-video: 16 / 9;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
  --animate-gradient-x: gradient-x 5s ease-in-out;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  @keyframes ping {
    75%,
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
  @keyframes pulse {
    50% {
      opacity: 0.5;
    }
  }
  @keyframes bounce {
    0%,
    100% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
      transform: none;
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  @keyframes gradient-x {
    0% {
      background-position: 0% 0%;
    }
    100% {
      background-position: 100% 100%;
    }
  }
}

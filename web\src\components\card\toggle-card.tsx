"use client";

import { Button, Typography } from "@components/ui";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowRight, Plus } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";

type CardProps = {
  item: any;
};
//----------------------------------------------------------------------------------
export default function ToggleCard({ item }: CardProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div
      onMouseLeave={() => setExpanded(false)}
      className="relative p-7.5 rounded-[20px] bg-[#FCFCFD] flex flex-col gap-y-2 md:gap-y-6 justify-between"
    >
      <div className="flex flex-col gap-4">
        <Typography variant="title-2" classname="text-black">
          {item?.title}
        </Typography>
        <div
          className="text-emphasize text-base font-normal leading-6"
          dangerouslySetInnerHTML={{ __html: item?.description }}
        />
      </div>
      {/* Mô tả & hình ảnh */}
      <div className="relative h-[173px] overflow-hidden">
        {/* <motion.img
          src={item?.image}
          alt="overlay"
          className="w-auto h-full aspect-square absolute left-0"
          animate={expanded ? { opacity: 0, y: 40 } : { opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        /> */}
        <motion.div
          className="w-auto h-full aspect-square absolute left-0"
          animate={
            expanded && item.animation
              ? { opacity: 0, y: 40 }
              : { opacity: 1, y: 0 }
          }
          transition={{ duration: 0.4 }}
        >
          <Image
            src={item?.image}
            alt="overlay"
            fill // Or use width/height if preferred
            className="object-cover"
          />
        </motion.div>
      </div>

      {/* Overlay content */}
      {item.animation && (
        <AnimatePresence>
          {expanded && (
            <motion.div
              key="expanded"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
              className="absolute inset-0 text-white p-7.5 rounded-[20px] z-10"
              style={{
                background: "linear-gradient(360deg, #0DD5A2 0%, #04A87E 100%)",
              }}
            >
              <div className="flex flex-col gap-4">
                <Typography variant="title-2">{item?.title}</Typography>
                <div
                  className="text-white text-base font-normal leading-6"
                  dangerouslySetInnerHTML={{ __html: item?.content }}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}

      <Button
        onClick={() => setExpanded(!expanded)}
        variant={"ghost"}
        size={"icon"}
        className={cn(
          "absolute max-h-max bottom-7.5 right-7.5 z-10 rounded-full  cursor-pointer",
          item.animation &&
            "group hover:!bg-brand ease-in-out transition-all duration-500",
        )}
      >
        {item.icon === "plus" && (
          <Plus
            className={cn(
              "stroke-1 size-10.5 group-hover:scale-70 group-hover:text-white ease-in-out transition-all duration-500",
              expanded && "rotate-45 scale-70 text-white",
            )}
          />
        )}
        {item.icon === "arrow-right" && (
          <Link href={`research/${item?.slug ?? "#"}`}>
            <ArrowRight className={cn("stroke-1 size-10.5")} />
          </Link>
        )}
      </Button>
    </div>
  );
}

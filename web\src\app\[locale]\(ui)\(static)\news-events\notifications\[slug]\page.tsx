import { NotificationsDetailView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Notifications Detail - Kafi",
  description: "Notifications Detail - Kafi",
};

const NotificationDetail = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <NotificationsDetailView />
    </Suspense>
  );
};

export default NotificationDetail;

export const dynamic = "force-dynamic";

import { Button, Typography } from "@components/ui";
import ToggleCard from "@components/card/toggle-card";
import Link from "next/link";

//----------------------------------------------------------------------------------
type TProps = {
  data: any;
};
//----------------------------------------------------------------------------------

export default function ToggleCardsSection({ data }: TProps) {
  const { heading, sub_heading, buttons, items } = data || {};

  return (
    <section className="container !py-0">
      <div className="max-w-[1066px] mx-auto flex flex-col items-center gap-6 md:gap-9">
        <div className="flex flex-col gap-6 max-w-[635px] text-center">
          {heading ? (
            <Typography
              variant="large-title"
              classname="text-black max-md:text-2xl max-lg:text-3xl"
            >
              {heading}
            </Typography>
          ) : null}
          {sub_heading ? (
            <Typography classname="text-black">{sub_heading}</Typography>
          ) : null}
        </div>

        {buttons?.length ? (
          <div className="flex items-center gap-x-8">
            {buttons?.map((item: any, idx: number) => (
              <Link href={item?.url} key={idx}>
                <Button variant="outline">{item?.text}</Button>
              </Link>
            ))}
          </div>
        ) : null}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          {items?.map((item: any, idx: number) => (
            <ToggleCard key={idx} item={item} />
          ))}
        </div>
      </div>
    </section>
  );
}

type FacebookIconProps = {
  size?: number;
  className?: string;
};

export const FacebookIcon = ({ size = 24, className }: FacebookIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3382_13283)">
        <path
          d="M10.7865 19V12.4308H8.99951V10.0656H10.7865V8.04535C10.7865 6.45785 11.842 5 14.2742 5C15.259 5 15.9872 5.09177 15.9872 5.09177L15.9298 7.30049C15.9298 7.30049 15.1872 7.29347 14.3768 7.29347C13.4997 7.29347 13.3591 7.68638 13.3591 8.33851V10.0656H15.9995L15.8846 12.4308H13.3591V19H10.7865Z"
          fill="#767C82"
        />
      </g>
      <defs>
        <clipPath id="clip0_3382_13283">
          <rect
            width="7"
            height="14"
            fill="white"
            transform="translate(8.99951 5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

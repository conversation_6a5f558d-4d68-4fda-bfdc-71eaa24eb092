// import Autoplay from 'embla-carousel-autoplay'
import { <PERSON><PERSON>, Typography } from "@/components/ui";
import { ChevronRight } from "lucide-react";
import Image from "next/image";

//----------------------------------------------------------------------------------
const _DATA = [
  {
    title: "pro",
    description:
      "Nền tảng giao dịch chuyên nghiệp, phân tích chuyên sâu, công cụ hỗ trợ đa dạng",
    buttons: [
      {
        text: "Giao dịch ngay",
        url: "#",
      },
    ],
    image: "/images/trade/k-pro.png",
    image_max_width: 525,
  },
  {
    title: "app",
    description:
      "Nền tảng giao dịch di động n<PERSON>h chóng, tiện lợi. <br /> <PERSON><PERSON><PERSON> dịch mọi lúc mọi nơi một cách bảo mật",
    buttons: [],
    image: "/images/trade/k-app.png",
    image_max_width: 428,
    QR: "/images/trade/k-qr.png",
  },
];

//----------------------------------------------------------------------------------
export default function AvailablePlatforms() {
  return (
    <section className="container">
      <div className="content flex flex-col gap-6 md:gap-10">
        <div className="w-full flex justify-center text-center">
          <Typography
            variant="large-title"
            classname="max-md:text-2xl max-lg:text-3xl"
          >
            Nền tảng giao dịch cho
            <br /> Nhà đầu tư
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {_DATA?.map((item: any, idx: number) => (
            <CardItem key={idx} item={item} />
          ))}
        </div>
      </div>
    </section>
  );
}

function CardItem({ item }: { item: any }) {
  return (
    <div className="relative h-full flex flex-col items-center justify-between gap-6 lg:gap-10 bg-white rounded-4xl pt-8 lg:pt-10 overflow-hidden">
      <div className="flex flex-col gap-4 text-center max-w-[423px]">
        <Typography
          variant={"title-3"}
          className="font-semibold text-[#1E2328] capitalize"
        >
          <span>Kafi</span>
          <span className="upcase text-brand"> X</span>
          <span> {item?.title}</span>
        </Typography>
        <div
          className="text-emphasize font-normal text-base leading-6"
          dangerouslySetInnerHTML={{ __html: item?.description }}
        ></div>
        <div className="flex items-center justify-center gap-4">
          {item?.buttons &&
            item?.buttons?.map((item: any, idx: number) => (
              <Button
                key={idx}
                variant={"ghost"}
                className="!text-brand p-0 hover:!bg-transparent"
              >
                {item?.text}
                <ChevronRight />
              </Button>
            ))}
        </div>
      </div>
      <div
        className="w-full flex justify-center sm:justify-start"
        style={{
          maxWidth: item?.image_max_width,
        }}
      >
        <Image
          src={item?.image}
          alt="icon"
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto"
        />
      </div>
      {item?.QR && (
        <Image
          src={item?.QR}
          alt="icon"
          width={0}
          height={0}
          sizes="100vw"
          className="w-[73px] h-auto aspect-square absolute left-5 bottom-5 hidden lg:block"
        />
      )}
    </div>
  );
}

"use client";

import { <PERSON><PERSON>, Typography } from "#/src/components/ui";
import { LineHelpCircleIcon, LineMarkerPinIcon } from "@components/svg";
import Link from "next/link";

interface SupportCardData {
  id: string;
  title: string;
  icon: React.ReactNode;
  description?: string;
  phone?: string;
  email?: string;
  buttonText?: string;
  buttonLink?: string;
}

const supportCards: SupportCardData[] = [
  {
    id: "contact",
    title: "Hội sở",
    icon: <LineMarkerPinIcon />,
    description:
      "Tầng 14, Tòa nhà Sailing Tower, 111A Pasteur, Phường Sài Gòn, TP. Hồ Chí Minh",
    phone: "1900 633 322",
    email: "<EMAIL>",
  },
  {
    id: "faq",
    title: "FAQs",
    icon: <LineHelpCircleIcon />,
    description: "Giúp bạn tìm hiểu nhiều hơn về sản phẩm và các nền tảng Kafi",
    buttonText: "<PERSON><PERSON><PERSON> hiểu thêm",
    buttonLink: "#",
  },
];

function SupportCard({ card }: { card: SupportCardData }) {
  return (
    <div className="rounded-2xl px-8 py-5  border-0 bg-white flex flex-col justify-between flex-1 h-[220px]">
      <div className="flex flex-col items-start h-full justify-between">
        {/* Title */}
        <div className="flex justify-between items-center w-full py-2">
          <Typography variant="title-3">{card.title}</Typography>
          <div className="flex-shrink-0">{card.icon}</div>
        </div>

        {/* Content */}
        <div className="flex justify-between items-start flex-col gap-5">
          {card.description && (
            <Typography>
              {card.description.split("\n").map((line, index) => (
                <span key={index}>
                  {line}
                  <br />
                </span>
              ))}
            </Typography>
          )}
          {(card.phone || card.email) && (
            <div className="flex items-center gap-4 py-2">
              {card.phone && (
                <Link href={`tel:${card.phone}`}>
                  <Typography
                    variant="body-medium"
                    className="font-medium hover:text-brand text-black transition-all duration-300"
                  >
                    {card.phone}
                  </Typography>
                </Link>
              )}
              {card.phone && card.email && (
                <span className="text-black">|</span>
              )}
              {card.email && (
                <Link href={`mailto:${card.email}`}>
                  <Typography
                    variant="body-medium"
                    className="hover:text-brand text-black transition-all duration-300"
                  >
                    {card.email}
                  </Typography>
                </Link>
              )}
            </div>
          )}
          {card.buttonText && card.buttonLink && (
            <Button
              variant="text"
              className="text-black hover:text-brand px-0"
              asChild
            >
              <Link href={card.buttonLink}>{card.buttonText}</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

export default function SupportInfo() {
  return (
    <section className="container flex flex-col gap-10">
      {/* Title */}
      <Typography
        variant="large-title"
        className="max-md:text-2xl max-lg:text-3xl"
      >
        Bạn cần hỗ trợ thêm?
      </Typography>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Mapping Support Cards */}
        {supportCards.map((card) => (
          <SupportCard key={card.id} card={card} />
        ))}
      </div>
    </section>
  );
}

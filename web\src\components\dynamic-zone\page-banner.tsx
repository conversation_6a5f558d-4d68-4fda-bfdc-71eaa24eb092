import Image from "next/image";
import { Typography } from "#/src/components/ui";
//-----------------------------------------------------------
type Props = {
  data: any;
};
//-----------------------------------------------------------
export default function PageBanner({ data }: Props) {
  const { banner, heading } = data || {};
  return (
    <div className="flex flex-col gap-6 w-full items-center py-10 md:py-12">
      {banner?.url && (
        <Image
          src={banner?.url}
          alt="research banner"
          width={0}
          height={0}
          className="w-full h-[160px] md:h-[250px] object-cover"
          sizes="100vw"
        />
      )}
      <div
        className="container"
        style={{
          maxWidth: heading?.max_width ? `${heading?.max_width}px` : "100%",
        }}
      >
        <div className="flex flex-col gap-4 justify-center items-center text-center">
          {heading?.heading && (
            <Typography variant="special-title">{heading?.heading}</Typography>
          )}
          {heading?.sub_heading && (
            <Typography variant="body-regular">
              {heading?.sub_heading}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
}

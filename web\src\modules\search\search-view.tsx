"use client";

import { Typography } from "#/src/components/ui/typography";
import { InputSearch } from "#/src/components/ui/input";
import { useEffect, useMemo, useRef, useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Button } from "#/src/components/ui";
import { _searchCategories, _searchData } from "#/src/__mock__/search";
import { cn } from "#/src/lib";
import SearchDataList from "./search-data-list";

const getActiveClass = (current: string | null, value: string) => {
  if (value == "all" && !current)
    return "!bg-screen !text-emphasize !shadow-3 ";
  return current === value
    ? "!bg-screen !text-emphasize !shadow-3 "
    : "!bg-transparent !text-default !shadow-none";
};

const getActiveTextVariant = (current: string | null, value: string) => {
  if (value == "all" && !current) return "body-medium";
  return current === value ? "body-medium" : "body-regular";
};

export default function SearchView() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [screenWidth, setScreenWidth] = useState<number>(0);
  const [searchValue, setSearchValue] = useState("");

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const search = searchParams.get("search");
  const router = useRouter();

  const categoryParam = searchParams.get("q");

  const handleCategory = (category: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("q", category);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };

  const filteredData = useMemo(() => {
    const matchesSearch = (item: any, searchTerm: string) => {
      const lowerSearch = searchTerm.toLowerCase();
      return (
        item.title.toLowerCase().includes(lowerSearch) ||
        item.description.toLowerCase().includes(lowerSearch) ||
        item.category.title.toLowerCase().includes(lowerSearch)
      );
    };

    const matchesCategory = (item: any, category: string) => {
      return item.category.slug === category;
    };

    return _searchData.filter((item) => {
      const searchMatch = !search || matchesSearch(item, search);
      const categoryMatch =
        !categoryParam || matchesCategory(item, categoryParam);
      return searchMatch && categoryMatch;
    });
  }, [search, categoryParam]);

  const handleSearch = () => {
    const params = new URLSearchParams(searchParams);
    params.set("search", searchValue);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };
  useEffect(() => {
    setSearchValue(search || "");
  }, [search]);
  useEffect(() => {
    const updateWidths = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
      setScreenWidth(window.innerWidth);
    };

    updateWidths(); // Initial

    const observer = new ResizeObserver(updateWidths);
    if (containerRef.current) observer.observe(containerRef.current);

    window.addEventListener("resize", updateWidths);

    return () => {
      observer.disconnect();
      window.removeEventListener("resize", updateWidths);
    };
  }, []);
  const renderHead = (
    <div className="flex flex-col md:justify-center md:items-center gap-12 container">
      <Typography variant="special-title" className="text-center">
        Tìm kiếm
      </Typography>

      <div className="flex flex-col gap-4">
        <InputSearch
          placeholder="Tìm kiếm"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={handleSearch}
          className="w-full md:w-lg h-14 bg-white block mx-auto rounded-xl"
        />
        <Typography variant="body-regular" className="text-default text-center">
          Có <span className="font-bold">{filteredData?.length}</span> kết quả
          tìm kiếm
        </Typography>
      </div>
    </div>
  );
  const renderCategory = (
    <div
      style={
        screenWidth <= 1024
          ? { marginLeft: (screenWidth - containerWidth) / 2 + 20 }
          : {}
      }
      className="flex items-start gap-5 bg-[#F1F3F5] rounded-full p-1 max-lg:overflow-x-auto lg:w-fit lg:mx-auto hide-scrollbar max-lg:px-2 max-lg:py-2"
    >
      <Button
        variant="outline"
        className={cn(
          "rounded-full !border-0 px-6 py-2",
          getActiveClass(categoryParam, "all"),
        )}
        onClick={() => handleCategory("")}
      >
        <Typography variant={getActiveTextVariant(categoryParam, "all")}>
          Tất cả ({_searchData.length})
        </Typography>
      </Button>
      {_searchCategories.map((category, index) => (
        <Button
          key={index}
          variant="outline"
          className={cn(
            "rounded-full !border-0 px-6 py-2",
            getActiveClass(categoryParam, category.slug),
          )}
          onClick={() => handleCategory(category.slug)}
        >
          <Typography
            variant={getActiveTextVariant(categoryParam, category.slug)}
          >
            {category.title} ({category.total})
          </Typography>
        </Button>
      ))}
    </div>
  );
  return (
    <div className=" space-y-8 min-h-[817px]">
      <div className="flex flex-col gap-4">
        {renderHead}
        {renderCategory}
      </div>
      <div className="container lg:!max-w-[921px] " ref={containerRef}>
        <SearchDataList
          searchData={filteredData || []}
          pagination={{
            page: 1,
            pageSize: 10,
            total: filteredData?.length || 0,
          }}
        />
      </div>
    </div>
  );
}

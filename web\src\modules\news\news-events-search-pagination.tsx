import { TNewsEvents } from "@/types/news-event";
import NewsEventsCard from "./news-events-card";
import { TPagination } from "@/types/common";
import AppPagination from "@/components/common/app-pagination";
import { cn } from "#/src/lib";

type TProps = {
  data: TNewsEvents[];
  pagination: TPagination;
  classname?: string;
  searchKeyword?: string;
  onPageChange?: (page: number) => void;
};

const NewsEventsSearchPagination = ({
  data,
  pagination,
  classname,
  searchKeyword,
  onPageChange,
}: TProps) => {
  const renderListPosts = (
    <div className={cn(classname)}>
      {data.map((item) => (
        <NewsEventsCard
          key={item.id}
          item={item}
          largeTitle
          type="horizontal"
          searchKeyword={searchKeyword}
        />
      ))}
    </div>
  );

  return (
    <div className="flex flex-col gap-10">
      {renderListPosts}

      <AppPagination
        page={pagination.page}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onChange={(nextPage) => {
          onPageChange?.(nextPage);
        }}
      />
    </div>
  );
};

export default NewsEventsSearchPagination;

import { usePathname } from "next/navigation";

/**
 * Hook to get current locale from pathname
 * @returns Current locale string (defaults to "vi")
 */
export const useLocale = () => {
  const pathname = usePathname();
  return pathname?.split("/")[1] ?? "vi";
};

export const createLocalizedPath = (path: string, locale?: string) => {
  const currentLocale = locale || "vi";
  // Remove leading slash if present
  const cleanPath = path.startsWith("/") ? path.slice(1) : path;
  return `/${currentLocale}/${cleanPath}`;
};

export const isPathActive = (pathname: string, targetPath: string) => {
  // Remove locale from pathname for comparison
  const pathWithoutLocale = pathname.split("/").slice(2).join("/");
  const targetWithoutSlash = targetPath.startsWith("/")
    ? targetPath.slice(1)
    : targetPath;

  // Special case for home page
  if (targetPath === "/" || targetWithoutSlash === "") {
    return pathWithoutLocale === "";
  }

  return pathWithoutLocale.startsWith(targetWithoutSlash);
};

/**
 * Gets the pathname without locale prefix
 */
export const getPathnameWithoutLocale = (pathname: string) => {
  const segments = pathname.split("/").slice(2);
  return segments.length > 0 ? `/${segments.join("/")}` : "/";
};

/**
 * Checks if a locale is valid
 */
export const isValidLocale = (
  locale: string,
  validLocales: string[] = ["vi", "en"],
) => {
  return validLocales.includes(locale);
};

{"name": "web", "version": "0.1.0", "private": true, "description": "Kafi.vn website", "type": "module", "scripts": {"dev": "next dev --turbopack", "prebuild": "lingui extract", "build": "next build", "start": "next start", "test": "vitest", "coverage": "vitest run --coverage", "lint": "next lint", "lingui:extract": "lingui extract --clean", "lingui:compile": "lingui compile"}, "dependencies": {"@lingui/core": "^5.3.2", "@lingui/loader": "^5.3.2", "@lingui/macro": "^5.3.2", "@lingui/react": "^5.3.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-form": "^1.14.1", "@vitest/ui": "^3.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "command": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "negotiator": "^1.0.0", "next": "15.3.5", "next-themes": "^0.4.6", "qs": "^6.14.0", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-is": "^19.1.1", "recharts": "^3.1.0", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@lingui/cli": "^5.3.2", "@lingui/swc-plugin": "^5.5.2", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.4", "@types/node": "^20", "@types/qs": "^6.14.0", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-istanbul": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4"}}
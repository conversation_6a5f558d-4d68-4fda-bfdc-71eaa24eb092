"use client";

import { useRouter } from "next/navigation";
import { memo, useEffect } from "react";

import { APP_CONFIG } from "@/config-global";
import { useSiteContext } from "@/context/use-site-context";
import { useLocale } from "@/hooks/use-locale";

type TProps = {
  globalData: any;
  navigationData: any;
  topbarData: any;
  locale: string;
};

const ClientSiteHandler = ({
  globalData,
  navigationData,
  topbarData,
  locale,
}: TProps) => {
  const { dispatch } = useSiteContext();

  const cookieLocale = useLocale();

  useEffect(() => {
    if (globalData) {
      dispatch({
        type: "SET_GLOBAL_DATA",
        globalData,
        locale,
      });
    }
    if (navigationData) {
      dispatch({
        type: "SET_NAVIGATION_DATA",
        navigationData,
        locale,
      });
    }
    if (topbarData) {
      dispatch({
        type: "SET_TOPBAR_DATA",
        topbarData,
        locale,
      });
    }
  }, [globalData, navigationData, topbarData, dispatch, locale]);

  const router = useRouter();

  useEffect(() => {
    const handleMessage = async (message: MessageEvent<any>) => {
      if (
        message.origin === APP_CONFIG.apiUrl &&
        message.data.type === "strapiUpdate"
      ) {
        router.refresh();
      }
    };

    // Add the event listener
    window.addEventListener("message", handleMessage);

    // Cleanup the event listener on unmount
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [router]);

  useEffect(() => {
    if (cookieLocale !== locale) {
      router.refresh();
    }
  }, [cookieLocale, router, locale]);

  return null;
};

export default memo(ClientSiteHandler);

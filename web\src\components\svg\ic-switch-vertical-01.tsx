import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
};

const IcSwitchVertical01 = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}: TProps) => {
  const pathScale = Number(size) / 24;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M15 1V17M15 17L11 13M15 17L19 13M5 17V1M5 1L1 5M5 1L9 5"
        stroke={"currentColor"}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcSwitchVertical01;

// Usage:
// <IcSwitchVertical01 size={24} hoverColor="text-brand" activeColor="text-brand" />

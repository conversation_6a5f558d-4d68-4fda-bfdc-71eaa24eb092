export const LOCATIONS = [
  {
    name: "<PERSON><PERSON><PERSON> sở",
    address:
      "<PERSON><PERSON><PERSON> 14, Tòa nhà Sailing Tower, 111A <PERSON>, <PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON> <PERSON><PERSON>",
    src: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2301.975649812895!2d106.69632066541561!3d10.780526011754093!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f00348cd76f%3A0xf73ec2bc57fee829!2sSailing%20Tower!5e0!3m2!1sen!2s!4v1753241695625!5m2!1sen!2s",
  },
  {
    name: "Phòng giao dịch",
    address:
      "Tầng 1, Toà nhà Saigon Tower, 29 <PERSON><PERSON>, <PERSON><PERSON><PERSON>ng <PERSON><PERSON>, T<PERSON><PERSON> <PERSON><PERSON>",
    src: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.3736637720494!2d106.69863767461297!3d10.782666489366504!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f5c5cc494b7%3A0xf2bd2e6c2a244102!2sSaigon%20Tower!5e0!3m2!1sen!2s!4v1753241781251!5m2!1sen!2s",
  },
  {
    name: "Chi nhánh Hà Nội",
    address:
      "Tầng 1, Tòa nhà CornerStone, số 16 Phố Phan Chu Trinh, Phường Cửa Nam, Thành phố Hà Nội",
    src: "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3724.2832717275355!2d105.85237737484877!3d21.021348680626044!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135abedabfe3a39%3A0xa2493989694d17ba!2sCorner%20Stone%20Building!5e0!3m2!1sen!2s!4v1753241822790!5m2!1sen!2s",
  },
];

export const CONTACT = [
  {
    name: "Hỗ trợ",
    phone: "1900 633 322",
    email: "<EMAIL>",
    text: "",
    icon: "/icons/location/headphones.png",
  },
  {
    name: "Đánh giá & ý kiến",
    phone: "",
    email: "",
    text: "Gửi phản hồi",
    icon: "/icons/location/message-chat-square.png",
  },
  {
    name: "Câu hỏi thường gặp",
    phone: "",
    email: "",
    text: "Tìm hiểu thêm",
    icon: "/icons/location/help-circle.png",
  },
];

import { Typography } from "#/src/components/ui";
import IconBoxCardCustom from "./icon-box-card-custom";

export default function Vision({ items }: { items: any }) {
  return (
    <div className="flex flex-col gap-10 mx-auto">
      <div className="flex flex-col gap-6 max-w-[761px] mx-auto">
        <Typography
          variant="large-title"
          classname="text-center max-lg:text-3xl max-md:text-2xl"
        >
          Tầm nhìn & <PERSON><PERSON> mệnh
        </Typography>
        <Typography
          variant="body-regular"
          classname="text-emphasize text-center"
        >
          Vớ<PERSON> hơn 18 năm phát triển bền vững, <PERSON><PERSON> khẳng định vị thế là một
          trong những công ty chứng khoán uy tín và năng động hàng đầu trên Thị
          trường Chứng khoán (TTCK) Việt Nam
        </Typography>
      </div>
      <div className="max-w-[1065px] mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {items.map((item: any, idx: number) => (
            <IconBoxCardCustom key={idx} item={item} />
          ))}
        </div>
      </div>
    </div>
  );
}

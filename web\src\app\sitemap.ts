import type { MetadataRoute } from "next";

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://kafi.vn";

const pages = ["/trade", "/trade/fee", "/gbi"];

const pageSitemap = pages.map((page) => ({
  url: `${baseUrl}${page}`,
  alternates: {
    languages: {
      vi: `${baseUrl}${page}`,
      en: `${baseUrl}${page}`,
    },
  },
  lastModified: new Date(),
  changeFrequency: "weekly",
  priority: 0.5,
  images: [`${baseUrl}/images/logo.png`],
})) as MetadataRoute.Sitemap;

export default function sitemap(): MetadataRoute.Sitemap {
  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 1,
      alternates: {
        languages: {
          vi: `${baseUrl}`,
          en: `${baseUrl}`,
        },
      },
      images: [`${baseUrl}/logo/logo-color.png`],
    },
    ...pageSitemap,
  ];
}

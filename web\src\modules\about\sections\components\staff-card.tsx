import { Typography } from "#/src/components/ui";
import Image from "next/image";
import { StaffProp } from "../team-view";
import { cn } from "#/src/lib";

export default function StaffCard({
  pronoun,
  name,
  position,
  experience,
  image,
  imageClassName,
}: StaffProp) {
  return (
    <div className="flex flex-col gap-6">
      <Image
        src={image}
        alt="staff image"
        width={0}
        height={0}
        sizes="(max-width: 768px) 100vw, (min-width: 1200px) 50vw"
        className={cn("w-full h-auto rounded-lg object-cover", imageClassName)}
      />
      <div className="flex flex-col gap-4">
        <div className="flex flex-col">
          <Typography
            variant="title-2"
            classname="text-emphasize max-lg:text-xl"
          >
            <span>{pronoun}</span> {name}
          </Typography>
          <Typography variant="headline" classname="text-emphasize">
            {position}
          </Typography>
        </div>
        <Typography variant="body-regular" classname="text-emphasize">
          {experience}
        </Typography>
      </div>
    </div>
  );
}

"use client";

import { AWARDS_DATA } from "#/src/__mock__/awards";
import { Typography } from "#/src/components/ui";
import Image from "next/image";

export default function AwardsView() {
  return (
    <div className="flex flex-col gap-10">
      <Image
        src={"/images/about/awards-page-banner.png"}
        alt="research banner"
        width={0}
        height={0}
        className="hidden md:flex  w-full h-auto max-h-[250px]"
        sizes="(max-width: 768px) 100vw, (min-width: 1024px) 80vw"
      />
      <div className="flex flex-col container !py-0 gap-6">
        <div className="flex flex-col gap-4 text-center">
          <Typography
            variant="special-title"
            classname="max-md:font-semibold max-md:text-2xl max-lg:text-[28px]"
          >
            <PERSON><PERSON> v<PERSON> c<PERSON> g<PERSON><PERSON> thưởng nổi bật
          </Typography>
          <Typography variant={"body-regular"} className="text-emphasize">
            <PERSON><PERSON><PERSON><PERSON> trao bởi cái tổ chức uy tín trong nước và quốc tế
          </Typography>
        </div>
        <div className="flex flex-col gap-8 max-w-[1065px] mx-auto">
          {AWARDS_DATA.map((award, index) => (
            <div
              key={index}
              className="p-10 bg-screen rounded-2xl flex flex-col lg:flex-row gap-6 lg:even:flex-row-reverse relative"
            >
              <div
                className="max-lg:hidden absolute w-[613px] h-auto aspect-square"
                style={{
                  opacity: "0.05",
                  background:
                    "linear-gradient(153deg, #C600AC -7.37%, #12B88E 80.62%)",
                  filter: "blur(107.80000305175781px)",
                  bottom: index % 2 === 0 ? "-153px" : "-229px",
                  ...(index % 2 === 0
                    ? { right: "-112.5px" }
                    : { left: "-223.5px" }),
                }}
              ></div>
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-4">
                  <Typography variant="title-1" classname="text-[#070707]">
                    {award.title}
                  </Typography>
                  <Typography variant="title-3" classname="text-brand">
                    {award.category}
                  </Typography>
                </div>
                <div
                  className="text-base leading-6 font-normal text-emphasize"
                  dangerouslySetInnerHTML={{ __html: award.content }}
                ></div>
              </div>
              <Image
                src={award.image}
                alt={`index ${index + 1}`}
                width={0}
                height={0}
                sizes="100vw"
                className="w-auto h-[320px] aspect-square object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

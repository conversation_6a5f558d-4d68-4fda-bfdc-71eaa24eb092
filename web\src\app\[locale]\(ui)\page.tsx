import { initLingui } from "#/src/i18n/init-lingui";
import { HomeView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ locale: string }>;
}) => {
  const { locale } = await params;
  const i18n = initLingui(locale);
  return {
    title: i18n._(`Home - Kafi`),
    description: i18n._(`Kafi - The Future of Finance`),
  };
};

export default async function Home() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <HomeView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

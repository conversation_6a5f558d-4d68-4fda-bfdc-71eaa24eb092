import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Typography,
} from "@/components/ui";

const Faqs = ({ dataFAQ, className }: { dataFAQ: any; className?: string }) => {
  return (
    <div className="container">
      <div className={cn("grid grid-cols-1 gap-10 lg:grid-cols-3", className)}>
        {/**use heading section later */}
        <div className="col-span-1 mt-2">
          <Typography
            variant="large-title"
            classname="max-md:text-2xl max-lg:text-3xl"
          >
            Câu hỏi thường gặp
          </Typography>
        </div>
        <div className="col-span-1 lg:col-span-2">
          <Accordion type="single" collapsible>
            {dataFAQ.map((data: any, index: number) => (
              <AccordionItem
                key={index}
                value={`item-${index + 1}`}
                className=" border-[#E2E2E2]"
              >
                <AccordionTrigger className="hover:no-underline cursor-pointer py-6 data-[state=open]:pb-4">
                  <div className="w-full flex justify-between font-semibold text-[#1E2328] ">
                    <div
                      dangerouslySetInnerHTML={{ __html: data.question }}
                      className="max-lg:text-xl scroll-m-20 text-2xl font-semibold leading-[32px] tracking-[-1%]"
                    ></div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="flex flex-col gap-4 text-sm md:text-base pb-6 text-[#1E2328] font-normal md:max-w-[584px] lg:max-w-[780px] text-wrap">
                  <div
                    className="text-[#1E2328] font-normal text-base leading-6"
                    dangerouslySetInnerHTML={{ __html: data.answer }}
                  ></div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default Faqs;

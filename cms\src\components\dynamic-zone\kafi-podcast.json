{"collectionName": "components_dynamic_zone_kafi_podcasts", "info": {"displayName": "Kafi_Podcast"}, "options": {}, "attributes": {"section_styles": {"type": "component", "component": "elementals.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.section-heading", "repeatable": false}, "podcast": {"type": "component", "component": "elementals.podcast", "repeatable": false}}, "config": {}}
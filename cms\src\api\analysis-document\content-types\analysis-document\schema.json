{"kind": "collectionType", "collectionName": "analysis_documents", "info": {"singularName": "analysis-document", "pluralName": "analysis-documents", "displayName": "Analysis - Document"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "summary": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::analysis-category.analysis-category", "inversedBy": "documents"}, "file": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["files"]}}}
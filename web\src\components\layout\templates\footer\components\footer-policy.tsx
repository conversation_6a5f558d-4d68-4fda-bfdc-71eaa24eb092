"use client";

import Image from "next/image";
import { useState } from "react";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { Typography } from "#/src/components/ui";
import { mockSocialIcons } from "#/src/__mock__";
import { mockTermsAndCondition } from "#/src/__mock__/terms-and-condition";

const FooterPolicy = () => {
  const [selectedValue, setSelectedValue] = useState<any>(
    mockTermsAndCondition[0],
  );
  const isMobile = useMediaQuery("(max-width: 743px)");

  return (
    <div className={cn("flex flex-col gap-5 ", isMobile && "gap-0 ")}>
      <div
        className={cn(
          "w-full flex py-3.5 justify-between border-y-[0.5px] border-y-[#E5E5E5] scrollbar-hide",
        )}
      >
        <div className="flex gap-2 md:gap-4 lg:gap-14 overflow-x-auto whitespace-nowrap hide-scrollbar">
          {mockTermsAndCondition.map((policy, index) => (
            <p
              key={index}
              className="font-semibold text-gray-900 text-xs leading-4 cursor-pointer hover:text-brand"
              onClick={() => setSelectedValue(policy)}
            >
              {policy.name}
            </p>
          ))}
        </div>
        {!isMobile && (
          <div className="flex gap-4">
            {mockSocialIcons.map((icon, index) => (
              <Image
                key={index}
                src={icon}
                alt={`icon ${index + 1}`}
                width={0}
                height={0}
                sizes="100vw"
                className="w-4 h-auto cursor-pointer"
              />
            ))}
          </div>
        )}
      </div>
      <div
        className={cn(
          "text-[10px] text-[#585858] font-medium leading-4 pl-3",
          isMobile && "pt-5 pb-4",
        )}
      >
        <Typography variant="special-label">
          Trang thông tin điện tử tại địa chỉ https://kafi.vn là tài sản của
          Công ty Cổ phần Chứng khoán KAFI (Kafi), bao gồm các điều khoản và
          điều kiện quản lý, sử dụng trang thông tin điện tử này
        </Typography>
        <div>
          <ol className="list-decimal-no-dot space-y-2 flex gap-1 flex-col">
            {selectedValue.content.map((item: any, itemIndex: any) => (
              <li key={itemIndex}>
                <div className="inline">
                  <div className="flex-col inline-flex">
                    <Typography variant="special-label" className="font-medium">
                      {item.title}
                    </Typography>
                    <Typography variant="special-label">
                      {item.description}
                    </Typography>
                  </div>
                </div>
              </li>
            ))}
          </ol>
        </div>
      </div>
      <div
        className={cn(
          "w-full flex py-6 justify-between border-t-[0.5px] border-y-[#E5E5E5]",
        )}
      >
        <p className="text-[10px] xl:text-xs font-normal text-[#262626] leading-4">
          Bản quyền thuộc về Công ty cổ phần chứng khoán Kafi
        </p>
        <p className="text-xs font-normal text-[#262626] leading-4">English</p>
      </div>
    </div>
  );
};

export default FooterPolicy;

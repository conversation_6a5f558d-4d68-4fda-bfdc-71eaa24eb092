import { Button } from "#/src/components/ui";
import Link from "next/link";
//--------------------------------------------------------------------------
type CmsButtonsProps = {
  buttons: any;
};
//--------------------------------------------------------------------------
export default function CmsButtons({ buttons }: CmsButtonsProps) {
  return (
    <div className="w-full flex items-center gap-4 flex-wrap">
      {buttons?.map((button: any, idx: number) => (
        <Link key={idx} href={button?.url} target={button?.target}>
          <Button variant={button?.variant} className="min-w-max">
            {button?.text}
          </Button>
        </Link>
      ))}
    </div>
  );
}

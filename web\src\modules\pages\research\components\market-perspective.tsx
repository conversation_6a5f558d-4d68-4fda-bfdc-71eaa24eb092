import { MOCK_MARKET_PERSPECTIVE } from "#/src/__mock__/research";
import { Typography } from "#/src/components/ui";

const MarketPerspective = () => {
  return (
    <div className="container !max-w-[1066px] flex flex-col gap-10 lg:py-10">
      <div className="flex flex-col gap-6 text-start md:text-center">
        <Typography variant="title-1"><PERSON><PERSON><PERSON> nhìn thị trường mới nhất</Typography>
        <Typography variant="body-regular">
          Tổng hợp các phân tích chuyên sâu và tin tức mới nhất từ <PERSON>, gi<PERSON>p
          bạn nắm bắt kịp thời diễn biến thị trường.
        </Typography>
      </div>
      <div className="flex flex-col gap-6">
        {MOCK_MARKET_PERSPECTIVE.map((market, index) => (
          <MarketItem key={index} {...market} />
        ))}
      </div>
    </div>
  );
};

export default MarketPerspective;

type MarketItemProps = {
  category: string;
  title: string;
  publishedAt: string; // hoặc có thể dùng Date nếu bạn sẽ parse nó
  content: string;
};

const MarketItem = ({
  category,
  title,
  publishedAt,
  content,
}: MarketItemProps) => {
  return (
    <div className="w-full bg-transparent max-md:last:border-0 border-b md:border border-subtle lg:border-0 rounded-none lg:bg-screen md:rounded-2xl flex">
      <div className="hidden p-6 w-full max-w-[175px] h-auto aspect-square border-r-2 border-border-default lg:flex items-center relative">
        <Typography variant="title-3" classname="text-brand">
          {category}
        </Typography>
        <div
          id="#filter0_nf_3277_5148"
          className="w-[303px] h-[187px] absolute top-[63px] left-[-69px]"
          style={{
            background:
              "radial-gradient(50% 50% at 50% 50%, rgba(0, 198, 148, 0.15) 0%, rgba(22, 230, 132, 0.00) 100%)",
            opacity: 0.4,
            filter: "blur(29.47882652282715px)",
          }}
        ></div>
      </div>
      <div className="pb-3 md:p-6 w-full flex gap-10">
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-1">
            <Typography
              variant="subheadline-medium"
              classname="text-placeholder lg:hidden"
            >
              {category}
            </Typography>
            <Typography
              variant="title-2"
              classname="max-lg:font-semibold max-lg:text-xl max-md:text-base text-default line-clamp-2 lg:line-clamp-1 cursor-pointer text-wrap lg:text-balance"
            >
              {title}
            </Typography>
            <Typography variant="body-regular" classname="text-placeholder">
              {publishedAt}
            </Typography>
          </div>
          <Typography
            variant="body-regular"
            classname="lg:!line-clamp-2 text-default hidden lg:flex"
          >
            {content}
          </Typography>
        </div>
      </div>
    </div>
  );
};

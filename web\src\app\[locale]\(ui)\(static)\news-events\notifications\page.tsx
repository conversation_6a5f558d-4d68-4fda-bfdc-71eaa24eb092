import { NotificationsListView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Notifications - <PERSON><PERSON>",
  description: "Notifications - Kafi",
};

const NotificationsPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <NotificationsListView />
    </Suspense>
  );
};

export default NotificationsPage;

export const dynamic = "force-dynamic";

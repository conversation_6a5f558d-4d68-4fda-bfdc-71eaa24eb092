"use client";
import Image from "next/image";
import JobOpenings from "./components/job-openings";
import RecruitmentWhyJoinUs from "./components/recruitmnet-why-join-us";
import { useSearchStore } from "#/src/store/use-open-search-store";
import JobPositionPrompt from "./components/job-position-prompt";

export default function RecruitmentView() {
  const { setOpenSearch } = useSearchStore();

  return (
    <div className="flex flex-col gap-10" onClick={() => setOpenSearch(false)}>
      <Image
        src={"/images/research/research-banner.png"}
        alt="research banner"
        width={0}
        height={0}
        className="w-0 md:w-full h-auto"
        sizes="(max-width: 768px) 100vw, (min-width: 1024px) 80vw"
      />
      <div className="w-full flex flex-col gap-10 container-inner lg:!max-w-[1066px] mx-auto">
        <RecruitmentWhyJoinUs />
        <JobOpenings />
        <JobPositionPrompt />
      </div>
    </div>
  );
}

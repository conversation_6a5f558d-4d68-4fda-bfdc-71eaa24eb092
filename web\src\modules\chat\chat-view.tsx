"use client";
import { LocalizedLink } from "#/src/components/common/localized-link";
import { Typography } from "#/src/components/ui";
import { Input } from "#/src/components/ui/input";
import Image from "next/image";
import { useState } from "react";

export default function ChatView() {
  const [searchValue, setSearchValue] = useState("");

  const handleChangeSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  return (
    <div className="min-h-[100vh] container pt-20">
      <div className="flex flex-col gap-7 max-w-[587px] h-full justify-end mx-auto ">
        <div className="flex flex-col gap-3">
          <div className="w-full flex justify-end">
            <div className="py-3.5 px-7 rounded-t-[21px] rounded-bl-[21px] rounded-br-[3px] bg-brand items-end w-fit">
              <Typography classname="text-white font-medium text-sm leading-5 ">
                <PERSON><PERSON> c<PERSON> bao nhiêu sản phẩm?
              </Typography>
            </div>
          </div>
          <div className="w-full flex items-start gap-4">
            <Image
              src="/images/stars.png"
              alt="stars"
              width={0}
              height={0}
              className="w-8 h-8 shrink-0"
              sizes="100vw"
            />
            <div className="flex flex-col gap-10 text-sm">
              <div>
                <Typography classname="text-emphasize font-bold text-sm">
                  1. Dịch vụ môi giới chứng khoán
                </Typography>
                <Typography classname="text-emphasize font-normal text-sm">
                  Đây là dịch vụ cốt lõi, giúp khách hàng thực hiện các giao
                  dịch mua bán cổ phiếu, trái phiếu và các loại chứng khoán khác
                  trên thị trường. Kafi cung cấp nền tảng giao dịch chuyên
                  nghiệp, tiện lợi và an toàn như Kafi Trade.
                </Typography>
              </div>
              <div>
                <Typography classname="text-emphasize font-bold text-sm">
                  2. Sản phẩm cho vay ký quỹ (Margin)
                </Typography>
                <Typography classname="text-emphasize font-normal text-sm">
                  Kafi đặc biệt chú trọng phát triển các gói sản phẩm Margin đa
                  dạng để tối ưu hóa đòn bẩy tài chính cho nhà đầu tư, bao gồm:
                </Typography>
                <ul className="list-disc list-inside pl-4">
                  <li className="text-emphasize font-bold text-sm">
                    Margin-Zero:{" "}
                    <span className="font-normal">
                      Gói vay ký quỹ với lãi suất 0% cho dư nợ cuối ngày lên đến
                      100 triệu VND. Đây là giải pháp lý tưởng cho các nhà đầu
                      tư mới hoặc có quy mô vốn nhỏ
                    </span>
                  </li>
                  <li className="text-emphasize font-bold text-sm">
                    Margin-S25:{" "}
                    <span className="font-normal">
                      Áp dụng lãi suất ưu đãi (ví dụ: 8%/năm) cho khách hàng đầu
                      tư theo danh mục cổ phiếu S25 do Kafi lựa chọn (thường là
                      các mã cổ phiếu ngân hàng và một số mã bluechip khác).
                    </span>
                  </li>
                  <li className="text-emphasize font-bold text-sm">
                    Margin-B10:{" "}
                    <span className="font-normal">
                      Dành cho các nhà đầu tư có dư nợ lớn (từ 10 tỷ VND trở
                      lên) với lãi suất cạnh tranh (ví dụ: 8.5%/năm).
                    </span>
                  </li>
                </ul>
              </div>
              <LocalizedLink href="/">
                <Typography classname="text-brand font-semibold">
                  Đi đến trang chi tiết
                </Typography>
              </LocalizedLink>
              <div>
                <Typography classname="text-emphasize font-bold text-sm">
                  3. Tư vấn đầu tư chứng khoán
                </Typography>
                <Typography classname="text-emphasize font-normal text-sm">
                  Kafi cung cấp các dịch vụ tư vấn chuyên sâu, hỗ trợ khách hàng
                  đưa ra quyết định đầu tư hiệu quả dựa trên phân tích thị
                  trường và tình hình tài chính cá nhân.
                </Typography>
              </div>
              <div>
                <Typography classname="text-emphasize font-bold text-sm">
                  4. Tự doanh chứng khoán
                </Typography>
                <Typography classname="text-emphasize font-normal text-sm">
                  Kafi cũng tham gia vào hoạt động tự doanh, tức là công ty trực
                  tiếp đầu tư vào các loại chứng khoán trên thị trường để tạo ra
                  lợi nhuận cho chính mình.
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full h-fit min-h-[52px] bg-white rounded-full px-8 py-2.5 group mx-auto relative flex items-center">
          <Input
            placeholder="Hỏi Kafi"
            className="p-0  border-none rounded-none placeholder:text-base placeholder:text-[#A3A3A3] placeholder:font-medium focus-visible:ring-0 focus-visible:ring-offset-0"
            onChange={handleChangeSearch}
            value={searchValue}
          />
          <Image
            src="/images/stars.png"
            alt="stars"
            width={0}
            height={0}
            className="w-8 h-8"
            sizes="100vw"
          />
        </div>
      </div>
    </div>
  );
}

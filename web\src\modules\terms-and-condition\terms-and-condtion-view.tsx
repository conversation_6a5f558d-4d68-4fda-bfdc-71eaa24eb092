"use client";

import { mockTermsAndCondition } from "#/src/__mock__/terms-and-condition";
import { Button, Typography } from "#/src/components/ui";
import { useEffect, useMemo, useRef, useState } from "react";
import ArchiveDialog from "./components/archive-dialog";
import { cn } from "#/src/lib";

export default function TermsAndConditionView() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [screenWidth, setScreenWidth] = useState<number>(0);

  const [open, setOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState(mockTermsAndCondition[0].value);

  const matchingContent = useMemo(() => {
    return mockTermsAndCondition.find((item) => item.value === activeTab);
  }, [activeTab]);

  useEffect(() => {
    const updateWidths = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
      setScreenWidth(window.innerWidth);
    };

    updateWidths(); // Initial

    const observer = new ResizeObserver(updateWidths);
    if (containerRef.current) observer.observe(containerRef.current);

    window.addEventListener("resize", updateWidths);

    return () => {
      observer.disconnect();
      window.removeEventListener("resize", updateWidths);
    };
  }, []);

  return (
    <div className="flex flex-col">
      <div className="w-full">
        <div className="h-fit w-full overflow-x-auto whitespace-nowrap hide-scrollbar">
          <div
            style={
              screenWidth <= 1024
                ? { marginLeft: (screenWidth - containerWidth) / 2 + 20 }
                : {}
            }
            className="flex gap-4 bg-[#F1F3F5] rounded-full  w-fit p-1 max-lg:justify-start mx-auto "
          >
            {mockTermsAndCondition.map((item, index) => (
              <Button
                onClick={() => setActiveTab(item.value)}
                variant={"ghost"}
                key={index}
                value={item.value}
                className={cn(
                  "shrink-0 py-2.5 px-6 rounded-full text-icon-placeholder border-0 cursor-pointer hover:!text-icon-emphasize hover:!bg-transparent overflow-visible",
                  activeTab === item.value &&
                    "!bg-white text-icon-emphasize shadow-lg ",
                )}
              >
                {item.name}
              </Button>
            ))}
          </div>
        </div>
      </div>
      <div
        ref={containerRef}
        className="flex flex-col gap-4 container !py-0 max-w-[850px] mt-10"
      >
        <div className="w-full flex justify-end">
          <div className="w-fit" onClick={() => setOpen(true)}>
            <Typography
              variant="body-regular"
              className="text-brand cursor-pointer"
            >
              Các phiên bản đã lưu trữ
            </Typography>
          </div>
        </div>
        {matchingContent && (
          <div className="flex flex-col gap-8">
            <Typography
              variant="special-title"
              className="max-md:text-2xl max-md:font-semibold"
            >
              {matchingContent.name}
            </Typography>
            <div className="flex flex-col gap-6">
              <Typography variant="body-regular" className="text-default">
                {matchingContent.subtitle}
              </Typography>
              <ol className="list-decimal space-y-6 flex flex-col pl-4 marker:font-bold">
                {matchingContent.content.map((item: any, itemIndex: any) => (
                  <li key={itemIndex}>
                    <div className="inline ml-2.5">
                      <div className="flex-col inline-flex">
                        <Typography
                          variant="body-regular"
                          className="font-bold"
                        >
                          {item.title}
                        </Typography>
                        <Typography variant="body-regular">
                          {item.description}
                        </Typography>
                      </div>
                    </div>
                  </li>
                ))}
              </ol>
            </div>
          </div>
        )}
      </div>
      <ArchiveDialog open={open} setOpen={setOpen} />
    </div>
  );
}

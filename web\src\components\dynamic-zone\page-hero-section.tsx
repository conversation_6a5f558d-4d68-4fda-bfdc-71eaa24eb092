"use client";

import Image from "next/image";
import { AspectRatio, Button, Typography } from "@components/ui";
import { OnBoardingDrawer } from "../layout/templates/onboarding/onboarding-drawer";

//----------------------------------------------------------------------------------
type TProps = {
  data: any;
};

//----------------------------------------------------------------------------------
export default function PageHeroSection({ data }: TProps) {
  const {
    section_styles,
    heading,
    sub_heading,
    buttons,
    image,
    statistics,
    background_pattern,
    image_style,
  } = data || {};

  return (
    <section
      className="relative"
      style={{
        background: background_pattern
          ? `url(${background_pattern})`
          : undefined,
        backgroundRepeat: "no-repeat",
        backgroundPosition: "50% 75%",
        backgroundSize: "100%",
        backgroundColor: section_styles?.background?.color || undefined,
      }}
    >
      <div className="container relative">
        <div className="relative w-full max-w-2xl mx-auto">
          <div className="flex flex-col items-center">
            {/* Top Content */}
            <div className="flex flex-col items-center gap-6">
              <div className="text-center flex flex-col items-center gap-3">
                {heading && (
                  <Typography
                    variant="special-title"
                    className="whitespace-pre-line"
                  >
                    {heading}
                  </Typography>
                )}
                {sub_heading ? (
                  <p className="text-black mx-auto">{sub_heading}</p>
                ) : (
                  <p className="text-black mx-auto">
                    Chiến lược đầu tư thông minh hiệu quả, giúp bạn tăng trưởng{" "}
                    <strong>5.00% - 15.00% / Năm</strong>
                  </p>
                )}
              </div>
              {buttons?.length > 0 && (
                <div className="flex gap-4">
                  {buttons.map((button: any, index: number) => (
                    <OnBoardingDrawer key={index}>
                      <Button
                        variant={
                          button.variant ||
                          (index === 0 ? "default" : "outline")
                        }
                      >
                        {button.text}
                      </Button>
                    </OnBoardingDrawer>
                  ))}
                </div>
              )}
            </div>

            {/* Image */}
            {image && (
              <AspectRatio
                ratio={16 / 9}
                className="relative flex max-h-[426px] h-full"
                style={{
                  marginTop: image_style ? image_style.margin.top : 0,
                }}
              >
                <Image
                  src={image.src}
                  alt={image.alt || "Hero illustration"}
                  fill
                  className="object-contain"
                />
              </AspectRatio>
            )}

            {/* Statistics */}
            {statistics?.length > 0 && (
              <div className="flex items-start justify-between max-w-[623px] md:px-[30px] w-full">
                {statistics.map((stat: any, index: number) => (
                  <div
                    key={index}
                    className="text-center flex flex-col items-center justify-center w-[153px]"
                  >
                    <div className="w-full h-[53px] flex items-center justify-center">
                      <Typography
                        variant="title-2"
                        className="whitespace-nowrap shrink-0 text-black"
                      >
                        {stat.value}
                      </Typography>
                    </div>
                    <Typography className="text-black">{stat.label}</Typography>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}

/**
 * Verification of GBI Formula according to user's specification
 * 
 * 1. Số tiền đầu tư ban đầu: z * g
 * 2. <PERSON><PERSON><PERSON> nhuận tích lũy tháng: m = ((1 + r/12)^t - 1) / (r/12)
 * 3. <PERSON><PERSON><PERSON> tiêu: g = x(1 + r/12)^t + a * m
 * 4. Số tiền nộp hàng tháng: a = (g - x(1 + r/12)^t) / m
 */

export function verifyGBIFormula() {
  console.log('🧮 === GBI Formula Verification ===');
  
  // Input values from Figma
  const g = 1_000_000_000; // Target amount (1 billion)
  const r = 6; // Annual return rate (6%)
  const t = 12; // Time period (12 months)
  
  // Step 1: Calculate z ratio
  const z = g >= 1_000_000_000 ? 0.5 : g >= 500_000_000 ? 0.4 : 0.3;
  console.log(`1. Z ratio for ${g.toLocaleString()}: ${z * 100}%`);
  
  // Step 2: Calculate initial investment
  const x = z * g;
  console.log(`2. Initial Investment (x): ${x.toLocaleString()}`);
  
  // Step 3: Calculate monthly multiplier
  const monthlyRate = r / 100 / 12; // Convert to decimal monthly rate
  const m = ((1 + monthlyRate) ** t - 1) / monthlyRate;
  console.log(`3. Monthly Rate: ${(monthlyRate * 100).toFixed(4)}%`);
  console.log(`4. Monthly Multiplier (m): ${m.toFixed(6)}`);
  
  // Step 4: Calculate monthly deposit
  const futureValueOfX = x * (1 + monthlyRate) ** t;
  const a = (g - futureValueOfX) / m;
  console.log(`5. Future Value of X: ${futureValueOfX.toLocaleString()}`);
  console.log(`6. Monthly Deposit (a): ${a.toLocaleString()}`);
  
  // Step 5: Verify the formula g = x(1 + r/12)^t + a * m
  const calculatedG = futureValueOfX + a * m;
  console.log(`7. Calculated G: ${calculatedG.toLocaleString()}`);
  console.log(`8. Target G: ${g.toLocaleString()}`);
  console.log(`9. Difference: ${Math.abs(calculatedG - g).toLocaleString()}`);
  
  // Comparison with Figma
  const figmaA = 38_033_215;
  const figmaX = 500_000_000;
  
  console.log('\n📊 === Figma Comparison ===');
  console.log(`Figma X: ${figmaX.toLocaleString()}, Calculated X: ${x.toLocaleString()}`);
  console.log(`Figma A: ${figmaA.toLocaleString()}, Calculated A: ${Math.round(a).toLocaleString()}`);
  console.log(`X Difference: ${Math.abs(x - figmaX).toLocaleString()}`);
  console.log(`A Difference: ${Math.abs(a - figmaA).toLocaleString()}`);
  console.log(`A Error %: ${((Math.abs(a - figmaA) / figmaA) * 100).toFixed(3)}%`);
  
  // Final verification
  const isAccurate = Math.abs(calculatedG - g) < 1 && Math.abs(a - figmaA) / figmaA < 0.01;
  console.log(`\n✅ Formula Accuracy: ${isAccurate ? 'CORRECT' : 'NEEDS REVIEW'}`);
  
  return {
    x,
    a,
    m,
    calculatedG,
    targetG: g,
    figmaMatch: Math.abs(a - figmaA) / figmaA < 0.01,
    formulaCorrect: Math.abs(calculatedG - g) < 1
  };
}

// Auto-run in browser console
if (typeof window !== 'undefined') {
  // Make it available globally for browser testing
  (window as any).verifyGBIFormula = verifyGBIFormula;
  console.log('🔧 Run verifyGBIFormula() in console to test');
}

////////////////////////////////
// App config
////////////////////////////////
export const APP_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || "https://localhost:3000",
  apiUrl: process.env.NEXT_PUBLIC_API_URL || "http://localhost:1337",
  sessionKey: process.env.NEXT_PUBLIC_SESSION_KEY || "kafi_session",
  rootDomain: process.env.NEXT_PUBLIC_ROOT_DOMAIN || "domain.com",
  apiKey: process.env.NEXT_PUBLIC_API_TOKEN || "",
};

export const KEYS = {
  keyAccessToken: "_kafi_ac_token",
  keyRefreshToken: "_kafi_rf_token",
  keyCacheId: "_kafi_cache_id",
  MODEL_VIEWED: "_kafi_recently_viewed_products",
  MODEL_VIEWED_LIMIT: "recently_viewed_products_limit",
  keyViewed: "_kafi_viewed",
};

const locales = process.env.NEXT_PUBLIC_LANGUAGES?.split(",") || ["vi", "en"];

export const LINGUI_CONFIG = {
  locales: locales || ["vi", "en"],
  defaultLocale: process.env.NEXT_PUBLIC_DEFAULT_LOCALE || "vi",
};

////////////////////////////////
// Analytics config
////////////////////////////////
export const ANALYTIC_API = {
  url: process.env.NEXT_PUBLIC_ANALYTIC_API_URL || "",
  apiKey: process.env.NEXT_PUBLIC_ANALYTIC_API_KEY || "",
};

////////////////////////////////
// Auth config
////////////////////////////////
export const AUTH_API = {
  google: {
    clientId: process.env.NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_ID || "",
    clientSecret: process.env.NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_SECRET || "",
  },
  facebook: {
    clientId: process.env.NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_ID || "",
    clientSecret: process.env.NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_SECRET || "",
  },
};

import { cn } from "#/src/lib/utils";

interface ICompassBoxProps {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  strokeWidth?: string | number;
  strokeLinecap?: string;
  strokeLinejoin?: string;
  className?: string;
  path: string;
}

export const CustomIcon: React.FC<ICompassBoxProps> = ({
  color = "text-icon-placeholder",
  strokeWidth = "3",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
  path,
}) => {
  return (
    <svg
      width="48"
      height="49"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "cursor-pointer transition-all duration-300",
        color,
        `hover:${hoverColor}`,
        `focus:${activeColor}`,
        className,
      )}
    >
      <path
        d={path}
        stroke={"currentColor"}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

{"collectionName": "components_dynamic_zone_featured_news_events", "info": {"displayName": "featured_news_events", "icon": "calendar"}, "options": {}, "attributes": {"section_styles": {"type": "component", "component": "elementals.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.section-heading", "repeatable": false}, "news": {"type": "relation", "relation": "oneToMany", "target": "api::article.article"}, "award": {"type": "relation", "relation": "oneToOne", "target": "api::award.award"}}, "config": {}}
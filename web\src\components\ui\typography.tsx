import { cn } from "#/src/lib";
import { cva } from "class-variance-authority";
import React from "react";

export type TTypographyElement =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "p"
  | "span"
  | "div"
  | "label";

type TVariant =
  | "special-title"
  | "large-title"
  | "title-0"
  | "title-1"
  | "title-2"
  | "title-3"
  | "headline"
  | "body-regular"
  | "body-bold"
  | "body-medium"
  | "body"
  | "subheadline-medium"
  | "subheadline-regular"
  | "medium-body-semi-bold"
  | "medium-body-regular"
  | "small-body-bold"
  | "small-body-regular"
  | "button-label"
  | "input-medium"
  | "caption-semi-bold"
  | "caption-regular"
  | "caption"
  | "special-label"
  | "mini-label"
  | "blockquote"
  | "overline"
  | "lead"
  | "large"
  | "small"
  | "muted"
  | "button"
  | "code";

export const typographyVariants = cva("", {
  variants: {
    variant: {
      "special-title":
        "scroll-m-20 text-6xl font-bold leading-[56px] tracking-[-1%]",
      "large-title":
        "scroll-m-20 text-5xl font-bold leading-[48px] tracking-[-1%]",
      "title-0": "scroll-m-20 text-4xl font-bold leading-[36px]",
      "title-1": "scroll-m-20 text-3xl font-bold leading-[32px]",
      "title-2":
        "scroll-m-20 text-2xl font-semibold leading-[32px] tracking-[-1%]",
      "title-3": "scroll-m-20 text-xl font-semibold leading-[28px]",
      headline: "text-base font-semibold",
      "body-regular": "text-base leading-[24px] font-normal",
      "body-bold": "text-base leading-[24px] font-semibold",
      "body-medium": "text-base leading-[24px] font-medium",
      body: "text-base leading-[24px] font-normal",
      "subheadline-medium": "text-sm leading-[20px] font-medium",
      "subheadline-regular": "text-sm leading-[20px] font-normal",
      "medium-body-semi-bold": "text-sm leading-[20px] font-semibold",
      "medium-body-regular": "text-sm leading-[20px] font-normal",
      "small-body-bold": "text-[12px] leading-[18px] font-semibold",
      "small-body-regular": "text-[12px] leading-[18px] font-normal",
      "button-label": "text-base leading-[24px] font-semibold",
      "input-medium": "text-sm leading-base font-medium",
      "caption-semi-bold": "text-xs leading-base font-semibold",
      "caption-regular": "text-xs leading-[18px] font-normal",
      caption: "text-xs leading-base font-normal",
      "special-label": "text-2xs leading-base font-medium",
      "mini-label": "text-3xs leading-[12px] font-semibold",
      blockquote: "mt-6 border-l-2 border-border pl-6 italic text-sm",
      overline: "text-xs uppercase tracking-widest font-medium",
      lead: "text-xl",
      large: "text-lg",
      small: "text-sm",
      muted: "text-sm",
      button: "text-base",
      code: "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm",
    },
  },
  defaultVariants: {
    variant: "body",
  },
});

export interface TypographyProps {
  variant?: TVariant;
  component?: TTypographyElement;
  children: React.ReactNode;
  classname?: string;
  textAlign?: "left" | "center" | "right" | "justify";
  [key: string]: any;
}

const defaultElements: Record<string, TTypographyElement> = {
  "special-title": "h1",
  "large-title": "h2",
  "title-0": "h3",
  "title-1": "h4",
  "title-2": "h5",
  "title-3": "h6",
  headline: "h6",
  "body-bold": "p",
  "body-medium": "p",
  "body-regular": "p",
  "subheadline-medium": "h6",
  "subheadline-regular": "h6",
  "medium-body-semi-bold": "p",
  "medium-body-regular": "p",
  "small-body-bold": "p",
  "small-body-regular": "p",
  "button-label": "span",
  "input-medium": "span",
  "caption-semi-bold": "span",
  "caption-regular": "span",
  caption: "span",
  "special-label": "label",
  "mini-label": "span",
  blockquote: "p",
  overline: "span",
  lead: "p",
  large: "p",
  small: "p",
  muted: "p",
  button: "span",
};

export const Typography = ({
  variant = "body",
  component,
  children,
  classname,
  ...props
}: TypographyProps) => {
  const Component = component || defaultElements[variant] || "p";

  const { className, ...rest } = props;

  return (
    <Component
      {...rest}
      className={cn(typographyVariants({ variant }), classname, className)}
    >
      {children}
    </Component>
  );
};

export default Typography;

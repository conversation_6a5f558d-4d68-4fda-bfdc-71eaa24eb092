{"kind": "collectionType", "collectionName": "articles", "info": {"singularName": "article", "pluralName": "articles", "displayName": "Article - List"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::article-category.article-category", "inversedBy": "articles"}, "views": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}, "min": 0}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "content": {"type": "customField", "pluginOptions": {"i18n": {"localized": true}}, "customField": "plugin::ckeditor5.CKEditor", "options": {"preset": "defaultHtml"}}}}
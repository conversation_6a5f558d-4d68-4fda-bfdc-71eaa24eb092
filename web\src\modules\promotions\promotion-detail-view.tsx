"use client";

import { PROMOTIONS_DATA } from "#/src/__mock__/promotions";
import GoBackButton from "#/src/components/custom-button/go-back-button";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import OtherPromotions from "./components/other-promotions";
import DetailContent from "./components/detail-content";
//-----------------------------------------------------------
export default function PromotionDetailView() {
  const { slug } = useParams();
  const [promotion, setPromotion] = useState<any>(null);

  useEffect(() => {
    const promotion = PROMOTIONS_DATA.find((item: any) => item.slug === slug);
    setPromotion(promotion);
  }, [slug]);

  return (
    <div className="container flex flex-col gap-4">
      <GoBackButton />
      <div className="flex flex-col gap-20">
        <DetailContent current_promotion={promotion} />
        <OtherPromotions current_slug={slug as string} />
      </div>
    </div>
  );
}

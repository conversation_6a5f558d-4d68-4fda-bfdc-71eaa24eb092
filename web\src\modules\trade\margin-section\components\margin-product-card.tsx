"use client";

import { Typography } from "@/components/ui";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/use-media-query";
import { motion } from "framer-motion";
import { useState } from "react";

export type MarginProduct = {
  value: string;
  category: string;
  name: string;
  description: string;
  interest_rate: string;
  condition: string;
  method: string;
  newly_added: boolean;
};

const buttonVariants = {
  initial: { y: 0 },
  hoverUp: { y: -70, x: 80 },
  hoverDown: { y: 4 },
};

const MarginProductCard = ({
  category,
  name,
  description,
  interest_rate,
  condition,
  method,
  newly_added,
}: MarginProduct) => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const isTablet = useMediaQuery("(max-width: 1023px)");
  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "w-full h-full bg-white relative px-4 md:px-8 py-20 group rounded-4xl hover:bg-gradient-to-r hover:from-[var(--color-gradient-start)] hover:to-[var(--color-gradient-end)] transition-colors duration-300",
      )}
    >
      {isTablet ? (
        <div className="flex flex-col gap-8">
          <div className="flex flex-col gap-4 lg:gap-2">
            <Typography
              variant="title-3"
              className="text-emphasize lg:text-white"
            >
              {category}
            </Typography>
            <div className="flex w-full justify-between items-center">
              <Typography
                variant="special-title"
                component="h3"
                className="font-medium text-brand lg:text-white text-[56px]"
              >
                {name}
              </Typography>
              {newly_added && (
                <motion.button className="rounded-[20px] bg-brand group-hover:bg-white group-hover:text-brand border-none py-2 px-5 w-fit h-fit text-white text-base font-semibold">
                  Mới
                </motion.button>
              )}
            </div>
            <Typography
              variant="body-regular"
              classname="text-emphasize lg:hidden"
            >
              {description}
            </Typography>
          </div>

          <div className="flex flex-col gap-4 lg:gap-8">
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Lãi suất
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {interest_rate}
              </Typography>
            </div>
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Điều kiện
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {condition}
              </Typography>
            </div>
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Hình thức đăng ký
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {method}
              </Typography>
            </div>
          </div>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          className="flex flex-col gap-8"
        >
          <motion.div className="flex flex-col gap-4 lg:gap-2">
            <Typography
              variant="title-3"
              className="text-emphasize lg:text-white"
            >
              {category}
            </Typography>
            <div className="flex w-full justify-between items-center">
              <Typography
                variant="special-title"
                component="h3"
                className="font-medium text-brand lg:text-white text-[56px]"
              >
                {name}
              </Typography>
              {newly_added && (
                <motion.button className="rounded-[20px] bg-brand group-hover:bg-white group-hover:text-brand border-none py-2 px-5 w-fit h-fit text-white text-base font-semibold">
                  Mới
                </motion.button>
              )}
            </div>
            <Typography
              variant="body-regular"
              classname="text-emphasize lg:hidden"
            >
              {description}
            </Typography>
          </motion.div>

          <div className="flex flex-col gap-4 lg:gap-8">
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Lãi suất
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {interest_rate}
              </Typography>
            </div>
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Điều kiện
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {condition}
              </Typography>
            </div>
            <div className="flex flex-col gap-1">
              <Typography
                variant="body-regular"
                className=" text-emphasize lg:text-white"
              >
                Hình thức đăng ký
              </Typography>
              <Typography
                variant="body-bold"
                className=" text-emphasize lg:text-white"
              >
                {method}
              </Typography>
            </div>
          </div>
        </motion.div>
      )}
      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: isHovered ? 0 : 1 }}
        className="w-full h-full absolute inset-0 hidden lg:flex flex-col gap-8 justify-center px-8"
      >
        <div className="flex flex-col gap-2">
          <motion.div
            animate={{ y: isHovered ? -60 : 0 }}
            transition={{ type: "spring", stiffness: 200, damping: 20 }}
          >
            <Typography variant="title-3" className="text-xl text-emphasize">
              {category}
            </Typography>
          </motion.div>
          <div className="flex w-full justify-start gap-2 items-center">
            <motion.div
              animate={{ y: isHovered ? -60 : 0 }}
              transition={{ type: "spring", stiffness: 200, damping: 20 }}
            >
              <Typography
                variant="special-title"
                component="h3"
                className="font-medium text-brand text-[56px]"
              >
                {name}
              </Typography>
            </motion.div>
            {newly_added && (
              <motion.button
                animate={isHovered ? "hoverUp" : "initial"}
                variants={buttonVariants}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="rounded-[20px] bg-brand group-hover:bg-white group-hover:text-brand border-none py-2 px-5 w-fit h-fit text-white text-base font-semibold"
              >
                Mới
              </motion.button>
            )}
          </div>
        </div>
        <Typography variant="body-regular" classname="text-emphasize">
          {description}
        </Typography>
        <Typography variant="button-label" classname="text-emphasize">
          Tìm hiểu thêm
        </Typography>
      </motion.div>
    </div>
  );
};

export default MarginProductCard;

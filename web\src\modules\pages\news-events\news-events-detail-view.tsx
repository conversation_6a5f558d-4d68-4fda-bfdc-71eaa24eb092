"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Separator,
  Typo<PERSON>,
} from "#/src/components/ui";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import NewsEventsCard from "../../news/news-events-card";
import { _image, _newsEvents } from "#/src/__mock__";
import { FacebookIcon, LinkedinIcon, ShareLinkIcon } from "#/src/components";

export default function NewsEventsDetailView() {
  const renderDetail = (
    <div className="container-inner-sm flex flex-col gap-8 mx-auto">
      <Typography variant="special-title">
        Kafi và VIB ký kết biên bản ghi nhớ hợp tác chiến lược
      </Typography>

      <div className="flex gap-4 justify-between">
        <div className="flex gap-4 items-center">
          <Typography variant="subheadline-medium">20/06/2024</Typography>
          <Separator orientation="vertical" className="text-placeholder" />
          <Typography variant="subheadline-medium">
            220 <span className="text-placeholder">Đã xem</span>
          </Typography>
        </div>

        <div className="flex gap-4">
          <Link href="#!">
            <FacebookIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
          <Link href="#!">
            <LinkedinIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
          <Link href="#!">
            <ShareLinkIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
        </div>
      </div>

      <Separator />

      <div className="dynamic-content">
        <div className="flex flex-col gap-10">
          {/* Thumbnail */}
          <AspectRatio ratio={2 / 1} className="rounded-xl overflow-hidden">
            <Image
              src={_image}
              alt="Kafi và VIB ký kết biên bản ghi nhớ hợp tác chiến lược"
              fill
              className="object-cover"
            />
          </AspectRatio>
          {/* Description Content */}
          <Typography variant="body-bold">
            Ngày 02/04/2025, lễ ký kết Biên bản Ghi nhớ Hợp tác (MoU) giữa Công
            ty Cổ phần Chứng khoán KAFI (Kafi) và Ngân hàng TMCP Quốc tế VIB
            (VIB) đã diễn ra, đánh dấu bước tiến trong việc xây dựng hệ sinh
            thái tài chính số toàn diện, mang đến giải pháp tài chính hiện đại,
            an toàn và hiệu quả cho khách hàng cá nhân và tổ chức. Đại diện tham
            gia lễ ký kết, về phía Kafi có ông Trịnh Thanh Cần – Tổng Giám đốc
            Công ty Cổ phần Chứng khoán KAFI; về phía đối tác có ông Trần Nhất
            Minh – Phó Tổng Giám đốc Ngân hàng TMCP Quốc tế VIB.
          </Typography>
          {/* Main Content */}
          <div>
            <Typography variant="title-3">
              Hợp tác chiến lược giữa hai định chế tài chính uy tín
            </Typography>
            <Typography variant="body-medium" className="mt-6">
              Theo nội dung Biên bản ghi nhớ, Kafi và VIB sẽ hợp tác sâu rộng để
              phát triển và tích hợp các sản phẩm, dịch vụ tài chính số trên nền
              tảng công nghệ hiện đại của cả hai bên. Các giải pháp đầu tư và
              thanh toán tiện ích nhằm gia tăng trải nghiệm người dùng, đồng
              thời mở rộng quy mô tiếp cận khách hàng mục tiêu, tạo động lực cho
              sự phát triển bền vững của thị trường tài chính Việt Nam.
            </Typography>

            <Typography variant="body-medium" className="mt-4">
              Trong phát biểu tại buổi lễ, ông Trịnh Thanh Cần – Tổng Giám đốc
              Kafi nhấn mạnh: “Việc ký kết MOU với VIB không chỉ đánh dấu bước
              tiến quan trọng trong chiến lược phát triển của Kafi, mà còn thể
              hiện cam kết của chúng tôi trong việc mang đến các giải pháp tài
              chính số tích hợp và đổi mới sáng tạo cho thị trường. Sự hợp tác
              này sẽ không chỉ tạo ra cơ hội phát triển bền vững cho cả hai bên
              mà còn góp phần nâng cao giá trị cung cấp cho khách hàng, đồng
              thời mở rộng tầm ảnh hưởng của chúng tôi trong ngành tài chính.”
            </Typography>

            <Typography variant="body-medium" className="mt-4">
              Chia sẻ về quan hệ hợp tác với Kafi, ông Trần Nhất Minh – Phó Tổng
              Giám đốc VIB cho biết: “Lễ ký kết hôm nay đánh dấu lần đầu tiên
              tích hợp các giải pháp đầu tư thông minh trực tiếp vào Ngân hàng
              số MyVIB. Dựa trên sự kết hợp giữa nền tảng công nghệ vững chắc
              của VIB và chuyên môn sâu rộng của Chứng khoán Kafi, chúng tôi sẽ
              mang lại cho người dùng những công cụ mạnh mẽ nhất để tối ưu hóa
              tài sản và tận dụng tối đa cơ hội đầu tư. Sự kết hợp này không chỉ
              mở ra cơ hội đầu tư và giao dịch nhanh chóng, tiện lợi, mà còn
              đánh dấu bước tiến quan trọng trong việc xây dựng hệ sinh thái số
              toàn diện, mang đến trải nghiệm tài chính tối ưu, giá trị vượt
              trội cho khách hàng và thúc đẩy sự phát triển tài chính bền vững.”
            </Typography>
          </div>

          <div>
            <Typography variant="title-3">
              Hướng tới một hệ sinh thái tài chính số bền vững và minh bạch
            </Typography>
            <Typography variant="body-medium" className="mt-6">
              Kafi và VIB đã chính thức ký kết MOU, đánh dấu sự hợp tác chiến
              lược giữa hai bên với nguyên tắc tự nguyện, bình đẳng và tuân thủ
              nghiêm ngặt các quy định pháp luật hiện hành. Hai bên cam kết phối
              hợp chặt chẽ để xây dựng và triển khai các kế hoạch hợp tác mang
              tính dài hạn và bền vững, nhằm tối ưu hóa các cơ hội phát triển
              trong ngành tài chính. Phạm vi hợp tác sẽ bao gồm nghiên cứu và
              phát triển các sản phẩm dịch vụ tài chính số tích hợp trên nền
              tảng công nghệ tiên tiến của cả Kafi và VIB. Cùng với đó, hai bên
              sẽ tổ chức các chương trình nhằm mở rộng mạng lưới khách hàng sử
              dụng dịch vụ đồng thương hiệu, đồng thời gia tăng sự hiện diện của
              các bên trong thị trường tài chính bán lẻ. Sự hợp tác cũng sẽ mở
              rộng qua việc chia sẻ thông tin, phối hợp tổ chức các hoạt động
              marketing chiến lược, chăm sóc khách hàng và phát triển thương
              hiệu, qua đó nâng cao giá trị cung cấp cho khách hàng và mở rộng
              cơ hội phát triển thị trường tài chính.
            </Typography>
          </div>

          <div>
            <Typography variant="title-3">
              Chiến lược Tăng trưởng Năng động của Kafi giai đoạn 2025-2027
            </Typography>
            <Typography variant="body-medium" className="mt-6">
              Trong giai đoạn 2025-2027, Kafi sẽ triển khai chiến lược
              &apos;Tăng trưởng năng động&apos; nhằm thu hút mạnh mẽ nhà đầu tư,
              phát triển sản phẩm sáng tạo và cạnh tranh, tiên phong công nghệ
              và số hóa, đồng thời đẩy mạnh truyền thông và nâng cao quản trị
              rủi ro tuân thủ.
            </Typography>

            <AspectRatio
              ratio={2 / 1}
              className="mt-6 rounded-xl overflow-hidden"
            >
              <Image
                src="/images/news-events/tang-von-dieu-le-va-niem-yet-co-phieu-tren-upcom-la-hai-muc-tieu-lon-cua-kafi-trong-nam-2025.png"
                alt="tang-von-dieu-le-va-niem-yet-co-phieu-tren-upcom-la-hai-muc-tieu-lon-cua-kafi-trong-nam-2025"
                fill
                className="object-cover"
              />
            </AspectRatio>
            <Typography
              variant="small-body-regular"
              className="mt-2 w-full text-center"
            >
              Tăng vốn điều lệ và niêm yết cổ phiếu trên UpCom là hai mục tiêu
              lớn của Kafi trong năm 2025.
            </Typography>

            <Typography variant="body-medium" className="mt-6">
              Mục tiêu của công ty là đạt tổng tài sản 25.000 tỷ đồng, lợi nhuận
              trước thuế 800 tỷ đồng, với mảng môi giới chứng khoán đặt mục tiêu
              vào Top 10 thị phần giao dịch và Top 7 thị phần cho vay ký quỹ.
              Đặc biệt, Kafi sẽ niêm yết 500 triệu cổ phiếu trên hệ thống giao
              dịch UPCoM vào Quý II năm 2025 và tăng vốn điều lệ lên 7.500 tỷ
              đồng thông qua chào bán cổ phần cho cổ đông hiện hữu, nhằm tăng
              cường năng lực tài chính và mở rộng thị phần. Một trong những sản
              phẩm trọng tâm năm 2025 là chứng quyền có bảo đảm (CW), giúp mở
              rộng nguồn doanh thu và tạo bước đệm phát triển thị trường chứng
              khoán phái sinh trong tương lai.
            </Typography>
          </div>

          {/* Attached Link */}
          <div className="flex flex-col gap-2">
            <Typography variant="title-3">Link bài viết:</Typography>
            <div className="flex gap-2">
              <Link
                href="https://vnexpress.net/kafi-hop-tac-vib-xay-dung-he-sinh-thai-tai-chinh-so-4870072.html"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:no-underline"
              >
                <Button
                  variant="link"
                  className="!text-brand p-0 hover:no-underline"
                >
                  VNExpress
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderRelated = (
    <div className="flex flex-col gap-6">
      <Typography variant="large-title">Tin khác</Typography>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {_newsEvents.slice(1, 4).map((item) => (
          <NewsEventsCard key={item.id} item={item} hideDescription />
        ))}
      </div>
    </div>
  );

  return (
    <div className="container flex flex-col gap-8 py-10">
      <Link href="/news-events">
        <Button variant="text" className="!px-0">
          <ArrowLeft />
          Quay lại
        </Button>
      </Link>

      <div className="flex flex-col gap-20">
        {renderDetail}
        {renderRelated}
      </div>
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

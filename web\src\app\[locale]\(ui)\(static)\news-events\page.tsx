import { NewsEventsView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "News & Events",
  description: "News & Events",
};

const NewsEventsPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <NewsEventsView />
    </Suspense>
  );
};

export default NewsEventsPage;

export const dynamic = "force-dynamic";

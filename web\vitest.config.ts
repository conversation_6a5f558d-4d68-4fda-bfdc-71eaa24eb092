import { defineConfig, mergeConfig } from "vitest/config";

const baseConfig = defineConfig({
  test: {
    coverage: {
      provider: "istanbul",
      reporter: [
        [
          "json",
          {
            file: `../coverage.json`,
          },
        ],
      ],
      enabled: true,
    },
  },
});

export default mergeConfig(
  baseConfig,
  defineConfig({
    test: {
      globals: true,
      reporters: ["html"],
      coverage: {
        provider: "istanbul",
        reporter: ["html"],
      },
    },
  }),
);

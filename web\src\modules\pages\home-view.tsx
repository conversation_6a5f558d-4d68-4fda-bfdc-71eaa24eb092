import {
  HOME_HERO_SECTION,
  HOME_TOGGLE_CARDS,
  HOME_ICON_BOXES,
} from "#/src/__mock__";
import PageHeroSection from "@components/dynamic-zone/page-hero-section";
import ToggleCardsSection from "@components/dynamic-zone/toggle-cards-section";
import IconBoxesSection from "@components/dynamic-zone/icon-boxes-section";

import {
  BannerPromotionSection,
  GoalBasedInvestingSection,
  PartnersSection,
  CommunitySection,
} from "@/modules/home";

import { NewsEventView } from "@/modules/news";
import { SupportInfo } from "@/modules/shared";

export default function HomeView() {
  return (
    <div className="flex flex-col gap-y-20">
      <PageHeroSection data={HOME_HERO_SECTION} />

      <BannerPromotionSection />

      <ToggleCardsSection data={HOME_TOGGLE_CARDS} />

      <GoalBasedInvestingSection />

      <PartnersSection />

      <CommunitySection />

      <IconBoxesSection data={HOME_ICON_BOXES} />

      <NewsEventView />

      <SupportInfo />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * <BannerPromotionSection data={_mock__}  />
 *
 * <ToggleCardsSection data={_mock__}
 *
 * <GoalBasedInvestingSection data={_mock__}  />
 *
 * <PartnersSection data={_mock__} />
 *
 * <CommunitySection data={_mock__} />
 *
 * <IconBoxesSection data={_mock__} />
 *
 * <NewsEventView data={_mock__} />
 *
 * <SupportInfo data={_mock__} />
 * **/

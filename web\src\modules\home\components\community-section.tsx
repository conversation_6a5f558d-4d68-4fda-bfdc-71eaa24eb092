"use client";
import { Button, Typography } from "@components/ui";
import { useMediaQuery } from "@/hooks";
import { motion } from "framer-motion";
import { Ellipsis } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

const mockFAQ = [
  {
    avatar: "/images/home/<USER>",
    question: "Làm thế nào để bắt đầu đầu tư chứng khoán?",
    color: "#00C694",
  },
  {
    avatar: "/images/home/<USER>",
    question: "Nên mua cổ phiếu nào? C<PERSON> phiếu tốt là cổ phiếu như thế nào?",
    color: "#FFAB00",
  },
  {
    avatar: "/images/home/<USER>",
    question: "Làm thế nào để kiểm soát cảm xúc khi thị trường biến động?",
    color: "#F14E42",
  },
];

const renderMessageBoxWidth = (index: number, inTablet: boolean = false) => {
  switch (index) {
    case 0:
      return 172;
    case 1:
      return inTablet ? 198 : 231;
    case 2:
      return inTablet ? 174 : 217;
    default:
      return 172;
  }
};

// const dotVariants = (i: number) => ({
//   opacity: [0, 1, 0],
//   transition: {
//     delay: i * 0.2,
//     repeat: Infinity,
//     repeatType: "loop" as const,
//     duration: 1.2,
//   },
// });

export default function CommunitySection() {
  const isTablet = useMediaQuery("(max-width: 1023px)");
  const [isHovered, setIsHovered] = useState(false);
  const [isHoveredDiscuss, setIsHoveredDiscuss] = useState(false);

  return (
    <div className="container-inner flex flex-col gap-8 lg:!max-w-[1064px] pt-8 mx-auto">
      <Typography
        variant="large-title"
        classname="text-center max-md:text-2xl max-lg:text-3xl"
      >
        Cùng phát triển Cộng đồng Kafi
      </Typography>
      <div className="w-fit flex flex-col md:flex-row gap-6 mx-auto">
        <Button>Tham gia cộng đồng</Button>
        <Button variant="outline" className="w-fit mx-auto md:mx-0">
          Tìm hiểu thêm
        </Button>
      </div>

      {isTablet ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 auto-rows-fr">
          <div
            className="rounded-[20px]  flex flex-col gap-11"
            style={{
              background:
                "linear-gradient(180deg, #F0FAF9 1.56%, #E3ECEC 100%)",
            }}
          >
            <Image
              src={"/images/home/<USER>"}
              alt="community"
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-auto object-contain"
            />
            <div className="px-6 text-center flex flex-col gap-4">
              <Typography variant="title-2" className="font-medium text-black">
                Tham gia xây dựng cộng đồng
              </Typography>
              <Typography>
                Tham gia cộng đồng Kafi dễ dàng nhanh chóng để nắm bắt thông tin
                thị trường, theo dõi những chia sẻ về thị trường từ những nhà
                đầu tư kinh nghiệm
              </Typography>
            </div>
          </div>
          <div className="rounded-[20px] flex flex-col justify-between items-center pt-20 pb-8 px-10 bg-[#F1F1F1]">
            <Image
              src={"/images/home/<USER>"}
              alt="message"
              sizes="100vw"
              width={0}
              height={0}
              className="w-[89px] h-auto aspect-square object-contain transition-transform duration-300 group-hover:scale-110"
            />
            <div className="flex flex-col gap-2.5 text-center">
              <Typography variant="title-2" className="font-medium text-black">
                Lời khuyên từ các chuyên gia
              </Typography>
              <Typography>
                Cộng đồng giúp nhà đầu tư giữ vững tâm lý, tránh những quyết
                định vội vàng theo cảm tính
              </Typography>
            </div>
          </div>
          <div
            className="rounded-[20px] p-8 flex flex-col gap-10"
            style={{
              background: "linear-gradient(180deg, #F6F6F6 0%, #EEE 100%)",
            }}
          >
            <Typography variant="title-2" className="font-medium text-black">
              Hỏi và giải đáp
            </Typography>
            <div className="flex flex-col gap-3">
              {mockFAQ.map((item, index) => (
                <div key={index} className="flex gap-2.5 items-start">
                  <Image
                    src={item.avatar}
                    alt={`item ${index + 1}`}
                    sizes="100vw"
                    className="w-7 h-auto aspect-square object-contain"
                    width={0}
                    height={0}
                  />
                  <div
                    className="px-4 py-3 rounded-[12px]"
                    style={{
                      backgroundColor: item.color,
                      maxWidth: `${renderMessageBoxWidth(index, isTablet)}px`,
                    }}
                  >
                    <Typography className="text-white leading-[18px] font-medium text-xs">
                      {item.question}
                    </Typography>
                  </div>
                </div>
              ))}
            </div>
            <div className="w-full flex justify-end">
              <div className="w-fit h-fit px-3 py-1 bg-subtle rounded-full">
                <Ellipsis className="w-8 text-black" />
              </div>
            </div>
          </div>
          <div className="rounded-[20px] bg-white flex flex-col pt-12 gap-2">
            <Image
              src={"/images/home/<USER>"}
              alt="discuss banner"
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-auto object-contain ml-7"
            />
            <div className="flex flex-col gap-4 text-center px-6">
              <Typography className="text-[25px] font-medium text-black">
                Thảo luận các chuyên đề
              </Typography>
              <Typography className="text-[16px] font-normal text-emphasize leading-6">
                Phân tích cổ phiếu cụ thể một mã cổ phiếu từ nhiều góc độ. Chiến
                lược đầu tư, thảo luận về các phương pháp như đầu tư giá trị,
                đầu tư tăng trưởng, lướt sóng, giao dịch trong ngày
              </Typography>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="w-full grid grid-cols-12 min-h-[360px] h-auto gap-8">
            <div
              className="w-full h-full col-span-7 relative rounded-[20px] group flex flex-col"
              style={{
                background:
                  "linear-gradient(180deg, #F0FAF9 1.56%, #E3ECEC 100%)",
              }}
            >
              <div className="h-[218px] w-full relative">
                <Image
                  src={"/images/home/<USER>"}
                  alt="avt 1"
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-[85px] h-auto aspect-square absolute inset-0 object-contain transition-transform duration-300 group-hover:scale-90"
                />
                <Image
                  src={"/images/home/<USER>"}
                  alt="avt 2"
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-[85px] h-auto aspect-square absolute bottom-0 left-16 object-contain transition-transform duration-300 group-hover:scale-90"
                />
                <Image
                  src={"/images/home/<USER>"}
                  alt="avt 3"
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-[123px] h-auto aspect-square absolute bottom-[30px] left-1/3 object-contain transition-transform duration-300 group-hover:scale-110"
                />
                <Image
                  src={"/images/home/<USER>"}
                  alt="avt 4"
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-[106px] h-auto aspect-square absolute bottom-0 right-[107px] object-contain transition-transform duration-300 group-hover:scale-90"
                />
                <Image
                  src={"/images/home/<USER>"}
                  alt="avt 5"
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-[106px] h-auto aspect-square absolute top-0 right-0 object-contain transition-transform duration-300 group-hover:scale-90"
                />
              </div>
              <div className="w-full flex justify-center items-start mb-8">
                <div className="flex flex-col gap-4 text-center max-w-[504px] mx-auto">
                  <Typography
                    variant="title-2"
                    className="font-medium text-black"
                  >
                    Tham gia xây dựng cộng đồng
                  </Typography>
                  <Typography>
                    Tham gia cộng đồng Kafi dễ dàng nhanh chóng để nắm bắt thông
                    tin thị trường, theo dõi những chia sẻ về thị trường từ
                    những nhà đầu tư kinh nghiệm
                  </Typography>
                </div>
              </div>
            </div>
            <div
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              className="w-full h-full col-span-5 bg-[#F1F1F1]  rounded-[20px] group"
            >
              <div className="w-full h-1/2 flex justify-center items-center">
                <div className="w-fit h-fit relative">
                  <Image
                    src={"/images/home/<USER>"}
                    alt="message"
                    sizes="100vw"
                    width={0}
                    height={0}
                    className="w-[89px] h-auto aspect-square object-contain transition-transform duration-300 group-hover:scale-110"
                  />
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={
                      isHovered
                        ? { opacity: 1, scale: 1.2 }
                        : { opacity: 0, scale: 0.8 }
                    }
                    transition={{ duration: 0.3 }}
                    className="absolute top-5 left-7 w-fit h-fit"
                  >
                    <Ellipsis className="w-[25px] text-brand" />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={
                      isHovered
                        ? { opacity: 1, scale: 1.2 }
                        : { opacity: 0, scale: 0.8 }
                    }
                    transition={{ duration: 0.3 }}
                    className="absolute bottom-4 left-[52px]  w-fit h-fit"
                  >
                    <Ellipsis className="w-[17px] text-brand" />
                  </motion.div>
                </div>
              </div>
              <div className="w-full h-1/2 flex justify-center">
                <div className="flex flex-col gap-2.5 max-w-[320px] text-center">
                  <Typography
                    variant="title-2"
                    className="font-medium text-black"
                  >
                    Lời khuyên từ các chuyên gia
                  </Typography>
                  <Typography>
                    Cộng đồng giúp nhà đầu tư giữ vững tâm lý, tránh những quyết
                    định vội vàng theo cảm tính
                  </Typography>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full grid grid-cols-12 min-h-[360px] gap-8">
            <div
              className="col-span-5 rounded-[20px] px-8 pt-8 flex flex-col gap-3.5"
              style={{
                background: "linear-gradient(180deg, #F6F6F6 0%, #EEE 100%)",
              }}
            >
              <Typography variant="title-2" className="font-medium text-black">
                Hỏi và giải đáp
              </Typography>
              {mockFAQ.map((item, index) => (
                <div
                  key={index}
                  className="flex gap-2.5 items-start transition-transform duration-300 hover:scale-130"
                  style={{
                    transformOrigin: "left",
                  }}
                >
                  <Image
                    src={item.avatar}
                    alt={`item ${index + 1}`}
                    sizes="100vw"
                    className="w-7 h-auto aspect-square object-contain"
                    width={0}
                    height={0}
                  />
                  <div
                    className="px-4 py-3 rounded-[12px]"
                    style={{
                      backgroundColor: item.color,
                      maxWidth: `${renderMessageBoxWidth(index)}px`,
                    }}
                  >
                    <Typography
                      variant="small-body-regular"
                      className="text-white"
                    >
                      {item.question}
                    </Typography>
                  </div>
                </div>
              ))}
              <div className="w-full flex justify-end">
                <TypingDots />
              </div>
            </div>
            <div
              onMouseEnter={() => setIsHoveredDiscuss(true)}
              onMouseLeave={() => setIsHoveredDiscuss(false)}
              className="col-span-7 rounded-[20px] bg-white pt-[50px]"
            >
              <div className="w-full h-[145px] flex items-end justify-center ">
                <div className="w-fit h-fit relative">
                  <Image
                    src={"/images/home/<USER>"}
                    alt="discuss banner"
                    width={0}
                    height={0}
                    sizes="100vw"
                    className="w-[460px] h-auto aspect-[92/29]"
                  />
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, x: -10, y: 10 }}
                    animate={
                      isHoveredDiscuss
                        ? { opacity: 1, scale: 1, x: 0, y: 0 }
                        : { opacity: 0, scale: 0.8, x: -10, y: 10 }
                    }
                    transition={{ duration: 0.3 }}
                    style={{ transformOrigin: "left bottom" }}
                    className="w-fit h-fit px-3 py-1 bg-brand rounded-full bg absolute top-3 left-10"
                  >
                    <Ellipsis className="w-8 text-white" />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={
                      isHoveredDiscuss
                        ? { opacity: 1, scale: 1, x: 0 }
                        : { opacity: 0, x: -10 }
                    }
                    transition={{ duration: 0.3 }}
                    className="w-fit h-fit px-1 py-1 bg-[#F1F1F1] rounded-full bg absolute top-0 right-[175px] -translate-y-1/3"
                  >
                    <Ellipsis className="w-4 h-auto aspect-[16/9] text-black" />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: 10 }}
                    animate={
                      isHoveredDiscuss
                        ? { opacity: 1, scale: 1, x: 0 }
                        : { opacity: 0, x: 10 }
                    }
                    transition={{ duration: 0.3 }}
                    className="w-fit h-fit px-1 py-1 bg-[#F1F1F1] rounded-full bg absolute top-1/3 right-1/8 "
                  >
                    <Ellipsis className="w-4 h-auto aspect-[16/9] text-black" />
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={
                      isHoveredDiscuss
                        ? { opacity: 1, scale: 1, x: 0 }
                        : { opacity: 0, x: -10 }
                    }
                    transition={{ duration: 0.3 }}
                    className="w-fit h-fit px-1 py-1 bg-[#F1F1F1] rounded-full bg absolute top-8/11 right-1/8 "
                  >
                    <Ellipsis className="w-4 h-auto aspect-[16/9] text-black" />
                  </motion.div>
                </div>
              </div>
              <div className="w-full flex justify-center pb-10">
                <div className="max-w-[504px] text-center flex flex-col gap-4">
                  <Typography
                    variant="title-2"
                    className="font-medium text-black"
                  >
                    Thảo luận các chuyên đề
                  </Typography>
                  <Typography>
                    Phân tích cổ phiếu cụ thể một mã cổ phiếu từ nhiều góc độ.
                    Chiến lược đầu tư, thảo luận về các phương pháp như đầu tư
                    giá trị, đầu tư tăng trưởng, lướt sóng, giao dịch trong ngày
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

function TypingDots() {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="w-fit h-fit px-4 py-3.5 bg-subtle rounded-full flex space-x-1"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {[0, 1, 2].map((i) => (
        <motion.span
          key={i}
          className="w-1 h-1 bg-black rounded-full"
          animate={
            isHovered
              ? {
                  opacity: [1, 0.2, 1],
                  transition: {
                    delay: i * 0.2,
                    repeat: Infinity,
                    repeatType: "loop" as const,
                    duration: 1.2,
                  },
                }
              : { opacity: 1 }
          }
        />
      ))}
    </div>
  );
}

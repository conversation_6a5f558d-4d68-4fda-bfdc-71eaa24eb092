import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  cn(
    "cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-base font-medium transition-all",
    "disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
    "aria-invalid:ring-destructive/20 aria-invalid:border-destructive group",
    "text-wrap max-w-full",
  ),
  {
    variants: {
      variant: {
        default: "btn-primary",
        destructive:
          "bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline: "btn-outline",
        "outline-dark": "btn-outline-dark",
        ghost: "btn-ghost",
        link: "btn-link",
        text: "btn-text has-[>svg]:text-inherit",
        activeShadow:
          "font-medium text-[#1E2328] hover:text-brand bg-white shadow-[0px_4px_6px_-2px_#1018280d,0px_12px_16px_-4px_#1018281a] py-[9px] px-6 rounded-full",
        "button-label": "btn-label",
      },
      size: {
        default: "px-6 py-3 has-[>svg]:px-4",
        sm: "px-4 py-2 has-[>svg]:px-3",
        lg: "px-6 has-[>svg]:px-4",
        icon: "size-[48px] p-[12px] font-base font-semibold",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  const { children, ...rest } = props;

  const isDefault =
    !variant || variant === "default" || variant === "activeShadow";

  const child =
    typeof children === "string" ? (
      <span className="color-inherit">{children}</span>
    ) : (
      <div className="color-inherit">{children}</div>
    );

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size }), className)}
      {...rest}
    >
      {/* @ts-ignore */}
      {isDefault ? child : children}
    </Comp>
  );
}

export { Button, buttonVariants };

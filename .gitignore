# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage
.last-run.json

# storybook
storybook-static/

# next.js
.next/
out/
build
next-env.d.ts

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*
!.env.example
packages/database/.env

# turbo
.turbo

# production
build
dist
dist-ssr

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Sentry Config File
.sentryclirc
# bun
bun.lock
bun.lockb
# Logs
*.log
.turbo
.cache
todos.md
task.md
temp

# Todo2 - AI-powered task management directories
.cursor/rules/todo2-overview.mdc
.cursor/rules/todo2.mdc
.cursor/mcp.json
.todo2/

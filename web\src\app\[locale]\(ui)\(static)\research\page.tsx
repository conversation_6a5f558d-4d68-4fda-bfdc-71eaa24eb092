import { ResearchesView } from "#/src/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Research - Kafi",
  description: "Research - Kafi",
};

const ResearchPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <ResearchesView />
    </Suspense>
  );
};

export default ResearchPage;

export const dynamic = "force-dynamic";

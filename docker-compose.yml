# docker-compose.yml

version: '3.8'

volumes:
  database:
    driver: local

services:
  # postgress
  postgres:
    image: postgres:13.5
    restart: always
    environment:
      - POSTGRES_USER=myuser
      - POSTGRES_PASSWORD=mypassword
    volumes:
      - postgres:/var/lib/postgresql/data
    ports:
      - '5432:5432'

  # mysql:
  #   platform: linux/amd64
  #   image: mysql:8.0.32
  #   container_name: turborepo_mysql
  #   restart: always
  #   ports:
  #     - 3306:3306
  #   environment:
  #     MYSQL_DATABASE: turborepo
  #     MYSQL_ALLOW_EMPTY_PASSWORD: 1
  #   volumes:
  #     - database:/var/lib/mysql

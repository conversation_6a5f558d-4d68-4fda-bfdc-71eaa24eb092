import { LocalizedLink } from "@/components/common/localized-link";
import { Separator, Typography } from "@/components/ui";

const mockTradingData = [
  {
    title: "Giao dịch cổ phiếu/CCQ/ETF/CW",
    detail: [
      {
        name: "<PERSON><PERSON><PERSON><PERSON> hàng chủ động giao dịch",
        value: "0,10%",
      },
      {
        name: "<PERSON>h<PERSON><PERSON> hàng có nhân viên tư vấn đầu tư",
        value: "0,15%",
      },
    ],
  },
  {
    title: "Giao dịch trái phiếu",
    detail: [
      {
        name: "Tất cả giao dịch",
        value: "0,10%",
      },
    ],
  },
];
const TradingFeeSchedule = () => {
  return (
    <div className="container bg-[#F6F7F8] pb-10">
      <div className="flex flex-col gap-10 content">
        <div className="flex flex-col gap-6 md:flex-row md:justify-between">
          <Typography
            variant={"large-title"}
            classname="max-md:text-2xl max-lg:text-3xl"
          >
            Biểu phí giao dịch
          </Typography>
          <LocalizedLink href="/trade/fee">
            <Typography className="text-base text-brand font-semibold md:text-icon-emphasize hover:text-brand cursor-pointer transition-colors">
              Xem tất cả biểu phí
            </Typography>
          </LocalizedLink>
        </div>
        <div className="flex flex-col gap-6 py-6">
          <Typography variant={"title-3"}>Giao dịch qua sàn</Typography>
          <div className="flex flex-col gap-8">
            {mockTradingData.map((data, index) => (
              <div key={index} className="flex flex-col gap-4">
                <Typography variant={"body-bold"} classname="text-emphasize">
                  {data.title}
                </Typography>
                <Separator className="w-full h-[1px] !bg-subtle" />
                <div className="flex flex-col gap-8">
                  {data.detail.map((detail, index) => (
                    <div
                      key={index}
                      className="w-full flex justify-between md:max-w-1/2"
                    >
                      <Typography
                        variant={"body-medium"}
                        classname="text-placeholder"
                      >
                        {detail.name}
                      </Typography>
                      <Typography
                        variant={"body-medium"}
                        classname="text-emphasize"
                      >
                        {detail.value}
                      </Typography>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <Typography variant="caption">
            * Biểu phí giao dịch được áp dụng mặc định, trường hợp có thoả thuận
            khác vui lòng liên hệ nhân viên tư vấn đầu tư
          </Typography>
        </div>
      </div>
    </div>
  );
};

export default TradingFeeSchedule;

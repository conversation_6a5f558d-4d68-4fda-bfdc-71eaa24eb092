"use client";

import { _knowledge, _knowledgeCategories } from "#/src/__mock__/knowledge";
import { Typography } from "#/src/components/ui/typography";
import KnowledgeListPagination from "./knowledge-list-pagination";

export default function KnowledgeView() {
  const renderHead = (
    <div className="flex flex-col justify-center items-center gap-4">
      <Typography variant="special-title"><PERSON><PERSON><PERSON> thức đầu tư</Typography>
      <Typography
        variant="body-regular"
        className="text-center text-emphasize max-w-[630px] mx-auto"
      >
        N<PERSON>i cung cấp thông tin, kiến thức và công cụ giúp bạn hiểu rõ hơn về thị
        trường, nâng cao kỹ năng đầu tư và đưa ra quyết định sáng suốt.
      </Typography>
    </div>
  );
  const renderListPosts = (
    <div className="grid grid-cols-1 h-full">
      <KnowledgeListPagination
        data={_knowledge}
        categories={_knowledgeCategories}
        // pagination={{
        //   page: 1,
        //   pageSize: 10,
        //   total: _knowledge.length,
        // }}
      />
    </div>
  );
  return (
    <div className="container space-y-12">
      {renderHead}
      {renderListPosts}
    </div>
  );
}

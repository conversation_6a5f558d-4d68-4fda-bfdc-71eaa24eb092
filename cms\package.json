{"name": "cms", "version": "0.1.0", "private": true, "description": "Kafivn CMS", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^6.0.2", "@strapi/plugin-cloud": "^5.19.0", "@strapi/plugin-color-picker": "^5.19.0", "@strapi/plugin-users-permissions": "^5.19.0", "@strapi/strapi": "^5.19.0", "fs-extra": "^10.0.0", "mime-types": "^2.1.35", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-navigation": "^3.0.16", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "ajv": "^8.17.1", "minimatch": "^10.0.3", "typescript": "^5"}, "peerDependencies": {"strapi-plugin-navigation": "^3.0.16"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "c65dd0ab757d400798927f91f4d0e1be195fc5114e2cffd9a150748d26ca6741"}}
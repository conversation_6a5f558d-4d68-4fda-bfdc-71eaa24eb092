import { ResearchesDetailView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Research Detail - Kafi",
  description: "Research Detail - Kafi",
};

const ResearchesDetailPage = async () => {
  return (
    <Suspense fallback={<LoadingPage />}>
      <ResearchesDetailView />
    </Suspense>
  );
};

export default ResearchesDetailPage;

export const dynamic = "force-dynamic";

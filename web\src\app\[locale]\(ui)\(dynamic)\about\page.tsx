import { AboutUsView } from "#/src/modules/about";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "About - Kafi",
  description: "About - Kafi",
};

export default async function AboutUsPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <AboutUsView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

"use client";
import { But<PERSON>, Slider, Typography } from "#/src/components/ui";
import { useCallback, useRef, useState } from "react";
import IconBoxCardCustom from "./components/icon-box-card-custom";
import { Play } from "lucide-react";
import { LocalizedLink } from "@/components/common";

export default function PeopleView({ items }: { items: any }) {
  return (
    <div className="flex flex-col  gap-10">
      <div className="container !py-0 flex flex-col gap-10">
        <div className="flex flex-col gap-6">
          <Typography
            variant="large-title"
            className="text-emphasize md:text-center max-md:text-2xl max-lg:text-3xl"
          >
            Con người & Văn hoá
          </Typography>
          <Typography
            variant="body-regular"
            className="text-emphasize text-center max-w-[752px] mx-auto"
          >
            Con người là giá trị cốt lõi – là nơi khởi đầu của mọ<PERSON> hành trình,
            là động lực cho mọi thành tựu của doanh nghiệp và cũng là nhân tố
            chiến lược trong sự phát triển lâu dài của Công ty
          </Typography>
        </div>
        <div className="max-w-[1065px] mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {items.map((item: any, idx: number) => (
              <IconBoxCardCustom key={idx} item={item} />
            ))}
          </div>
        </div>
      </div>
      <VideoSection />
      <JobChance />
    </div>
  );
}

const VideoSection = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlay, setIsPlay] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const handleLoadedData = useCallback(() => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      if (isPlay) videoRef.current.play();
    }
  }, [isPlay]);

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handlePausePlayClick = () => {
    if (!videoRef.current) return;
    if (isPlay) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlay(!isPlay);
  };
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
      .toString()
      .padStart(2, "0");
    const seconds = Math.floor(time % 60)
      .toString()
      .padStart(2, "0");
    return `${minutes}:${seconds}`;
  };
  return (
    <div className="container">
      <div
        className="max-w-[1065px] mx-auto h-auto aspect-[327/427] md:aspect-[532/275] relative rounded-2xl  overflow-hidden"
        onClick={handlePausePlayClick}
      >
        <video
          ref={videoRef}
          src="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
          poster="/images/about/video-thumbnail.jpg"
          className="w-full h-full object-cover"
          onLoadedData={handleLoadedData}
          onTimeUpdate={handleTimeUpdate}
          onEnded={() => setIsPlay(false)}
          // muted
          playsInline
        >
          <track default kind="captions" src="video" />
        </video>
        {!isPlay && (
          <div
            className="w-14 h-auto aspect-square absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 rounded-full flex justify-center items-center"
            style={{
              background: "rgba(21, 20, 17, 0.60)",
              backdropFilter: "blur(20px)",
            }}
          >
            <Play className="text-white" fill="currentColor" />
          </div>
        )}
        <div
          className="w-full h-full absolute top-0 left-0"
          style={{
            background:
              "linear-gradient(180deg, rgba(21, 20, 17, 0) 44.93%, #151411 128.93%)",
          }}
        ></div>
        <div className="w-full min-h-[66px] absolute bottom-0 left-0 z-5 px-9 pb-6 flex flex-col gap-5">
          <Slider
            className="w-full data-[orientation=horizontal]:h-2 rounded-none"
            trackClassName="data-[orientation=horizontal]:h-2 bg-white/38 rounded-none"
            thumbClassName="w-4 h-2 focus-visible:ring-0 hover:ring-0 bg-white rounded-none border-0 shadow-none"
            rangeClassName="bg-white"
            value={[currentTime]}
            max={duration}
            step={0.1}
            onValueChange={([val]) => {
              if (videoRef.current) {
                videoRef.current.currentTime = val;
                setCurrentTime(val);
              }
            }}
          />
          <div className="w-full flex justify-between">
            <Typography classname="text-xs font-medium text-white">
              {formatTime(currentTime)}
            </Typography>
            <Typography classname="text-xs font-medium text-white">
              {formatTime(duration)}
            </Typography>
          </div>
        </div>
      </div>
    </div>
  );
};

const JobChance = () => {
  return (
    <div className="container">
      <div className="bg-screen flex flex-col gap-6 py-10 max-w-[1065px] mx-auto rounded-2xl">
        <Typography
          variant="large-title"
          className="text-emphasize text-center max-md:text-2xl max-lg:text-3xl"
        >
          Cơ hội việc làm tại Kafi
        </Typography>
        <Typography
          variant="body-regular"
          className="text-emphasize text-center max-w-[752px] mx-auto"
        >
          Gia nhập đội ngũ Kafi ngay hôm nay để có nhiều cơ hội phát triển nghề
          nghiệp.
        </Typography>
        <LocalizedLink href={"/ui/recruitment"} className="w-fit mx-auto">
          <Button variant={"default"} className="w-fit ">
            Tìm việc ngay
          </Button>
        </LocalizedLink>
      </div>
    </div>
  );
};

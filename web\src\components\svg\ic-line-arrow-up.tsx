type LineArrowUpIconProps = {
  size?: number;
  className?: string;
};

export const LineArrowUpIcon = ({
  size = 48,
  className,
}: LineArrowUpIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24 38V10M24 10L10 24M24 10L38 24"
        stroke="#767C82"
        className={className}
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

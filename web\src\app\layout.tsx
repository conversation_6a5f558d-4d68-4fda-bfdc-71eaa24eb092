import "@/styles/globals.css";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@components/layout";
import type { Metadata } from "next";
import AnimateSearchButton from "../components/ui/animate-search-button";
import AppProvider from "@/providers/app-provider";
import { SmootherContainer } from "@/components/animates";

export const metadata: Metadata = {
  title: "KAFI",
  description: "KAFI",
  icons: {
    icon: "/logo/logo-k.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi">
      <body className="antialiased relative hide-scrollbar">
        <AppProvider>
          <SmootherContainer>
            <Header />
            <main className="pt-24">{children}</main>
            <Footer />
          </SmootherContainer>

          <AnimateSearchButton />
        </AppProvider>
      </body>
    </html>
  );
}

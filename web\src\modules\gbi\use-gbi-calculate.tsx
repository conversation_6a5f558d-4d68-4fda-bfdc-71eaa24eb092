import { useState } from "react";
import { calculateGBI, GBIParams } from "#/src/services/calculate-gbi";
import { EGbiField, GBIState } from "./configs";

export const DEFAULT_STATE: GBIParams = {
  targetAmount_g1: 0,
  annualReturn_r: 6,
  timePeriod_t: 12,
  initialInvestment_x: 0,
  monthlyDeposit_a: 0,
};

export const INITIAL_STATE: GBIState = {
  ...DEFAULT_STATE,
  gbi: 0,
  gbiPercentage: 0,
  gbiSeries: [],
};

export const useGBICalculator = (initial?: Partial<GBIParams>) => {
  const defaultParams: GBIParams = {
    ...INITIAL_STATE,
    ...initial,
  };

  const [state, setState] = useState<GBIState>({
    ...INITIAL_STATE,
    ...defaultParams,
  });

  const changeValue = (field: EGbiField, value: number) => {
    const newState = calculateGBI({
      fieldChange: field,
      value,
      state,
    });

    setState({
      ...state,
      ...newState,
      [field]: value,
    });

    return {
      ...state,
      ...newState,
      [field]: value,
    };
  };

  const reset = () => {
    setState({ ...INITIAL_STATE });
    return { ...INITIAL_STATE };
  };

  return {
    state,
    changeValue,
    reset,
  };
};

type MenuActiveIconProps = {
  size?: number;
  className?: string;
};

export const MenuActiveIcon = ({
  size = 24,
  className,
}: MenuActiveIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 12H21M3 6H21M3 18H15"
        stroke="url(#paint0_linear_menu_active)"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_menu_active"
          x1="3"
          y1="12"
          x2="21"
          y2="12"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#19C574" />
          <stop offset="1" stopColor="#2AAB8A" />
        </linearGradient>
      </defs>
    </svg>
  );
};

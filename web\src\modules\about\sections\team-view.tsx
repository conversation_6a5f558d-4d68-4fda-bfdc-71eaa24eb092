import { ABOUT_STAFF_CONTROL_DEPARTMENT } from "#/src/__mock__/about";
import { Typography } from "#/src/components/ui";
import StaffCard from "./components/staff-card";

export interface StaffProp {
  pronoun: string;
  name: string;
  position: string;
  experience: string;
  image: string;
  imageClassName?: string;
}

export interface DepartmentProp {
  department: string;
  people: StaffProp[];
}

interface TeamProp {
  items: DepartmentProp[];
}

export default function TeamView({ items }: TeamProp) {
  return (
    <div className="flex flex-col container !py-0 gap-10 lg:gap-20">
      {items.map((item, index) => (
        <div
          key={index}
          className="flex flex-col gap-8 md:gap-10 lg:gap-20 max-w-[986px] mx-auto"
        >
          <Typography
            variant="large-title"
            className="text-emphasize md:text-center max-md:text-2xl max-lg:text-3xl"
          >
            {item.department}
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-10 lg:gap-x-10 lg:gap-y-20">
            {item.people.map((person, index) => (
              <StaffCard
                key={index}
                {...person}
                imageClassName="aspect-[302/235] lg:aspect-[2/1]"
              />
            ))}
          </div>
        </div>
      ))}
      <div className="flex flex-col gap-8 md:gap-10 lg:gap-20 max-w-[986px] mx-auto">
        <Typography
          variant="large-title"
          className="text-emphasize md:text-center max-md:text-2xl max-lg:text-3xl"
        >
          {ABOUT_STAFF_CONTROL_DEPARTMENT.department}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-10 lg:gap-x-10 lg:gap-y-20">
          {ABOUT_STAFF_CONTROL_DEPARTMENT.people.map((person, index) => (
            <StaffCard
              key={index}
              {...person}
              imageClassName="aspect-[302/235]"
            />
          ))}
        </div>
      </div>
    </div>
  );
}

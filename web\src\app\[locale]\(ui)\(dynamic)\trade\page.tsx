import { TradeView } from "@/modules/pages";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Trade - Kafi",
  description: "Trade - Kafi",
};

export default async function TradePage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <TradeView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

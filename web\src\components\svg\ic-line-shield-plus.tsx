import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: "round" | "butt" | "square" | "inherit";
  strokeLinejoin?: "round" | "inherit" | "miter" | "bevel";
  className?: string;
};

const IcLineShieldPlus = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  strokeWidth = 1,
  strokeLinecap = "round",
  strokeLinejoin = "round",
  className,
}: TProps) => {
  const pathScale = Number(size) / 24;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M12 14.5V8.5M9 11.5H15M20 12C20 16.909 14.646 20.478 12.698 21.615C12.477 21.744 12.366 21.809 12.21 21.842C12.088 21.868 11.912 21.868 11.79 21.842C11.634 21.809 11.523 21.744 11.302 21.615C9.354 20.478 4 16.909 4 12V7.218C4 6.418 4 6.018 4.131 5.675C4.246 5.371 4.434 5.1 4.678 4.886C4.954 4.642 5.328 4.502 6.076 4.221L11.438 2.211C11.646 2.133 11.75 2.094 11.857 2.078C11.952 2.065 12.048 2.065 12.143 2.078C12.25 2.094 12.354 2.133 12.562 2.211L17.924 4.221C18.672 4.502 19.046 4.642 19.322 4.886C19.566 5.1 19.754 5.371 19.869 5.675C20 6.018 20 6.418 20 7.218V12Z"
        stroke="currentColor"
        strokeWidth={strokeWidth}
        strokeLinecap={strokeLinecap}
        strokeLinejoin={strokeLinejoin}
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcLineShieldPlus;

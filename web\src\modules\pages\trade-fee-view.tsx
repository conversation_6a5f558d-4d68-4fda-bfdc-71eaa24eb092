"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@/components/ui";
import { FEE_PAGE_DATA, FeeCategory, FeeSection } from "@/__mock__/fee";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const FeeItemRow = ({ name, value }: { name: string; value: string }) => (
  <div className="w-full grid grid-cols-2 items-start py-4 gap-4">
    <p className="text-base text-icon-placeholder font-medium">{name}</p>
    <div
      className="text-base text-icon-emphasize whitespace-normal font-medium break-words"
      style={{ overflowWrap: "anywhere", wordBreak: "break-word" }}
      dangerouslySetInnerHTML={{ __html: value }}
    />
  </div>
);

const FeeCategoryComponent = ({ category }: { category: FeeCategory }) => (
  <div className="flex flex-col">
    <div>
      <p className="text-base font-semibold text-icon-emphasize py-4">
        {category.title}
      </p>
      <Separator className="w-full h-[1px] !bg-subtle" />
    </div>

    <div className="flex flex-col">
      {category.items.map((item, index) => (
        <FeeItemRow key={index} name={item.name} value={item.value} />
      ))}
    </div>

    {/* Note */}
    {category.note && (
      <span className="text-[12px] text-[#313941]">{category.note}</span>
    )}
  </div>
);

const FeeSectionComponent = ({ section }: { section: FeeSection }) => (
  <div className="flex flex-col gap-6 py-6">
    {/* Title & Subtitle */}
    <div className="flex flex-col gap-2">
      <p className="text-2xl font-semibold text-icon-emphasize">
        {section.title}
      </p>
      {/* {section.subtitle && (
        <Typography variant="body2" className="text-base text-icon-placeholder">
          {section.subtitle}
        </Typography>
      )} */}
    </div>

    {/* Categories & Note*/}
    <div>
      <div className="flex flex-col">
        {section.categories.map((category, index) => (
          <FeeCategoryComponent key={index} category={category} />
        ))}
      </div>
      {section.generalNote && (
        <span className="text-[12px] text-[#313941] mt-2.5">
          {section.generalNote}
        </span>
      )}
    </div>
  </div>
);

const TradeFeeView = () => {
  const { pageTitle, pageDescription, sections } = FEE_PAGE_DATA;

  return (
    <div className="container">
      {/* Header Section */}
      <Link href={"/trade"}>
        <Button variant="text" className="text-base font-semibold my-4 !px-0">
          <ArrowLeft />
          <span>Quay lại</span>
        </Button>
      </Link>
      <div className="xl:max-w-[1064px] mx-auto">
        <div className="flex flex-col gap-6 mb-10">
          <div className="text-[24px] sm:text-[30px] lg:text-[40px] font-bold text-icon-emphasize text-left lg:text-center">
            {pageTitle}
          </div>
          {pageDescription && (
            <div className="text-lg text-icon-placeholder text-center max-w-3xl mx-auto">
              {pageDescription}
            </div>
          )}
        </div>

        {/* Fee Sections */}
        <div className="flex flex-col">
          {sections.map((section, index) => (
            <FeeSectionComponent key={index} section={section} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TradeFeeView;

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

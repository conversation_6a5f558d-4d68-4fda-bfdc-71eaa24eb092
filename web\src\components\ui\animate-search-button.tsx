"use client";
import { motion } from "framer-motion";
import Image from "next/image";
import Typography from "./typography";
import { useState, useRef, useEffect } from "react";
import { Input } from "./input";
import { cn } from "#/src/lib";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useMediaQuery } from "#/src/hooks";
import { Button } from "./button";

const exampleSearchText = [
  "Sản phẩm của <PERSON>?",
  "Phí giao dịch?",
  "Hướng dẫn mở tài khoản",
];

export default function AnimateSearchButton() {
  const pathname = usePathname();
  const isMobile = useMediaQuery("(max-width: 743px)");
  const containerRef = useRef<HTMLDivElement>(null);
  const params = useSearchParams();
  const search = params.get("search");
  const router = useRouter();
  const [openSearchBar, setOpenSearchBar] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setOpenSearchBar(false);
      }
    };

    if (openSearchBar) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openSearchBar]);
  useEffect(() => {
    setOpenSearchBar(false);
  }, []);
  useEffect(() => {
    setSearchValue(search || "");
  }, [search]);

  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      router.push(`/search?search=${searchValue}`);
    }
  };

  const handleChangeSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  return pathname.includes("/chat") ? null : (
    <div
      ref={containerRef}
      className="fixed z-[99] bottom-6 left-1/2 -translate-x-1/2 flex flex-col gap-4"
    >
      <motion.div
        className="flex gap-4"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: openSearchBar ? 1 : 0, y: openSearchBar ? 0 : 50 }}
        transition={{ duration: 0.5 }}
      >
        {exampleSearchText.map((text, index) => (
          <Button
            variant="outline"
            className="rounded-xl hover:!bg-brand !border-0 !text-white !bg-brand "
            key={index}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setSearchValue(text);
            }}
          >
            {text}
          </Button>
        ))}
      </motion.div>
      <motion.button
        style={{
          boxShadow:
            "0 32px 64px -12px rgba(16, 24, 40, 0.20), 0 -32px 64px -12px rgba(16, 24, 40, 0.20)",
        }}
        className={cn(
          " h-fit min-h-[52px] bg-white rounded-full px-3 py-2.5 group cursor-pointer mx-auto relative",
          openSearchBar && "pl-8",
        )}
        initial={{ width: "auto" }}
        animate={{
          width: openSearchBar ? (isMobile ? "157px" : "610px") : "157px",
        }}
        transition={{ duration: 0.5 }}
        onClick={() => setOpenSearchBar(true)}
      >
        {!openSearchBar && (
          <div className="w-full flex items-center relative">
            <Typography variant="body-medium" classname="px-4 shrink-0">
              {" "}
              Hỏi Kafi
            </Typography>

            <Image
              src="/images/stars.png"
              alt="stars"
              width={0}
              height={0}
              className="w-8 h-8"
              sizes="100vw"
            />
          </div>
        )}
        {openSearchBar && (
          <>
            <Input
              placeholder="Hỏi Kafi"
              className="p-0 pr-9 border-none rounded-none placeholder:text-base placeholder:text-[#A3A3A3] placeholder:font-medium focus-visible:ring-0 focus-visible:ring-offset-0"
              onKeyDown={onKeyDown}
              onChange={handleChangeSearch}
              value={searchValue}
            />
            <SearchIcon className="absolute right-4 top-1/2 -translate-y-1/2" />
          </>
        )}
      </motion.button>
    </div>
  );
}

const SearchIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.875 21.7598C20.5793 21.2123 21.2131 20.5784 21.7606 19.8741L28.9436 27.0571C29.4643 27.5778 29.4643 28.4221 28.9436 28.9428C28.4229 29.4635 27.5787 29.4635 27.058 28.9428L19.875 21.7598Z"
        fill="#959A9E"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.3327 21.3333C17.751 21.3333 21.3327 17.7516 21.3327 13.3333C21.3327 8.91502 17.751 5.33329 13.3327 5.33329C8.9144 5.33329 5.33268 8.91502 5.33268 13.3333C5.33268 17.7516 8.9144 21.3333 13.3327 21.3333ZM13.3327 24C19.2237 24 23.9993 19.2243 23.9993 13.3333C23.9993 7.44226 19.2237 2.66663 13.3327 2.66663C7.44165 2.66663 2.66602 7.44226 2.66602 13.3333C2.66602 19.2243 7.44165 24 13.3327 24Z"
        fill="#00C694"
      />
    </svg>
  );
};

{"kind": "collectionType", "collectionName": "awards", "info": {"singularName": "award", "pluralName": "awards", "displayName": "Awards"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "award_name": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}}}
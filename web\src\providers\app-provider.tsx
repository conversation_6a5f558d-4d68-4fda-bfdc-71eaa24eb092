"use client";

import React from "react";
import { Toaster } from "../components/ui";
import { SlugProvider } from "@/context/slug-context";
import { SiteProvider } from "@/context/use-site-context";

type TProps = {
  children: React.ReactNode;
};

const AppProvider = ({ children }: TProps) => {
  return (
    <SiteProvider>
      <SlugProvider>
        {children}
        <Toaster />
      </SlugProvider>
    </SiteProvider>
  );
};

export default AppProvider;

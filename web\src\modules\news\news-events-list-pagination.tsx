"use client";

import { TNewsEvents } from "@/types/news-event";
import NewsEventsCard from "./news-events-card";
import { TPagination } from "@/types/common";
import AppPagination from "@/components/common/app-pagination";
import { IcSwitchVertical01 } from "#/src/components";
import { Button } from "#/src/components/ui";
import { cn } from "#/src/lib";
import { useRouter, usePathname, useSearchParams } from "next/navigation";

type TProps = {
  data: TNewsEvents[];
  pagination: TPagination;
  classname?: string;
};

const getActiveClass = (current: string | null, value: string) => {
  if (value == "all" && !current) return "activeShadow";
  return current === value ? "activeShadow" : "text";
};

const NewsEventsListPagination = ({ data, pagination, classname }: TProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const category = searchParams.get("q");
  const sort = searchParams.get("sort");

  const handleCategory = (category: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("q", category);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };

  const handleSort = (sort: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("sort", sort);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    router.refresh();
  };

  const renderCategory = (
    <div className="flex gap-4 w-full items-center justify-end md:justify-between flex-wrap">
      <div className="flex gap-2 items-start bg-[#F1F3F5] p-2 w-fit rounded-full ">
        <Button
          variant={getActiveClass(category, "all")}
          onClick={() => handleCategory("")}
        >
          Tất cả
        </Button>
        <Button
          variant={getActiveClass(category, "tin-tuc")}
          onClick={() => handleCategory("tin-tuc")}
        >
          Tin tức
        </Button>
        <Button
          variant={getActiveClass(category, "su-kien")}
          onClick={() => handleCategory("su-kien")}
        >
          Sự kiện
        </Button>
      </div>

      <div className="flex flex-col gap-2">
        <Button
          variant="ghost"
          className="gap-2 rounded-lg !bg-white text-black"
          onClick={() => handleSort(sort === "newest" ? "oldest" : "newest")}
        >
          {sort === "newest" ? "Cũ nhất" : "Mới nhất"}
          <IcSwitchVertical01 size={20} className="text-icon-placeholder" />
        </Button>
      </div>
    </div>
  );

  const renderListPosts = (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
        classname,
      )}
    >
      {data.map((item) => (
        <NewsEventsCard key={item.id} item={item} hideDescription={true} />
      ))}
    </div>
  );

  return (
    <div className="flex flex-col gap-10">
      {renderCategory}

      {renderListPosts}

      <AppPagination
        page={pagination.page}
        pageSize={pagination.pageSize}
        total={pagination.total}
        onChange={(nextPage) => {
          console.log(nextPage);
        }}
      />
    </div>
  );
};

export default NewsEventsListPagination;

{"collectionName": "components_elementals_podcasts", "info": {"displayName": "podcast"}, "options": {}, "attributes": {"social_links": {"type": "component", "component": "shared.social-link", "repeatable": true}, "items": {"type": "relation", "relation": "oneToMany", "target": "api::podcast.podcast"}, "auto_filter": {"type": "boolean", "default": false}, "auto_filter_limit": {"type": "integer"}}, "config": {}}
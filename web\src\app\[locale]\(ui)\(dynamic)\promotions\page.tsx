import PromotionsView from "#/src/modules/promotions/promotions-view";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";
//-----------------------------------------------------------
export const metadata: Metadata = {
  title: "Ưu đãi - Kafi",
  description: "Ưu đãi - Kafi",
};
//-----------------------------------------------------------

export default async function PromotionsPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <PromotionsView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

import { KnowledgeView } from "#/src/modules/pages/knowledge";
import LoadingPage from "@/components/loading/loading-page";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Knowledge - Kafi",
  description: "Knowledge - Kafi",
};

export default async function KnowledgePage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <KnowledgeView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  <PERSON>po<PERSON>,
} from "@/components/ui";
import { useState } from "react";

// Tab configuration
const TAB_CONFIG = [
  {
    key: "gioi-thieu",
    label: "Giới thiệu",
  },
  {
    key: "cham-soc",
    label: "Chăm sóc",
  },
];

const CALCULATOR_DATA = {
  maxValue: 50000000000, // 50 tỷ
  minValue: 1000000, // 1 triệu
  step: 1000000, // 1 triệu
  kPointsRate: 0.0001, // 1 VND = 0.0001 K-Points
  commissionRate: 0.0001, // 0.01% commission
};

function formatNumber(num: number): string {
  return new Intl.NumberFormat("vi-VN").format(num);
}

function formatCurrency(num: number): string {
  return `${formatNumber(num)}`;
}

function calculateKPoints(value: number): number {
  return Math.floor(value * CALCULATOR_DATA.kPointsRate);
}

function calculateCommission(value: number): number {
  return Math.floor(value * CALCULATOR_DATA.commissionRate);
}

// Dynamic Content Renderer
function DynamicTabContent({
  activeTab,
  sliderValue,
  onSliderChange,
}: {
  activeTab: string;
  sliderValue: number;
  onSliderChange: (value: number[]) => void;
}) {
  const _kPoints = calculateKPoints(sliderValue);
  const _commission = calculateCommission(sliderValue);

  if (activeTab === "gioi-thieu") {
    return (
      <div className="flex flex-col gap-8">
        {/* Title and amount */}
        <div className="text-center flex items-center flex-col md:flex-row justify-center gap-3">
          <div className="text-gray-600 text-base lg:text-xl">
            Giá trị đối tác đã giao dịch
          </div>
          <div>
            <span className="font-semibold text-lg lg:text-2xl">
              {formatCurrency(sliderValue)}
            </span>
            <span className="ml-1 ext-base lg:text-xl">VND</span>
          </div>
        </div>

        {/* Slider */}
        <div className="k-points-slider">
          <Slider
            value={[sliderValue]}
            onValueChange={onSliderChange}
            max={CALCULATOR_DATA.maxValue}
            min={CALCULATOR_DATA.minValue}
            step={CALCULATOR_DATA.step}
            className="w-full"
            trackClassName="data-[orientation=horizontal]:h-[7px]"
            rangeClassName="bg-brand"
            thumbClassName="bg-brand ring-brand border-brand"
          />
        </div>

        {/* K-Points info */}
        <div className="text-center flex flex-col md:flex-row items-center justify-center gap-3 text-base lg:text-2xl">
          <div>Partner sẽ nhận </div>
          <div className="text-base lg:text-xl bg-canvas rounded-2xl py-2 px-4">
            <span className="text-brand font-bold text-2xl lg:text-[30px]">
              {formatNumber(_kPoints)}
            </span>{" "}
            <span className="text-base lg:text-2xl">K-Points</span>
          </div>
          <div>Tương đương với </div>
          <div className="font-semibold">{formatCurrency(_commission)} VND</div>
        </div>
      </div>
    );
  }

  if (activeTab === "cham-soc") {
    return (
      <div className="flex flex-col gap-8">
        {/* Title and amount */}
        <div className="text-center flex items-center flex-col md:flex-row justify-center gap-3">
          <div className="text-gray-600 text-base lg:text-xl">
            Giá trị đối tác đã giao dịch
          </div>
          <div>
            <span className="font-semibold text-lg lg:text-2xl">
              {formatCurrency(sliderValue)}
            </span>
            <span className="ml-1 ext-base lg:text-xl">VND</span>
          </div>
        </div>

        {/* Slider */}
        <div className="k-points-slider">
          <Slider
            value={[sliderValue]}
            onValueChange={onSliderChange}
            max={CALCULATOR_DATA.maxValue}
            min={CALCULATOR_DATA.minValue}
            step={CALCULATOR_DATA.step}
            className="w-full"
            trackClassName="data-[orientation=horizontal]:h-[7px]"
            rangeClassName="bg-brand"
            thumbClassName="bg-brand ring-brand border-brand"
          />
        </div>

        {/* K-Points info */}
        <div className="text-center flex flex-col md:flex-row items-center justify-center gap-3 text-base lg:text-2xl">
          <div>Partner sẽ nhận </div>
          <div className="text-base lg:text-xl bg-canvas rounded-2xl py-2 px-4">
            <span className="text-brand font-bold text-2xl lg:text-[30px]">
              {formatNumber(_kPoints)}
            </span>{" "}
            <span className="text-base lg:text-2xl">K-Points</span>
          </div>
          <div>Tương đương với </div>
          <div className="font-semibold">{formatCurrency(_commission)} VND</div>
        </div>
      </div>
    );
  }

  return null;
}

export default function KPointsCalculator() {
  const [activeTab, setActiveTab] = useState("gioi-thieu");
  const [sliderValue, setSliderValue] = useState([20000000000]);

  const currentValue = sliderValue[0] ?? 20000000000;

  return (
    <div className="flex flex-col items-center justify-center container">
      <div className="w-full">
        {/* Typography always on top */}
        <div className="space-y-6 xl:space-y-10 max-w-6xl w-full mx-auto">
          <div className="w-full mx-auto space-y-4">
            <Typography className="text-center font-bold text-2xl lg:text-[36px]">
              Công cụ tính K-Points
            </Typography>
            {/* Tab controls */}
            <div className="flex space-x-3 justify-center mb-4">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="h-[50px] bg-[#F1F3F5] rounded-full p-1 w-full flex space-x-3 justify-center">
                  {TAB_CONFIG.map((tab) => (
                    <TabsTrigger
                      key={tab.key}
                      value={tab.key}
                      className="cursor-pointer text-base px-5 py-2 rounded-full font-medium text-gray-700 bg-white transition-all min-w-[90px] select-none data-[state=inactive]:text-gray-600 data-[state=inactive]:bg-gray-100 data-[state=inactive]:shadow-none"
                      style={
                        activeTab === tab.key
                          ? {
                              boxShadow:
                                "0px 4px 6px -2px #1018280D, 0px 12px 16px -4px #1018281A",
                            }
                          : undefined
                      }
                    >
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Tab content card */}
          <div className="bg-white rounded-xl p-10">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              {TAB_CONFIG.map((tab) => (
                <TabsContent key={tab.key} value={tab.key} className="mt-0">
                  <DynamicTabContent
                    activeTab={activeTab}
                    sliderValue={currentValue}
                    onSliderChange={setSliderValue}
                  />
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

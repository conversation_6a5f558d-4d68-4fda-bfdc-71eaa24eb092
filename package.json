{"name": "kafi.vn", "version": "1.0.0", "description": "Kafi Website", "type": "module", "scripts": {"web": "bun dev --prefix ../web/", "cms": "bun dev --prefix ../cms/", "setup:web": "cd web && bun install && node --loader ts-node/esm ../scripts/copy-env.mts ./", "setup:cms": "cd cms && bun install && node --loader ts-node/esm ../scripts/copy-env.mts ./", "setup": "bun install && bun setup:web && bun setup:cms", "dev": "bun concurrently --names cms,web --c blue,yellow \"cd cms && bun develop\" \"npx wait-on http://localhost:1338 && cd web && bun dev\"", "build": "bun concurrently --names cms,web --c blue,yellow \"cd cms && bun run build\" \"cd web && bun run build\"", "lint": "npx @biomejs/biome check --write", "seed": "cd cms && bun strapi import -f ./data/export_20250116105447.tar.gz --force", "export": "cd cms && bun strapi export --no-encrypt -f ./data/export_20250116105447"}, "dependencies": {"@types/node": "^22.5.2", "concurrently": "^8.2.2", "typescript": "^5.0.0", "wait-on": "^8.0.1"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lefthook": "^1.12.2", "ts-node": "^10.9.2"}}
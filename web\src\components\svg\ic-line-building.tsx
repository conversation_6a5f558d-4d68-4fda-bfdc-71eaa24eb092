import SvgIcon from "./icon";

type TProps = {
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  size?: string | number;
  strokeWidth?: string | number;
  strokeLinecap?: "round" | "butt" | "square" | "inherit";
  strokeLinejoin?: "round" | "inherit" | "miter" | "bevel";
  className?: string;
};

const IcLineBuilding = ({
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  strokeWidth = 1,
  strokeLinecap = "round",
  strokeLinejoin = "round",
  className,
}: TProps) => {
  const pathScale = Number(size) / 24;

  return (
    <SvgIcon
      size={size}
      color={color}
      hoverColor={hoverColor}
      activeColor={activeColor}
      className={className}
    >
      <path
        d="M7.5 11H4.6C4.04 11 3.76 11 3.546 11.109C3.356 11.205 3.205 11.356 3.109 11.546C3 11.76 3 12.04 3 12.6V21M16.5 11H19.4C19.96 11 20.24 11 20.454 11.109C20.644 11.205 20.795 11.356 20.891 11.546C21 11.76 21 12.04 21 12.6V21M16.5 21V6.2C16.5 5.08 16.5 4.52 16.282 4.092C16.09 3.716 15.784 3.41 15.408 3.218C14.98 3 14.42 3 13.3 3H10.7C9.58 3 9.02 3 8.592 3.218C8.216 3.41 7.91 3.716 7.718 4.092C7.5 4.52 7.5 5.08 7.5 6.2V21M22 21H2M11 7H13M11 11H13M11 15H13"
        stroke="currentColor"
        strokeWidth={strokeWidth}
        strokeLinecap={strokeLinecap}
        strokeLinejoin={strokeLinejoin}
        style={{ transform: `scale(${pathScale})` }}
      />
    </SvgIcon>
  );
};

export default IcLineBuilding;

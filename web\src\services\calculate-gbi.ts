/**
 * GBI Calculator Service
 *
 * This service is used to calculate the GBI of a user.
 *
 * Params
 * - targetAmount: number      // g1 - Target amount (slider), default: 0
 * - annualReturn: number // r - Annual interest rate (%), default: 6%
 * - timePeriod: number  // t - Duration in months, default: 12
 * - initialInvestment: number // x - Initial investment (disabled)
 * - monthlyDeposit: number   // a - Monthly deposit amount (disabled)
 *
 * Returns:
 * - GBI: number // g - Reality amount (integer)
 * - gbiPercentage: number // GBI percentage (%)
 * - gbiSeries: {x: number, y: number}[] // Array of points (month, amount) for chart generation
 *
 * Initial investment ratio based on target amount:
 * If g < 500M	 Z = 30% * g
 * If 500M <= g < 1B	Z = 40% * g
 * If g >= 1B	Z = 50% * g
 *
 * Formulas:
 * x = z*g (Only when g changes)
 * m = ((1+r/12)^t-1) / (r/12)
 * g = x(1+r/12)^t + a*m
 * a = (g- x(1+r/12)^t) / m
 *
 * **/

import { EGbiField, SLIDER_CONFIG } from "@/modules/gbi/configs";
import { DEFAULT_STATE } from "@/modules/gbi/use-gbi-calculate";

export interface GBIParams {
  /** Target future amount (g1) */
  targetAmount_g1: number;

  /** Annual interest rate in % (r), e.g., 6 = 6% */
  annualReturn_r: number;

  /** Duration in months (t) */
  timePeriod_t: number;

  /** Optional initial investment (x), calculated if not provided */
  initialInvestment_x?: number;

  /** Optional monthly deposit (a), calculated if not provided */
  monthlyDeposit_a?: number;
}

export interface GBIResult {
  /** Final projected value (g) */
  gbi: number;

  /** Percent over or under target amount */
  gbiPercentage: number;

  /** Time series of investment value per month */
  gbiSeries: Array<{
    x: number;
    y_achieved?: number | null;
    y_suggested: number;
    x_current: number;
  }>;
}
export interface GBIStateReturn extends GBIParams {
  /** Monthly multiplier used in calculations */
  m: number;
}

/**
 * Returns the initial investment ratio based on target amount.
 */
export const getInitialInvestmentRatio_Z = (
  targetAmount_g1: number,
): number => {
  if (targetAmount_g1 < 500_000_000) return 0.3;
  if (targetAmount_g1 < 1_000_000_000) return 0.4;
  return 0.5;
};

/**
 * Calculates the monthly multiplier (m) used for future value calculations.
 * m = ((1+r/12)^t-1) / (r/12)
 * @param annualRate_r - Annual rate as percentage (e.g., 6 for 6%)
 * @param timePeriod_t - Time period in months
 */
const calculateMonthlyMultiplier = (
  annualRate_r: number,
  timePeriod_t: number,
): number => {
  if (annualRate_r === 0) return timePeriod_t;
  const monthlyRate = annualRate_r / 100 / 12; // Convert percentage to decimal then to monthly
  return ((1 + monthlyRate) ** timePeriod_t - 1) / monthlyRate;
};

/**
 * Calculates monthly deposit if not provided.
 * a = (g- x(1+r/12)^t) / m
 * @param targetAmount_g1 - Target amount (g)
 * @param initialInvestment_x - Initial investment (x)
 * @param annualReturn_r - Annual return as percentage (e.g., 6 for 6%)
 * @param timePeriod_t - Time period in months (t)
 * @param m - Monthly multiplier
 */
const calculateMonthlyDeposit = (
  targetAmount_g1: number, // g
  initialInvestment_x: number, // x
  annualReturn_r: number, // r (as percentage)
  timePeriod_t: number, // t
  m: number, // m
): number => {
  const monthlyRate = annualReturn_r / 100 / 12; // Convert to decimal monthly rate
  return (
    (targetAmount_g1 -
      initialInvestment_x * (1 + monthlyRate) ** timePeriod_t) /
    m
  );
};

const calculateGbi = (state: GBIStateReturn): number => {
  //   g = x(1+r/12)^t + a*m
  const x = state.initialInvestment_x ?? 0;
  const r = state.annualReturn_r; // Already as percentage
  const t = state.timePeriod_t;
  const a = state.monthlyDeposit_a ?? 0;
  const m = state.m;

  if (x === 0 && a === 0) return 0;

  const monthlyRate = r / 100 / 12; // Convert percentage to decimal monthly rate
  return x * (1 + monthlyRate) ** t + a * m;
};

/**
 * Generates a series of monthly investment growth over time.
 * Starts from initial investment (not 0) to match Figma design.
 */
const generateGbiSeries = (
  currentMonth: number, // t
  state: GBIStateReturn,
): {
  x: number;
  y_achieved?: number | null;
  y_suggested: number;
  x_current: number;
}[] => {
  const series: {
    x: number;
    y_achieved?: number | null;
    y_suggested: number;
    x_current: number;
  }[] = [];
  const maxMonth = SLIDER_CONFIG.timePeriod.max;

  // Get the monthly rate and monthly deposit
  const monthlyRate = state.annualReturn_r / 100 / 12;
  const initialInvestment = state.initialInvestment_x || 0;
  const monthlyDeposit = state.monthlyDeposit_a || 0;

  for (let month = 0; month <= maxMonth; month++) {
    let value: number;

    if (month === 0) {
      // Month 0: Start with initial investment (like Figma)
      value = initialInvestment;
    } else {
      // Calculate compound growth: initial investment grows + monthly deposits accumulate
      const futureValueOfInitial =
        initialInvestment * (1 + monthlyRate) ** month;
      const futureValueOfDeposits =
        monthlyDeposit * (((1 + monthlyRate) ** month - 1) / monthlyRate);
      value = futureValueOfInitial + futureValueOfDeposits;
    }

    series.push({
      x: month,
      ...(month <= currentMonth && {
        y_achieved: value,
      }),
      y_suggested: value,
      x_current: month === currentMonth ? currentMonth : 0,
    });
  }

  return series;
};

// Case 1: g thay đổi: tính lại: m,x,a,g,g%
const handleTargetAmountChange = (
  targetAmount: number, // g1
  state: GBIParams,
): GBIStateReturn => {
  const r = state.annualReturn_r; // Keep as percentage
  const t = state.timePeriod_t;
  const m = calculateMonthlyMultiplier(r, t);

  const zRatio = getInitialInvestmentRatio_Z(targetAmount);
  const x = targetAmount * zRatio;

  const a = calculateMonthlyDeposit(targetAmount, x, r, t, m);

  return {
    ...state,
    targetAmount_g1: targetAmount,
    m,
    initialInvestment_x: x,
    monthlyDeposit_a: a,
  };
};

// Case 2: r thay đổi: tính lại: m,a,gbi,gbiPercentage
const handleAnnualReturnChange = (
  annualReturn: number,
  state: GBIParams,
): GBIStateReturn => {
  const r = annualReturn; // Keep as percentage
  const t = state.timePeriod_t;
  const m = calculateMonthlyMultiplier(r, t);

  const x = state.initialInvestment_x ?? 0;
  const a = calculateMonthlyDeposit(state.targetAmount_g1, x, r, t, m);

  return {
    ...state,
    annualReturn_r: annualReturn,
    monthlyDeposit_a: a,
    m,
  };
};

// Case 3: t thay đổi: tính lại: m,a,g,g%
const handleTimePeriodChange = (
  timePeriod: number,
  state: GBIParams,
): GBIStateReturn => {
  const r = state.annualReturn_r; // Keep as percentage
  const t = timePeriod;
  const m = calculateMonthlyMultiplier(r, timePeriod);

  const x = state.initialInvestment_x ?? 0;
  const a = calculateMonthlyDeposit(state.targetAmount_g1, x, r, t, m);

  return {
    ...state,
    timePeriod_t: timePeriod,
    monthlyDeposit_a: a,
    m,
  };
};

// Case 4: x thay đổi: tính lại: m,a,gbi,gbiPercentage
const handleInitialInvestmentChange = (
  initialInvestment: number,
  state: GBIParams,
): GBIStateReturn => {
  const x = initialInvestment;
  const r = state.annualReturn_r; // Keep as percentage
  const t = state.timePeriod_t;
  const g1 = state.targetAmount_g1;
  const m = calculateMonthlyMultiplier(r, t); // x must less than m(%) * g1
  const a = calculateMonthlyDeposit(g1, x, r, t, m);

  // Target Amount must be greater than initial investment
  if (initialInvestment >= state.targetAmount_g1) {
    return {
      ...state,
      initialInvestment_x: initialInvestment,
      targetAmount_g1: initialInvestment,
      monthlyDeposit_a: a,
      m,
    };
  }

  return {
    ...state,
    initialInvestment_x: initialInvestment,
    monthlyDeposit_a: a,
    m,
  };
};

// Case 5: a thay đổi: tính lại: m,gbi,gbiPercentage
const handleMonthlyDepositChange = (
  monthlyDeposit: number,
  state: GBIParams,
): GBIStateReturn => {
  const r = state.annualReturn_r; // Keep as percentage
  const t = state.timePeriod_t;

  const m = calculateMonthlyMultiplier(r, t);

  return {
    ...state,
    monthlyDeposit_a: monthlyDeposit,
    m,
  };
};

const handleFieldChange = (
  field: EGbiField,
  value: number,
  state: GBIParams,
): GBIStateReturn => {
  switch (field) {
    case EGbiField.TargetAmount_g1:
      return handleTargetAmountChange(value, state);

    case EGbiField.AnnualReturn_r:
      return handleAnnualReturnChange(value, state);

    case EGbiField.TimePeriod_t:
      return handleTimePeriodChange(value, state);

    case EGbiField.InitialInvestment_x:
      return handleInitialInvestmentChange(value, state);

    case EGbiField.MonthlyDeposit_a:
      return handleMonthlyDepositChange(value, state);

    default:
      return {
        ...DEFAULT_STATE,
        m: calculateMonthlyMultiplier(
          DEFAULT_STATE.annualReturn_r, // Already as percentage
          DEFAULT_STATE.timePeriod_t,
        ),
      };
  }
};

/**
 * Main GBI calculation function.
 */
type CalculateGBIParams = {
  fieldChange: EGbiField;
  value: number;
  state: GBIParams;
};
export const calculateGBI = ({
  fieldChange,
  value,
  state,
}: CalculateGBIParams): GBIResult => {
  const newState = handleFieldChange(fieldChange, value, state);

  const {
    initialInvestment_x = 0,
    annualReturn_r,
    monthlyDeposit_a = 0,
    m,
    timePeriod_t,
  } = newState || {};

  //   g = x(1+r/12)^t + a*m
  const monthlyRate = annualReturn_r / 100 / 12; // Convert percentage to decimal monthly rate
  const gbi =
    initialInvestment_x * (1 + monthlyRate) ** timePeriod_t +
    monthlyDeposit_a * m;

  // Calculate percentage: how much of target we can achieve
  const gbiPercentage =
    newState.targetAmount_g1 === 0 ? 0 : (gbi / newState.targetAmount_g1) * 100;

  const gbiSeries = generateGbiSeries(timePeriod_t, newState);

  return {
    ...state,
    ...newState,
    gbi,
    gbiPercentage,
    gbiSeries,
  };
};
/**
 * Example:
 * Case 1:
 * const params_1 = {
 *  targetAmount: 1_000_000_000,
 *  annualInterestRate: 6.00,
 *  durationInMonths: 12,
 *  monthlyProfit: 12.34
 * }
 * const result_1 = calculateGBI(params_1);
 * console.log(result_1);
 *
 * Case 2:
 * - targetAmount: 1,000,000,000đ
 * - annualInterestRate: 12%
 * - durationInMonths: 12
 * - monthlyProfit: 12.68
 * - initialInvestment: 500,000,000đ
 * - monthlyDeposit: 0đ
 *
 *
 * **/

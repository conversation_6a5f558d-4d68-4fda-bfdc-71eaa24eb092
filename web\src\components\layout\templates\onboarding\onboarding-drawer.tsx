"use client";

import * as React from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "#/src/components/ui/drawer";
import { DesktopLogo, MobileLogo } from "#/src/components/logo";
import { LocalizedLink } from "#/src/components/common/localized-link";
import { useIsMobile } from "#/src/hooks";
import { Button } from "#/src/components/ui";

interface OnBoardingDrawerProps {
  children: React.ReactElement;
}

export function OnBoardingDrawer({ children }: OnBoardingDrawerProps) {
  const isMobile = useIsMobile();

  const handleOpenChange = (open: boolean) => {
    if (open) {
      // Remove focus from any currently focused element when drawer opens
      if (
        typeof document !== "undefined" &&
        document.activeElement &&
        document.activeElement instanceof HTMLElement
      ) {
        document.activeElement.blur();
      }
    }
  };

  return (
    <Drawer direction="right" onOpenChange={handleOpenChange}>
      <DrawerTrigger asChild>{children as any}</DrawerTrigger>
      <DrawerContent
        className="border-none outline-none"
        style={{
          position: "fixed",
          inset: "0",
          left: "auto",
          right: isMobile ? "0" : "16px",
          top: isMobile ? "0" : "16px",
          bottom: isMobile ? "0" : "16px",
          width: isMobile ? "100%" : "438px",
          maxWidth: isMobile ? "100vw" : "",
          maxHeight: isMobile ? "" : "calc(100vh - 32px)",
          zIndex: 999,
          display: "flex",
        }}
      >
        <div className="bg-white h-full w-full flex flex-col rounded-[20px] overflow-hidden">
          {/* Accessibility title - hidden from visual users */}
          <DrawerTitle className="sr-only">Kafi</DrawerTitle>

          {/* Close button */}
          <div className="absolute top-6 right-6 z-10">
            <DrawerClose className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </DrawerClose>
          </div>

          {/* Content */}
          <div className="flex flex-col items-center gap-14 px-8 py-12 h-full overflow-y-auto custom-scrollbar">
            {/* Logo */}
            <div className="logo">
              <LocalizedLink href="/">
                {isMobile ? <MobileLogo /> : <DesktopLogo />}
              </LocalizedLink>
            </div>

            <div className="app_content flex flex-col items-center gap-7">
              {/* App Preview */}
              <div>
                <Image
                  src="/onboarding/kafi-app-preview.png"
                  alt="Kafi App Preview"
                  width={isMobile ? 200 : 270}
                  height={320}
                  className="object-contain h-auto"
                />
              </div>
              {/* Steps */}
              <div className="w-full max-w-sm space-y-8">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    1. Tải Kafi One
                  </h3>
                  {!isMobile && (
                    <p className="text-sm text-gray-600">
                      Quét để tải ứng dụng
                    </p>
                  )}
                </div>

                {/* QR Code */}
                {!isMobile ? (
                  <>
                    <div className="flex justify-center mb-4">
                      <Image
                        src="/onboarding/kafi-app-qr-code.png"
                        alt="QR Code"
                        width={80}
                        height={80}
                        className="object-contain"
                      />
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center">
                    <Button>Đăng ký ngay</Button>
                  </div>
                )}

                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    2. Đăng ký tài khoản
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Đăng ký ngay tài khoản Kafi để có thể tận hưởng các tiện ích
                  </p>
                  <Link
                    href="#"
                    className="text-sm font-medium hover:underline"
                  >
                    Hướng dẫn đăng ký
                  </Link>
                </div>
                {/* Step 3 */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    3. Tham gia các dịch vụ và chương trình của Kafi
                  </h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center justify-center">
                      <div className="w-1 h-1 rounded-full mr-2 bg-[#313941]"></div>
                      <span>Các gói hỗ trợ Margin</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="w-1 h-1 rounded-full mr-2  bg-[#313941]"></div>
                      <span>Kafi Partners</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="w-1 h-1 rounded-full mr-2 bg-[#313941]"></div>
                      <span>Dịch vụ Phái sinh</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}

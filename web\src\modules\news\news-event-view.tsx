import { But<PERSON>, Separator, Typography } from "@components/ui";
import Image from "next/image";
import Link from "next/link";
import React from "react";

// const section_styles = {
//   background: {
//     color: "#F6F7F8",
//     opacity: 1,
//   },
//   padding: {
//     top: 40,
//     bottom: 40,
//   },
// };
const mockNewsData = [
  {
    title: "Cập nhật hệ thống KRX",
    description:
      "Nâng cấp hạ tầng giao dịch theo chuẩn KRX, tăng tốc xử lý lệnh và chuẩn hóa theo thông lệ quốc tế",
    url: "#",
  },
  {
    title: "Kafi x VIB - Ưu đãi khi mở tài khoản trên MyVIB",
    description:
      "Tối ưu trải nghiệm mở tài khoản chứng khoán qua MyVIB, nhân ngày ưu đãi và quyền lợi dành cho nhà đầu tư <PERSON>",
    url: "#",
  },
];
const mockEventNews = {
  title: "<PERSON><PERSON>h nghiệp tăng trưởng nhanh Châu Á 2024",
  description:
    "Tháng 10 năm 2024, Kafi tự hào được vinh danh là một trong những doanh nghiệp có tốc độ tăng trưởng nhanh nhất tại giải thưởng Asia Pacific Enterprise Awards (APEA).",
  image: "/images/home/<USER>",
  url: "#",
};
const NewsEventView = () => {
  return (
    <div className="container">
      <div className="flex flex-col gap-6 lg:gap-10 max-lg:py-[30px] max-lg:border-t max-lg:border-[#E2E2E2]">
        <Typography
          variant="large-title"
          classname="max-md:text-2xl max-lg:text-3xl"
        >
          Tin tức & sự kiện
        </Typography>
        <div className="grid grid-cols-1 gap-6 lg:gap-5 lg:grid-cols-2">
          <div className="flex flex-col gap-5">
            {mockNewsData.map((news, index) => (
              <React.Fragment key={index}>
                <Link key={index} href={news.url}>
                  <div className="flex flex-col gap-2">
                    <Typography variant={"title-3"}>{news.title}</Typography>
                    <Typography>{news.description}</Typography>
                  </div>
                </Link>
                <Separator />
              </React.Fragment>
            ))}
            <Link href={"#"}>
              <Button
                variant="text"
                className="text-black hover:text-brand px-0"
              >
                Tìm hiểu thêm
              </Button>
            </Link>
          </div>
          <div className="bg-[#F1F3F5] p-8 w-full flex flex-col gap-2.5 md:gap-0 md:grid grid-cols-10 lg:grid-cols-2 rounded-2xl">
            <div className="flex flex-col gap-7 md:col-span-8 lg:col-span-1 order-last md:order-first">
              <Link href={mockEventNews.url}>
                <div className="flex flex-col gap-2">
                  <Typography variant="title-3">
                    {mockEventNews.title}
                  </Typography>
                  <Typography>{mockEventNews.description}</Typography>
                </div>
              </Link>
              <Link href={"#"}>
                <Button
                  variant="text"
                  className="text-black hover:text-brand px-0"
                >
                  Xem tất cả
                </Button>
              </Link>
            </div>
            <div className="w-full h-fit md:col-span-2 lg:col-span-1 order-first md:order-last">
              <Image
                src={mockEventNews.image}
                alt="achievement image"
                width={0}
                height={0}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="w-[149px] h-auto max-h-[221px] aspect-[3/4] object-contain justify-self-center md:justify-self-end"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewsEventView;

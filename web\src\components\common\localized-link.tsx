"use client";

import { cn } from "@/lib/utils";
import { createLocalizedPath, useLocale } from "@/lib/locale-navigation";
import Link from "next/link";

interface LocalizedLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}

/**
 * A Link component that automatically handles locale prefixing
 * @param href - The path to link to (without locale)
 * @param children - React children
 * @param className - CSS classes
 * @param props - Additional props to pass to Link
 */
export function LocalizedLink({
  href,
  children,
  className,
  ...props
}: LocalizedLinkProps) {
  const locale = useLocale();
  const localizedHref = createLocalizedPath(href, locale);

  return (
    <Link
      href={localizedHref}
      className={cn("cursor-pointer", className)}
      {...props}
    >
      {children}
    </Link>
  );
}

import { PROMOTIONS_DATA } from "#/src/__mock__/promotions";
import PromotionCard from "#/src/components/card/promotion-card";
import { Typography } from "#/src/components/ui";
import { useEffect, useState } from "react";
//--------------------------------------------------------------------------
type OtherPromotionsProps = {
  current_slug: string;
};
//--------------------------------------------------------------------------
export default function OtherPromotions({
  current_slug,
}: OtherPromotionsProps) {
  const [otherPromotions, setOtherPromotions] = useState<any[]>([]);

  useEffect(() => {
    const dataFilter = PROMOTIONS_DATA.filter(
      (item: any) => item.slug !== current_slug,
    )?.slice(0, 3);
    setOtherPromotions(dataFilter);
  }, [current_slug]);

  return (
    <div className="flex flex-col gap-6">
      <Typography variant="large-title" classname="text-emphasize">
        Các ưu đãi khác
      </Typography>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {otherPromotions?.map((item: any, idx: number) => (
          <PromotionCard key={idx} promotion={item} />
        ))}
      </div>
    </div>
  );
}

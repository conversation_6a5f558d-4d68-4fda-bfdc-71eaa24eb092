import { Typography } from "#/src/components/ui";
import Image from "next/image";

type CardProps = {
  item: any;
};

export default function IconBoxCardCustom({ item }: CardProps) {
  const renderIcon = (
    <div className="w-full flex">
      <Image
        src={item?.icon}
        alt="icon"
        width={0}
        height={0}
        sizes="100vw"
        className="w-15 aspect-square h-auto"
      />
    </div>
  );

  return (
    <div className="h-full p-6 lg:p-10 rounded-2xl bg-white flex flex-col gap-6">
      {renderIcon}
      <Typography variant={"title-2"} classname="text-emphasize max-lg:text-xl">
        {item?.title}
      </Typography>
      <Typography variant="body-regular" classname="text-emphasize">
        {item?.sub_title}
      </Typography>
    </div>
  );
}

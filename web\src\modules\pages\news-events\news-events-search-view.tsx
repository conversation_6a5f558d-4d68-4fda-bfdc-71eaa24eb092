"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { InputSearch } from "@/components/ui/input";
import { _newsEvents } from "@/__mock__";
import { Button, Typography } from "#/src/components/ui";
import NewsEventsSearchPagination from "../../news/news-events-search-pagination";
import { ArrowLeft } from "lucide-react";

export default function NewsEventsSearchView() {
  const [searchValue, setSearchValue] = useState("");
  const [_currentPage, _setCurrentPagee] = useState(1);
  const _pageSize = 5;

  const searchParams = useSearchParams();
  const search = searchParams.get("key");
  const router = useRouter();

  useEffect(() => {
    setSearchValue(search || "");
    // Reset về trang 1 khi search mới
    _setCurrentPagee(1);
  }, [search]);

  const _handlePageChange = (page: number) => {
    _setCurrentPagee(page);
  };

  const handleSearch = () => {
    router.push(`/news-events/search?key=${searchValue}`);
  };

  // Filter data based on search keyword
  const _filteredData = React.useMemo(() => {
    if (!search || !search.trim()) {
      return _newsEvents;
    }

    const keyword = search.toLowerCase().trim();
    const keywords = keyword.split(" ").filter((k) => k.length > 0);

    return _newsEvents.filter((item) => {
      const title = item.title.toLowerCase();
      const description = item.description.toLowerCase();
      const category = item.category.name.toLowerCase();

      // Check if all keywords exist in title, description, or category
      return keywords.every(
        (k) =>
          title.includes(k) || description.includes(k) || category.includes(k),
      );
    });
  }, [search]);

  // Tính toán data cho trang hiện tại
  const _paginatedData = React.useMemo(() => {
    const startIndex = (_currentPage - 1) * _pageSize;
    const endIndex = startIndex + _pageSize;
    return _filteredData.slice(startIndex, endIndex);
  }, [_filteredData, _currentPage]);

  const renderHead = (
    <div className="flex flex-col justify-center items-center gap-4">
      <Typography variant="special-title">Tin tức & sự kiện</Typography>

      <InputSearch
        placeholder="Tìm kiếm"
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onSearch={handleSearch}
        className="w-lg h-14 bg-white max-w-lg block mx-auto"
      />

      {_filteredData.length > 0 && (
        <Typography className="text-default">
          Có <strong>{_filteredData.length}</strong> kết quả tìm kiếm
        </Typography>
      )}
    </div>
  );

  const renderListPosts = (
    <div className="w-full">
      {_filteredData.length > 0 ? (
        <NewsEventsSearchPagination
          data={_paginatedData}
          pagination={{
            page: _currentPage,
            pageSize: _pageSize,
            total: _filteredData.length,
          }}
          searchKeyword={search || ""}
          onPageChange={_handlePageChange}
        />
      ) : search ? (
        <div className="text-center py-10">
          <Typography className="text-placeholder">
            Không tìm thấy kết quả nào cho từ khóa &quot;
            <strong className="text-brand">{search}</strong>&quot;
          </Typography>
          <Typography className="text-placeholder mt-2">
            Hãy thử tìm kiếm với từ khóa khác
          </Typography>
        </div>
      ) : null}
    </div>
  );

  return (
    <div className="container flex flex-col gap-10">
      <Button
        variant="text"
        className="!px-0 w-fit"
        onClick={() => router.back()}
      >
        <ArrowLeft />
        Quay lại
      </Button>

      {renderHead}

      <div className="container-inner-sm">{renderListPosts}</div>
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

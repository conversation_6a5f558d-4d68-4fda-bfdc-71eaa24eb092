"use client";
import { Typography } from "@/components/ui";
import { useMediaQuery } from "@/hooks/use-media-query";
import Image from "next/image";

export default function KPointRatio() {
  const isTablet = useMediaQuery("(max-width: 1023px)");
  return (
    <div className="container flex flex-col gap-10">
      <div className="flex flex-col gap-4">
        <Typography
          variant="large-title"
          className="text-black text-center max-md:text-2xl max-lg:text-3xl"
        >
          Tỷ lệ chi trả K-Points
        </Typography>
        <Typography variant={"body-medium"} className="text-black text-center">
          Chia sẻ mã giới thiệu hoặc mã chăm sóc – mở ra cơ hội thu nhập không
          giới hạn
        </Typography>
      </div>
      <div className="w-full flex flex-col gap-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 max-w-[1066px] mx-auto gap-8">
          <div className="flex flex-col lg:col-span-7 gap-8 lg:gap-11 pt-6 px-6 lg:pt-12 lg:px-8 bg-white relative rounded-2xl overflow-hidden">
            <div
              className="absolute bottom-0 w-full left-0 h-[110px]"
              style={{
                background:
                  "linear-gradient(180deg, rgba(255, 255, 255, 0.00) 18.83%, #FFF 95%)",
              }}
            ></div>
            <div className="flex flex-col gap-4 ">
              <Typography
                variant={"title-2"}
                className="text-emphasize text-center font-semibold"
              >
                Quản lý K-Points trực quan
              </Typography>
              <Typography variant="body-regular" classname="text-center">
                Theo dõi K-point theo thời gian thực dễ dàng cập nhật biến động
                mỗi ngày và xem chi tiết hoạt động để tối ưu chiến lược một cách
                linh hoạt và thông minh
              </Typography>
            </div>
            <Image
              src={
                isTablet
                  ? "/images/partner/kpoint-managment-banner-mobile.png"
                  : "/images/partner/kpoint-managment-banner.png"
              }
              alt="kpoint management"
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-auto object-contain"
            />
          </div>
          <div className="h-full grid grid-rows-2 lg:col-span-5 gap-8 ">
            <div className="h-full bg-white rounded-2xl  flex flex-col items-center justify-center gap-6 py-6 md:py-0">
              <Image
                src={"/icons/partner/k-users-plus.png"}
                alt="user plus"
                width={0}
                height={0}
                sizes="100vw"
                className="w-[60px] aspect-square h-auto"
              />
              <div className="w-full flex flex-col gap-2 text-center">
                <Typography
                  variant={"title-3"}
                  className="text-emphasize font-semibold"
                >
                  Giới thiệu <br /> khách hàng/ đối tác (NFI*)
                </Typography>
                <Typography variant={"special-title"} className="text-brand">
                  6%
                </Typography>
              </div>
            </div>
            <div className="h-full bg-white rounded-2xl flex flex-col items-center justify-center gap-6 py-6 md:py-0">
              <Image
                src={"/icons/partner/k-user-heart.png"}
                alt="user heart"
                width={0}
                height={0}
                sizes="100vw"
                className="w-[60px] aspect-square h-auto"
              />
              <div className="w-full flex flex-col gap-2 text-center">
                <Typography
                  variant={"title-3"}
                  className="text-emphasize font-semibold"
                >
                  Chăm sóc <br /> khách hàng/ đối tác (NFI*)
                </Typography>
                <Typography variant={"special-title"} className="text-brand">
                  60%
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <div className="max-w-[800px] mx-auto md:px-5 lg:px-0">
          <Typography
            variant={"small-body-regular"}
            className="text-emphasize text-center"
          >
            *NFI (Net Fee Income): là giá trị phí giao dịch chứng khoán của
            khách hàng trên thị trường chứng khoán cơ sở sau khi trừ phí trả các
            Sở Giao dịch Chứng khoán và các khoản giảm trừ kinh doanh khác do
            KAFI quy định từng thời kỳ.
          </Typography>
        </div>
      </div>
    </div>
  );
}

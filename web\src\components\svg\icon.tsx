import { cn } from "#/src/lib";

type TProps = {
  children: React.ReactNode;
  size?: string | number;
  color?: string;
  hoverColor?: string;
  activeColor?: string;
  className?: string;
};

const SvgIcon = ({
  children,
  size = 24,
  color = "text-emphasize",
  hoverColor = "text-brand",
  activeColor = "text-brand",
  className,
}: TProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox={`0 0 ${size} ${size}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "cursor-pointer transition-all duration-300",
        color,
        `hover:${hoverColor}`,
        `focus:${activeColor}`,
        className,
      )}
    >
      {children}
    </svg>
  );
};

export default SvgIcon;

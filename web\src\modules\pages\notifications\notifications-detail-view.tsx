import { _newsEvents } from "#/src/__mock__";
import { FacebookIcon, ShareLinkIcon, LinkedinIcon } from "#/src/components";
import { Button, Separator, Typography } from "#/src/components/ui";
import Link from "next/link";
import NewsEventsCard from "../../news/news-events-card";

export default function NotificationsDetailView() {
  const renderDetail = (
    <div className="container-inner-sm flex flex-col gap-8 mx-auto">
      <Typography variant="special-title">
        Kafi và VIB ký kết biên bản ghi nhớ hợp tác chiến lư<PERSON>
      </Typography>

      <div className="flex gap-4 justify-between">
        <div className="flex gap-4 items-center">
          <Typography variant="subheadline-medium">20/06/2024</Typography>
          <Separator orientation="vertical" className="text-placeholder" />
          <Typography variant="subheadline-medium">
            220 <span className="text-placeholder">Đã xem</span>
          </Typography>
        </div>

        <div className="flex gap-4">
          <Link href="#!">
            <FacebookIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
          <Link href="#!">
            <LinkedinIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
          <Link href="#!">
            <ShareLinkIcon className="text-icon-placeholder transition-colors hover:text-brand" />
          </Link>
        </div>
      </div>

      <Separator />

      <div className="dynamic-content space-y-10">
        <Typography variant="body-medium">
          Nhằm hỗ trợ nhà đầu tư tối ưu hiệu quả sinh lời và xóa bỏ những rào
          cản tài chính, Công ty Cổ phần Chứng khoán KAFI (&quot;Kafi&quot;)
          mang đến cho Quý khách chương trình ưu đãi chưa từng có – mở ra hành
          trình đầu tư thông minh, linh hoạt và không giới hạn!
        </Typography>
        <div className="space-y-6">
          {/* Đối tượng */}
          <div className="flex flex-col md:flex-row md:gap-6 gap-2">
            <div className="md:w-[200px] flex-shrink-0">
              <Typography variant="title-3" className="font-semibold">
                Đối tượng
              </Typography>
            </div>
            <div className="flex-1">
              <Typography variant="body-medium" className="leading-relaxed">
                Khách hàng trong nước chưa từng mở tài khoản giao dịch chứng
                khoán tại Kafi thực hiện mở tài khoản chứng khoán thông qua hệ
                thống giao dịch điện tử trong thời gian triển khai.
              </Typography>
            </div>
          </div>

          {/* Miễn phí giao dịch */}
          <div className="flex flex-col md:flex-row md:gap-6 gap-2">
            <div className="md:w-[200px] flex-shrink-0">
              <Typography variant="title-3" className="font-semibold">
                Miễn phí giao dịch
              </Typography>
            </div>
            <div className="flex-1">
              <Typography variant="body-medium" className="leading-relaxed">
                Miễn phí giao dịch mua bán chứng khoán (không bao gồm phí giao
                dịch bắt buộc do Sở giao dịch chứng khoán quy định) - mức phí
                này sẽ có thể thay đổi tại từng thời điểm trong vòng 30 ngày kể
                từ ngày mở tài khoản.
              </Typography>
            </div>
          </div>

          {/* Gói vay Margin - M65 */}
          <div className="flex flex-col md:flex-row md:gap-6 gap-2">
            <div className="md:w-[200px] flex-shrink-0">
              <Typography variant="title-3" className="font-semibold">
                Gói vay Margin - M65
              </Typography>
            </div>
            <div className="flex-1">
              <ul className="space-y-3 list-disc pl-6">
                <li>
                  <Typography variant="body-medium">
                    Áp dụng lãi vay 6.5%/năm trong vòng 90 ngày kể từ ngày mở
                    tài khoản, dành cho dư nợ lên đến 5 tỷ VND.
                  </Typography>
                </li>
                <li>
                  <Typography variant="body-medium">
                    Khách hàng cần đăng ký sử dụng tiện khoản ký quỹ bằng hình
                    thức trực tuyến trước khi có thể sử dụng gói Margin-M65.
                  </Typography>
                </li>
                <li>
                  <Typography variant="body-medium">
                    Sản phẩm Margin-M65 không áp dụng đồng thời với các sản phẩm
                    margin khác.
                  </Typography>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Chi tiết ưu đãi */}
        <Typography variant="body-medium">
          Chi tiết ưu đãi xem tại{" "}
          <Link
            href="https://vnexpress.net/kafi-hop-tac-vib-xay-dung-he-sinh-thai-tai-chinh-so-4870072.html"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:no-underline hover:text-brand"
          >
            đây
          </Link>
        </Typography>

        {/* Call to Action */}
        <div className="max-w-2xl space-y-4">
          <Typography variant="body-medium">
            Thời gian áp dụng ưu đãi có giới hạn. Hãy chủ động nắm bắt cơ hội và
            đăng ký tài khoản để lợi thế giao dịch!
          </Typography>

          <Button>Mở tài khoản</Button>
        </div>

        {/* Link bài viết */}
        <div className="flex flex-col gap-2">
          <Typography variant="title-3">Link bài viết:</Typography>
          <div className="flex gap-2">
            <Link
              href="https://vnexpress.net/kafi-hop-tac-vib-xay-dung-he-sinh-thai-tai-chinh-so-4870072.html"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:no-underline"
            >
              <Button
                variant="link"
                className="!text-brand p-0 hover:no-underline"
              >
                VNExpress
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );

  const renderRelated = (
    <div className="flex flex-col gap-6">
      <Typography variant="large-title">Thông báo khác</Typography>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {_newsEvents.slice(0, 3).map((item) => (
          <NewsEventsCard
            key={item.id}
            item={item}
            type="text-only"
            classname="bg-white"
            hideDescription
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className="container flex flex-col gap-20 py-10">
      {/* Main Content */}
      <div className="space-y-8">
        <div className="flex items-center">
          <Link href="/news-events">
            <Button variant="link" className="px-3 !font-normal">
              Tin tức & Sự kiện
            </Button>
          </Link>
          /
          <Link href="/news-events/notifications">
            <Button variant="link" className="px-3 !font-normal">
              Thông báo
            </Button>
          </Link>
        </div>

        {renderDetail}
      </div>

      {renderRelated}
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

import Cookies from "js-cookie";
const LOCALE_KEY = "NEXT_LOCALE";
const DEFAULT_LOCALE = "en";

export const getLocaleClient = () => {
  return Cookies.get(LOCALE_KEY) || DEFAULT_LOCALE;
};

export const setLocaleClient = (locale: string) => {
  Cookies.set(LOCALE_KEY, locale, {
    expires: 365,
  });
};

export const getCookieClient = (key: string) => {
  return Cookies.get(key);
};

export const setCookieClient = (key: string, value: string) => {
  Cookies.set(key, value, { expires: 365 });
};

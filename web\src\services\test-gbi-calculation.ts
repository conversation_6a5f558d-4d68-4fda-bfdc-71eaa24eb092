/**
 * Test file to verify GBI calculations match the formula
 * 
 * <PERSON><PERSON><PERSON> thức GBI:
 * 1. Số tiền đầu tư ban đầu: z * g
 * 2. <PERSON><PERSON><PERSON> nhuận tích lũy tháng: m = ((1 + r/12)^t - 1) / (r/12)
 * 3. <PERSON><PERSON><PERSON> tiêu: g = x(1 + r/12)^t + a * m
 * 4. Số tiền nộp hàng tháng: a = (g - x(1 + r/12)^t) / m
 * 
 * Tỷ lệ phần bổ đầu tư ban đầu: z(%)
 * - z = 30% nếu g < 500M
 * - z = 40% nếu 500M <= g < 1B  
 * - z = 50% nếu g >= 1B
 */

import { calculateGBI, getInitialInvestmentRatio_Z } from './calculate-gbi';
import { EGbiField } from '@/modules/gbi/configs';

// Test case 1: Basic calculation
export function testBasicGBICalculation() {
  console.log('=== Test Case 1: Basic GBI Calculation ===');
  
  const targetAmount = 1_000_000_000; // 1 tỷ
  const annualReturn = 12; // 12%
  const timePeriod = 12; // 12 tháng
  
  // Expected calculations:
  const z = getInitialInvestmentRatio_Z(targetAmount); // Should be 50% for 1B
  const expectedX = targetAmount * z; // 500M
  
  console.log(`Target Amount (g): ${targetAmount.toLocaleString()}`);
  console.log(`Annual Return (r): ${annualReturn}%`);
  console.log(`Time Period (t): ${timePeriod} months`);
  console.log(`Z Ratio: ${z * 100}%`);
  console.log(`Expected Initial Investment (x): ${expectedX.toLocaleString()}`);
  
  // Calculate using our function
  const result = calculateGBI({
    fieldChange: EGbiField.TargetAmount_g1,
    value: targetAmount,
    state: {
      targetAmount_g1: 0,
      annualReturn_r: annualReturn,
      timePeriod_t: timePeriod,
      initialInvestment_x: 0,
      monthlyDeposit_a: 0,
    }
  });
  
  console.log('\n--- Results ---');
  console.log(`Calculated Initial Investment (x): ${result.initialInvestment_x?.toLocaleString()}`);
  console.log(`Calculated Monthly Deposit (a): ${result.monthlyDeposit_a?.toLocaleString()}`);
  console.log(`Final GBI Value (g): ${result.gbi.toLocaleString()}`);
  console.log(`GBI Percentage: ${result.gbiPercentage.toFixed(2)}%`);
  
  // Manual verification
  const monthlyRate = (annualReturn / 100) / 12;
  const m = ((1 + monthlyRate) ** timePeriod - 1) / monthlyRate;
  const x = expectedX;
  const a = (targetAmount - x * (1 + monthlyRate) ** timePeriod) / m;
  const g = x * (1 + monthlyRate) ** timePeriod + a * m;
  
  console.log('\n--- Manual Verification ---');
  console.log(`Monthly Rate: ${(monthlyRate * 100).toFixed(4)}%`);
  console.log(`Monthly Multiplier (m): ${m.toFixed(2)}`);
  console.log(`Manual Initial Investment (x): ${x.toLocaleString()}`);
  console.log(`Manual Monthly Deposit (a): ${a.toLocaleString()}`);
  console.log(`Manual Final Value (g): ${g.toLocaleString()}`);
  
  // Check if calculations match
  const xMatch = Math.abs((result.initialInvestment_x || 0) - x) < 1;
  const aMatch = Math.abs((result.monthlyDeposit_a || 0) - a) < 1;
  const gMatch = Math.abs(result.gbi - g) < 1;
  
  console.log('\n--- Verification ---');
  console.log(`X Match: ${xMatch ? '✅' : '❌'}`);
  console.log(`A Match: ${aMatch ? '✅' : '❌'}`);
  console.log(`G Match: ${gMatch ? '✅' : '❌'}`);
  
  return { xMatch, aMatch, gMatch };
}

// Test case 2: Different target amounts (test Z ratios)
export function testZRatios() {
  console.log('\n=== Test Case 2: Z Ratios ===');
  
  const testCases = [
    { amount: 300_000_000, expectedZ: 0.3 }, // < 500M
    { amount: 700_000_000, expectedZ: 0.4 }, // 500M <= x < 1B
    { amount: 1_500_000_000, expectedZ: 0.5 }, // >= 1B
  ];
  
  testCases.forEach(({ amount, expectedZ }) => {
    const actualZ = getInitialInvestmentRatio_Z(amount);
    const match = actualZ === expectedZ;
    console.log(`Amount: ${amount.toLocaleString()} -> Z: ${actualZ * 100}% (Expected: ${expectedZ * 100}%) ${match ? '✅' : '❌'}`);
  });
}

// Run tests
if (typeof window === 'undefined') {
  // Only run in Node.js environment
  testBasicGBICalculation();
  testZRatios();
}

# Kafi.vn Website

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## 📦 Project Structure

```
root/
├── src/
├────── app/                                  # pages
├────── components/                           # components
├────── hooks/                                # hooks
├────── i18n/                                 # internationalization (lingui)
├────── lib/                                  # library
├────── locales/                              # locales (translation)
├────── modules/                              # modules
├────── providers/                            # providers (client providers)
├────── store/                                # store (zustand)
├────── types/                                # types
├────── utils/                                # utils
├────── config-global.ts                      # global variables
├────── middleware.ts                         # middleware
├── public/
├── .env
└── ...
```

## Dependencies

- [Next.js](https://nextjs.org/)
- [React](https://react.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [Lingui](https://lingui.dev/)
- [Shadcn UI](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Lucide](https://lucide.dev/)
- [GSAP](https://greensock.com/)
- [Framer Motion](https://www.framer.com/motion/)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

{"kind": "collectionType", "collectionName": "team_groups", "info": {"singularName": "team-group", "pluralName": "team-groups", "displayName": "Team - Group"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "team_members": {"type": "relation", "relation": "oneToMany", "target": "api::team-member.team-member", "mappedBy": "team_group"}, "order_number": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}}}
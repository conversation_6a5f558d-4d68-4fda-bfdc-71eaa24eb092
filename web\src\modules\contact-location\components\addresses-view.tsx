"use client";
import MapPinIcon from "#/src/components/svg/ic-map-pin";
import { Separator, Typography } from "#/src/components/ui";
import { cn } from "#/src/lib";
import { useState } from "react";

interface AddressProp {
  name: string;
  address: string;
  src: string;
}

interface AddressesListProp {
  addressesList: AddressProp[];
}

export default function AddressesView({ addressesList }: AddressesListProp) {
  const [selectedAddress, setSelectedAddress] = useState<AddressProp>(
    addressesList[0],
  );
  return (
    <div className="container !py-0 mt-12 mb-20 flex flex-col gap-10">
      <Typography
        variant="large-title"
        classname="text-emphasize max-lg:text-3xl max-md:text-2xl max-md:font-semibold"
      >
        Địa chỉ
      </Typography>
      <div className="flex lg:grid grid-cols-[420px_1fr] flex-col-reverse gap-8 h-fit">
        <div className="relative">
          <div
            className={cn(
              "flex flex-col gap-6 overflow-y-auto h-full w-full lg:max-h-[496px]  hide-scrollbar lg:pb-[100px]",
            )}
          >
            {addressesList.map((address, index) => {
              return (
                <AddressItem
                  key={index}
                  address={address}
                  isSelected={selectedAddress.src === address.src}
                  onClick={() => setSelectedAddress(address)}
                  isLast={index === addressesList.length - 1}
                />
              );
            })}
          </div>
          {/**overlay */}
          <div
            className="w-full h-[100px] absolute bottom-0 left-0 hidden lg:flex"
            style={{
              background:
                "linear-gradient(180deg, rgba(246, 247, 248, 0.00) 0%, #F6F7F8 100%)",
            }}
          ></div>
        </div>
        <iframe
          title="map"
          src={selectedAddress.src}
          allowFullScreen={true}
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          className="w-full h-full lg:max-h-[496px] aspect-[327/257] lg:aspect-[207/124] rounded-4xl"
        ></iframe>
      </div>
    </div>
  );
}

interface AddressItemProps {
  address: AddressProp;
  isSelected: boolean;
  onClick: () => void;
  isLast: boolean;
}

const AddressItem = ({
  address,
  isSelected,
  onClick,
  isLast,
}: AddressItemProps) => {
  return (
    <div className="flex flex-col gap-6 cursor-pointer" onClick={onClick}>
      <div className="flex flex-col gap-4">
        <div className="w-full flex justify-between py-3">
          <Typography variant="title-3">{address.name}</Typography>
          <MapPinIcon size={24} isMatched={isSelected} />
        </div>
        <Typography variant="body-regular">{address.address}</Typography>
      </div>
      {!isLast && (
        <Separator
          orientation="horizontal"
          className={cn("h-[1px]", isSelected ? "bg-brand" : "bg-subtle")}
        />
      )}
    </div>
  );
};

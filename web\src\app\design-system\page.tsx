import { Button, Typography } from "@components/ui";
import { HomeIcon } from "lucide-react";

export default function Home() {
  const renderTypography = (
    <div className="flex flex-row flex-wrap gap-8 bg-gray-50 p-4 rounded-sm shadow-sm">
      <div className="flex flex-col gap-4">
        <Typography variant="special-title">Special Title</Typography>
        <Typography variant="large-title">Large Title</Typography>
        <Typography variant="title-0">Title 0</Typography>
        <Typography variant="title-1">Title 1</Typography>
        <Typography variant="title-2">Title 2</Typography>
        <Typography variant="title-3">Title 3</Typography>
        <Typography variant="lead">Lead</Typography>
        <Typography variant="large">Large</Typography>
        <Typography variant="body">Body</Typography>
        <Typography variant="small">Small</Typography>
        <Typography variant="muted">Muted</Typography>
        <Typography variant="button">Button</Typography>
        <Typography variant="special-label">Special Label</Typography>
      </div>

      <div className="flex flex-col gap-4">
        <p className="text-primary">Primary</p>
        <p className="text-default">Default</p>
        <p className="text-emphasize">Emphasize</p>
        <p className="text-placeholder">Placeholder</p>
        <p className="text-disabled">Disabled</p>
        <p className="text-brand">Brand</p>
        <p className="text-on-color">On Color</p>
        <p className="text-on-white">On White</p>
        <p className="text-error">Error</p>
        <p className="text-warning">Warning</p>
        <p className="text-success">Success</p>
      </div>

      {/* TEXT ICON */}
      <div className="flex flex-col gap-4">
        <p className="text-icon-default">Icon default</p>
        <p className="text-icon-emphasize">Icon emphasize</p>
        <p className="text-icon-placeholder">Icon placeholder</p>
        <p className="text-icon-disabled">Icon disabled</p>
        <p className="text-icon-brand">Icon brand</p>
        <p className="text-icon-brand-light-subtle">Icon brand light subtle</p>
        <p className="text-icon-on-color">Icon on color</p>
        <p className="text-icon-on-white">Icon on white</p>
        <p className="text-icon-error">Icon error</p>
        <p className="text-icon-warning">Icon warning</p>
        <p className="text-icon-success">Icon success</p>
      </div>
    </div>
  );

  const renderBoxBorder = (
    <div className="flex gap-4 flex-wrap bg-gray-50 p-4 rounded-sm shadow-sm">
      <div className="border border-default w-40 h-40 rounded-sm">
        Border default
      </div>
      <div className="border border-subtle w-40 h-40 rounded-sm">
        Border subtle
      </div>
      <div className="border border-disabled w-40 h-40 rounded-sm">
        Border disabled
      </div>
      <div className="border border-brand w-40 h-40 rounded-sm">
        Border brand
      </div>
    </div>
  );

  const renderBackgrounds = (
    <div className="flex gap-4 flex-wrap bg-gray-50 p-4 rounded-sm shadow-sm">
      <div className="bg-canvas w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Canvas
      </div>
      <div className="bg-screen w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Screen
      </div>
      <div className="bg-subtle w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Subtle
      </div>
      <div className="bg-brand w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Brand
      </div>
      <div className="bg-error w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Error
      </div>
      <div className="bg-warning w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Warning
      </div>
      <div className="bg-success w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Success
      </div>
      <div className="bg-gradient-left w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient left
      </div>
      <div className="bg-gradient-right w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient right
      </div>
      <div className="bg-gradient-top w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient top
      </div>
      <div className="bg-gradient-bottom w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient bottom
      </div>
      <div className="bg-gradient-primary w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient primary
      </div>
      <div className="bg-gradient-dark w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient dark
      </div>
      <div className="bg-gradient-light w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient light
      </div>
      <div className="bg-gradient-lighter w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient lighter
      </div>
      <div className="bg-gradient-dark-left text-white w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient dark left
      </div>
      <div className="bg-gradient-dark-right text-white  w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient dark right
      </div>
      <div className="bg-gradient-dark-top text-white w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient dark top
      </div>
      <div className="bg-gradient-dark-bottom text-white w-40 h-40 rounded-sm border border-amber-500 flex items-center justify-center">
        Gradient dark bottom
      </div>
    </div>
  );

  const renderButtons = (
    <div className="flex gap-4">
      <Button>Default</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
      <Button variant="text">Text</Button>
      <Button variant="destructive">Destructive</Button>
      <Button size="icon">
        <HomeIcon className="size-6" />
      </Button>
      <Button variant="text" size="icon">
        <HomeIcon className="size-6" />
      </Button>
    </div>
  );

  const renderCards = <div className="flex gap-4">Card</div>;

  return (
    <div className="min-h-screen container flex flex-col gap-12">
      <div className="container-inner">{renderTypography}</div>

      {renderBoxBorder}

      {renderBackgrounds}

      {renderButtons}

      {renderCards}
    </div>
  );
}

export const dynamic = "force-dynamic";

"use client";

import { ARCHIVED_POLICY } from "#/src/__mock__/terms-and-condition";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Separator,
  Typography,
} from "#/src/components/ui";
import { useMediaQuery } from "#/src/hooks";
import { cn } from "#/src/lib";
import { XIcon } from "lucide-react";
import Image from "next/image";
interface ArchiveDialogProp {
  open: boolean;
  setOpen: (param?: any) => void;
}

export default function ArchiveDialog({ open, setOpen }: ArchiveDialogProp) {
  const isMobile = useMediaQuery("(max-width: 743px)");
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="focus-visible:outline-none focus:outline-none focus:ring-0 focus:ring-offset-0 w-full h-screen md:h-fit rounded-none md:rounded-lg bg-white md:bg-transparent md:max-w-[700px] lg:max-w-[864px] md:grid md:grid-cols-[638px_1fr] lg:grid-cols-[790px_1fr] md:max-h-[451px] border-0 !p-0 shadow-none !border-transparent"
        closeButtonClassName="[&_svg:not([class*='size-'])]:size-6 text-black top-7"
        showCloseButton={isMobile ? true : false}
      >
        <div className="bg-white rounded-[20px] flex flex-col gap-4 py-6 md:py-8 px-6 md:px-8 lg:px-12 ">
          <DialogHeader>
            <DialogTitle className="text-2xl font-semibold">
              Đã ban hành
            </DialogTitle>
          </DialogHeader>
          <div
            className={cn(
              "flex flex-col gap-4 md:gap-6 overflow-y-auto hide-scrollbar max-h-[calc(100vh-100px)] md:max-h-[338px]",
            )}
          >
            {ARCHIVED_POLICY.map((policy, index) => (
              <div
                key={index}
                className="flex flex-col gap-4 md:gap-6 first:pt-6"
              >
                <div className="w-full hidden md:flex  justify-between items-center">
                  <div className="flex gap-4">
                    <Image
                      src={"/icons/about/attachment.png"}
                      alt="attachment"
                      width={0}
                      height={0}
                      sizes="100vws"
                      className="min-w-6 h-auto aspect-square"
                    />
                    <Typography classname="text-default text-base font-medium leading-[22px]">
                      {policy.name}
                    </Typography>
                  </div>
                  <Typography classname="text-placeholder text-base font-medium leading-[22px]">
                    {policy.date}
                  </Typography>
                </div>
                <div className="flex md:hidden gap-4">
                  <div className="h-full">
                    <Image
                      src={"/icons/about/attachment.png"}
                      alt="attachment"
                      width={0}
                      height={0}
                      sizes="100vws"
                      className="min-w-6 h-auto aspect-square"
                    />
                  </div>
                  <div className="flex flex-col gap-4">
                    <Typography classname="text-default text-base font-medium leading-[22px]">
                      {policy.name}
                    </Typography>

                    <Typography classname="text-placeholder text-base font-medium leading-[22px]">
                      {policy.date}
                    </Typography>
                  </div>
                </div>
                {index !== ARCHIVED_POLICY.length - 1 && (
                  <Separator
                    orientation="horizontal"
                    className="h-[1px] bg-subtle"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        <div
          className="w-10 h-auto aspect-square rounded-full hidden bg-white md:flex justify-center items-center cursor-pointer"
          onClick={() => setOpen(false)}
        >
          <XIcon className="text-black size-6" strokeWidth={2} />
        </div>
      </DialogContent>
    </Dialog>
  );
}

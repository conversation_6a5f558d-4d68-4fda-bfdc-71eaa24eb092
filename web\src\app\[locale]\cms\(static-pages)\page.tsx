import { Metadata } from "next";
import { apiRoute } from "@/lib/constants/cms";
import { generateMetadataObject, fetchContentType } from "@/lib/data";
import { PageContent, ClientSlugHandler } from "#/src/components/common";

type TProps = {
  params: Promise<{
    locale: string;
  }>;
};

export async function generateMetadata({ params }: TProps): Promise<Metadata> {
  const { locale } = await params;

  const pageData = await fetchContentType({
    contentType: apiRoute.pageHome,
    params: {
      filters: {
        locale: locale,
      },
      populate: "seo.metaImage",
    },
    spreadData: true,
  });

  const seo = pageData?.seo || {};
  const metadata = generateMetadataObject(seo, pageData?.title);
  return metadata;
}

export default async function HomePage({ params }: TProps) {
  const { locale } = await params;
  const pageData = await fetchContentType({
    contentType: apiRoute.pageHome,
    params: {
      filters: { locale: locale },
    },
    spreadData: true,
  });

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = "";
      return acc;
    },
    { [locale]: "" },
  );

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageContent pageData={pageData} />
    </>
  );
}

export const dynamic = "force-dynamic";

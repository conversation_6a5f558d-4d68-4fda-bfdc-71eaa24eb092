"use client";

import * as React from "react";

import { cn } from "@/lib/utils";
import { IcSearchSm } from "../svg";
import { X } from "lucide-react";
import { <PERSON><PERSON> } from "./button";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground selection:bg-brand selection:text-primary-foreground border-border flex w-full min-w-0 rounded-lg border bg-transparent p-4 text-sm transition-all outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 ",
        "focus-visible:border-brand focus-visible:ring-brand/50 focus-visible:ring-[1px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        "placeholder:text-placeholder placeholder:text-sm",
        className,
      )}
      {...props}
    />
  );
}

const InputSearch = ({
  className,
  type,
  onSearch,
  onChange,
  ...props
}: React.ComponentProps<"input"> & { onSearch: (value: string) => void }) => {
  const [value, setValue] = React.useState(props.value || "");

  // Sync with external value changes
  React.useEffect(() => {
    setValue(props.value || "");
  }, [props.value]);

  const _handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    onChange?.(e);
  };

  const _handleClear = () => {
    setValue("");
    // Create synthetic event for onChange
    const syntheticEvent = {
      target: { value: "" },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange?.(syntheticEvent);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onSearch?.(value as string);
    }
  };

  return (
    <div className="relative group">
      {/* Left Search Icon Button */}
      <Button
        variant="text"
        size="icon"
        className="absolute left-2 top-1/2 transform -translate-y-1/2 p-0"
        onClick={() => onSearch?.(value as string)}
      >
        <IcSearchSm size={24} className="text-icon-placeholder" />
      </Button>

      {/* Input Field */}
      <input
        type={type}
        data-slot="input"
        value={value}
        onChange={_handleChange}
        onKeyDown={handleKeyDown}
        className={cn(
          "file:text-foreground selection:bg-brand selection:text-primary-foreground border-border flex w-full min-w-0 rounded-lg border bg-transparent px-4 py-4 text-sm transition-all outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 ",
          "focus-visible:border-brand focus-visible:ring-brand/50 focus-visible:ring-[1px]",
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
          "px-12",
          "placeholder:text-placeholder placeholder:text-sm",
          className,
        )}
        {...props}
      />

      {/* Right Clear Button - Only show when there's text */}
      {value && (
        <Button
          variant="text"
          className="absolute right-2 top-1/2 transform -translate-y-1/2"
          onClick={_handleClear}
        >
          <X className="text-icon-placeholder w-5 h-5" />
        </Button>
      )}
    </div>
  );
};

export { Input, InputSearch };

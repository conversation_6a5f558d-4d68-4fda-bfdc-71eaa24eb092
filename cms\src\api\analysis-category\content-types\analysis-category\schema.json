{"kind": "collectionType", "collectionName": "analysis_categories", "info": {"singularName": "analysis-category", "pluralName": "analysis-categories", "displayName": "Analysis - Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "documents": {"type": "relation", "relation": "oneToMany", "target": "api::analysis-document.analysis-document", "mappedBy": "category"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::analysis-category.analysis-category", "mappedBy": "parent"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::analysis-category.analysis-category", "inversedBy": "children"}, "order_number": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}}}
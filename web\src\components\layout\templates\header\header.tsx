"use client";

import { LocalizedLink } from "#/src/components/common/localized-link";
import {
  HomeActiveIcon,
  HomeIcon,
  MenuActiveIcon,
  MenuIcon,
} from "#/src/components/svg";
import {
  <PERSON><PERSON>,
  Separator,
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON>ontent,
  DrawerTrigger,
} from "#/src/components/ui";
import {
  createLocalizedPath,
  isPathActive,
  useLocale,
} from "#/src/lib/locale-navigation";

import { X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { OnBoardingDrawer } from "../onboarding/onboarding-drawer";
import { DesktopLogo, MobileLogo } from "#/src/components/logo";
import { cn } from "#/src/lib";

type MenuType = "trade" | "gbi" | "partners" | "menu" | null;

// Main navigation items configuration
interface MainNavItem {
  key: MenuType;
  href: string;
  label: string;
  hasMenu?: boolean;
}

const _mainNavItems: MainNavItem[] = [
  { key: "trade", href: "/trade", label: "Trade", hasMenu: true },
  { key: "gbi", href: "/gbi", label: "GBI", hasMenu: true },
  { key: "partners", href: "/partners", label: "Partners", hasMenu: true },
];

// Mobile navigation items (can include additional items not in main nav)
const _mobileNavItems = [
  ..._mainNavItems,
  { key: null, href: "/community", label: "Cộng đồng Kafi", hasMenu: false },
  { key: null, href: "/analysis", label: "Phân tích", hasMenu: false },
  { key: null, href: "/about", label: "Về chúng tôi", hasMenu: false },
  { key: null, href: "/news", label: "Tin tức & sự kiện", hasMenu: false },
  { key: null, href: "/knowledge", label: "Kiến thức", hasMenu: false },
  { key: null, href: "/support", label: "Liên hệ & hỗ trợ", hasMenu: false },
] as const;

// Navigation configuration with mapping approach
interface NavigationItem {
  title: string;
  href: string;
  desc: string;
}

interface NavigationConfig {
  [key: string]: {
    title: string;
    items: NavigationItem[];
    image?: string;
  };
}

const _navigationConfig: NavigationConfig = {
  trade: {
    title: "Trade",
    image: "/patterns/image-pattern-menu-tradepng.png",
    items: [
      {
        title: "Bảng điện",
        href: "/trade/bang-dien",
        desc: "Cập nhật thị trường và theo dõi biến động với công cụ chuyên biệt.",
      },
      {
        title: "Biểu phí",
        href: "/trade/fee",
        desc: "Thông tin chi tiết các khoản phí giao dịch áp dụng.",
      },
      {
        title: "FAQ",
        href: "/trade/faq",
        desc: "Tổng hợp câu hỏi thường gặp và giải đáp liên quan đến các sản phẩm của Kafi.",
      },
    ],
  },
  gbi: {
    title: "GBI",
    image: "/patterns/image-pattern-menu-gbi.png",
    items: [
      {
        title: "Lợi ích của đầu tư mục tiêu",
        href: "/gbi/loi-ich",
        desc: "Giao dịch nhóm cổ phiếu vốn hóa lớn, ít biến động, rủi ro thấp – tối ưu hiệu quả đầu tư.",
      },
      {
        title: "Công cụ tính toán",
        href: "/gbi/cong-cu",
        desc: "Đầu tư linh hoạt, phù hợp với nhiều giai đoạn cuộc sống khác nhau.",
      },
      {
        title: "FAQ",
        href: "/gbi/faq",
        desc: "Tổng hợp câu hỏi thường gặp và giải đáp liên quan đến GBI.",
      },
    ],
  },
  partners: {
    title: "Partners",
    image: "/patterns/image-pattern-menu-partners.png",
    items: [
      {
        title: "Lợi ích Kafi Partners",
        href: "/partners/loi-ich",
        desc: "Chương trình giới thiệu bạn bè dành cho khách hàng tin tưởng và đồng hành cùng Kafi.",
      },
      {
        title: "Cách tính K-Points",
        href: "/partners/kpoints",
        desc: "Hướng dẫn cách tích lũy, tính giá trị và sử dụng K-Points.",
      },
      {
        title: "FAQ",
        href: "/partners/faq",
        desc: "Tổng hợp câu hỏi thường gặp và giải đáp liên quan đến chương trình Partners.",
      },
    ],
  },
};

// Menu icon panel configuration
const _menuIconConfig = {
  analysis: {
    title: "Phân tích",
    items: [
      {
        title: "Trung tâm Phân tích",
        href: "/research",
        desc: "Tổng hợp các báo cáo phân tích về thị trường, doanh nghiệp và vĩ mô.",
      },
      {
        title: "Báo cáo thị trường",
        href: "/research/bao-cao-thi-truong",
        desc: "Tổng quan diễn biến thị trường và nhóm ngành tiềm năng.",
      },
      {
        title: "Báo cáo doanh nghiệp",
        href: "/research/tin-tuc-doanh-nghiep",
        desc: "Phân tích hoạt động, tài chính và tiềm năng tăng trưởng doanh nghiệp.",
      },
      {
        title: "Báo cáo vĩ mô",
        href: "/research/bao-cao-vi-mo-chien-luoc",
        desc: "Góc nhìn về tình hình kinh tế - vĩ mô, các chỉ số kinh tế và chính sách đến thị trường.",
      },
    ],
  },
  about: {
    title: "Về chúng tôi",
    items: [
      {
        title: "Thông tin chung",
        href: "/about",
        desc: "Tổng quan về Kafi, sứ mệnh, hoạt động và đội ngũ.",
      },
      {
        title: "Tuyển dụng",
        href: "/about/careers",
        desc: "Thông tin về vị trí tuyển dụng và cơ hội việc làm tại Kafi.",
      },
      {
        title: "Quan hệ cổ đông",
        href: "/shareholders",
        desc: "Báo cáo tài chính, thông tin minh bạch dành cho cổ đông.",
      },
      {
        title: "Tin tức & sự kiện",
        href: "/about/news",
        desc: "Cập nhật hoạt động, sự kiện và tin tức mới từ Kafi.",
      },
      {
        title: "Ưu đãi",
        href: "/promotions",
        desc: "Các thông tin về ưu đãi của Kafi theo từng thời kỳ.",
      },
      {
        title: "Giải thưởng",
        href: "/awards",
        desc: "Những thành tựu và giải thưởng của Kafi đã đạt được trong thời gian hoạt động.",
      },
      {
        title: "Kiến thức đầu tư",
        href: "/about/investment-knowledge",
        desc: "Hỗ trợ cung cấp kiến thức và công cụ hỗ trợ đầu tư hiệu quả.",
      },
      {
        title: "Điều khoản sử dụng",
        href: "/terms-and-condition",
        desc: "Thông tin về các điều khoản điều kiện và tuyên bố miễn trừ khi sử dụng dịch vụ Kafi.",
      },
      {
        title: "Địa chỉ",
        href: "/contact-location",
        desc: "Thông tin liên hệ và hệ thống các chi nhánh của Kafi.",
      },
    ],
  },
};

function MegaMenuContent({ menuType }: { menuType: MenuType }) {
  const locale = useLocale();

  if (!menuType || menuType === "menu" || !_navigationConfig[menuType]) {
    return null;
  }

  const config = _navigationConfig[menuType];
  const learnMoreHref = createLocalizedPath(`/${menuType}`, locale);

  return (
    <div className="flex flex-col gap-4 p-8 w-full max-h-[80vh] overflow-y-auto custom-scrollbar">
      {/* Panel Title */}
      <h2 className="text-2xl font-semibold px-4">{config.title}</h2>

      {/* Menu items */}
      <div className="flex flex-col">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {config.items.map((item) => (
            <LocalizedLink
              key={item.href}
              href={item.href}
              className="block group rounded-lg p-4 hover:bg-[#fafafa] transition-colors duration-200 max-w-[232px]"
            >
              <div className="font-medium text-[#000] group-hover:text-[#00C694] mb-2 text-base transition-colors duration-200">
                {item.title}
              </div>
              <div className="text-[#1E2328] font-normal text-sm leading-relaxed">
                {item.desc}
              </div>
            </LocalizedLink>
          ))}
        </div>
        <div className="flex items-end justify-between w-full">
          <Link href={learnMoreHref}>
            <Button
              variant="text"
              className="rounded-lg cursor-pointer font-medium hover:text-[#00B085] py-3 px-4 transition-colors duration-200"
            >
              Tìm hiểu thêm
            </Button>
          </Link>
          {config.image && (
            <div className="h-[100px] min-w-[100px] relative">
              <Image
                src={config.image}
                alt={`${menuType} illustration`}
                fill
                className="object-contain"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Component for rendering navigation button
function _NavButton({
  item,
  pathname,
  activeMenu,
  onMenuHover,
  onMenuLeave,
}: {
  item: MainNavItem;
  pathname: string;
  activeMenu: MenuType;
  onMenuHover: (menuType: MenuType) => void;
  onMenuLeave: () => void;
}) {
  const isActive = isPathActive(pathname, item.href) || activeMenu === item.key;

  const buttonContent = (
    <Button
      className={cn(
        "rounded-full flex items-center justify-center h-12 px-4 text-base font-semibold  transition-colors duration-200",
        isActive && "!text-brand !bg-brand-subtle",
      )}
      variant="ghost"
    >
      {item.label}
    </Button>
  );

  if (item.hasMenu) {
    return (
      <div
        onMouseEnter={() => onMenuHover(item.key)}
        onMouseLeave={onMenuLeave}
      >
        <LocalizedLink href={item.href}>{buttonContent}</LocalizedLink>
      </div>
    );
  }

  return <LocalizedLink href={item.href}>{buttonContent}</LocalizedLink>;
}

function MenuIconPanel() {
  return (
    <div className="flex flex-col gap-8 p-8 w-full max-h-[80vh] overflow-y-auto custom-scrollbar">
      {/* Render sections dynamically */}
      {Object.entries(_menuIconConfig).map(([key, section]) => (
        <div key={key} className="flex flex-col gap-4">
          <h2 className="text-xl font-semibold px-4 text-[#1E2328]">
            {section.title}
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 px-4">
            {section.items.map((item) => (
              <LocalizedLink
                key={item.href}
                href={item.href}
                className="block group rounded-lg p-4 hover:bg-[#fafafa] transition-colors duration-200 max-w-[232px]"
              >
                <div className="font-medium text-[#000] group-hover:text-[#00C694] mb-2 text-base transition-colors duration-200">
                  {item.title}
                </div>
                <div className="text-[#1E2328] font-normal text-sm leading-relaxed">
                  {item.desc}
                </div>
              </LocalizedLink>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

// Mobile Menu Drawer Component - Triggered by MenuIcon on mobile
function MobileMenuDrawer() {
  return (
    <Drawer direction="right">
      <DrawerTrigger asChild>
        <button
          className="rounded-full flex items-center justify-center h-12 w-12 !p-0 hover:bg-[#f4fdfa] transition"
          aria-label="Menu"
        >
          <MenuIcon />
        </button>
      </DrawerTrigger>
      <DrawerContent
        className="!w-full left-0 right-0 top-0 bottom-0 sm:left-auto sm:right-2 sm:top-2 sm:bottom-2 rounded-none sm:rounded-lg shadow-lg"
        style={
          {
            "--initial-transform": "calc(100% + 8px)",
            background: "#F6F7F8",
            backdropFilter: "blur(24px)",
          } as React.CSSProperties
        }
      >
        <div className="h-full w-full flex flex-col rounded-lg overflow-hidden">
          {/* Header with close button */}
          <div className="flex items-center justify-between px-4 py-3 h-16">
            <div className="rounded-lg flex items-center justify-center">
              <HomeIcon />
            </div>
            <DrawerClose className="hover:bg-white/20 rounded-full transition-colors">
              <X className="w-6 h-6" />
            </DrawerClose>
          </div>

          <div className="flex justify-center gap-3 my-4 px-16">
            <OnBoardingDrawer>
              <Button>Đăng ký</Button>
            </OnBoardingDrawer>
            <Button variant="outline">Đăng nhập</Button>
          </div>

          {/* Menu Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar px-6">
            <div className="space-y-1 text-center">
              {/* Main Navigation */}
              {_mobileNavItems.map((item, index) => (
                <LocalizedLink
                  key={`${item.href}-${index}`}
                  href={item.href}
                  className="block py-4 text-lg font-medium text-gray-900 hover:text-[#00C694] transition-colors text-center"
                >
                  {item.label}
                </LocalizedLink>
              ))}
              <Separator className="!w-12 mx-auto border-[#dedede]" />
              <Link
                href="/en"
                className="block py-4 text-lg font-medium text-gray-900 hover:text-[#00C694] transition-colors text-center"
              >
                English
              </Link>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}

function CustomMegaMenu({
  activeMenu,
  // setActiveMenu,
  menuRef,
  onMenuEnter,
  onMenuLeave,
}: {
  activeMenu: MenuType;
  setActiveMenu: (menu: MenuType) => void;
  menuRef: React.RefObject<HTMLDivElement | null>;
  onMenuEnter: () => void;
  onMenuLeave: () => void;
}) {
  if (!activeMenu || activeMenu === "menu") return null;

  return (
    <div
      ref={menuRef}
      className="fixed top-28 left-1/2 -translate-x-1/2 w-[calc(100vw-40px)] max-w-[790px] lg:w-[790px] bg-white shadow-xl z-[40] hidden lg:block"
      style={{
        borderRadius: "12px",
        boxShadow: "0 10px 40px rgba(0, 0, 0, 0.1)",
      }}
      onMouseEnter={onMenuEnter}
      onMouseLeave={onMenuLeave}
    >
      <div className="mx-auto">
        <MegaMenuContent menuType={activeMenu} />
      </div>
    </div>
  );
}

export function Header() {
  const pathname = usePathname();
  const [activeMenu, setActiveMenu] = useState<MenuType>(null);
  const [isHomeHovered, setIsHomeHovered] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const menuIconRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close menu when clicking outside
  useEffect(() => {
    if (typeof document === "undefined") return;

    function handleClickOutside(event: MouseEvent) {
      if (
        menuRef.current &&
        menuIconRef.current &&
        headerRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !menuIconRef.current.contains(event.target as Node) &&
        !headerRef.current.contains(event.target as Node)
      ) {
        setActiveMenu(null);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleMenuHover = (menuType: MenuType) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setActiveMenu(menuType);
  };

  const handleMenuLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setActiveMenu(null);
    }, 200);
  };

  const handleMenuEnter = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  };

  return (
    <>
      <div className="relative">
        <nav
          ref={headerRef}
          className="bg-nav-bg fixed inset-0 z-20 h-16 lg:h-24"
          style={{
            background:
              "linear-gradient(180deg, rgba(236, 245, 244, 0.4) 0%, rgba(236, 245, 244, 0) 100%)",
            backdropFilter: "blur(13px)",
          }}
        >
          <div className="h-16 lg:h-24 w-full mx-auto">
            {/* Mobile Layout */}
            <div className="lg:hidden flex items-center justify-between h-full mx-6 sm:mx-10">
              {/* Mobile Logo */}
              <div className="flex justify-start">
                <LocalizedLink href="/">
                  <MobileLogo />
                </LocalizedLink>
              </div>
              {/* Mobile Menu Drawer */}
              <MobileMenuDrawer />
            </div>
            {/* Desktop Layout */}
            <div className="hidden lg:grid grid-cols-3 items-center h-full lg:mx-20">
              {/* Desktop Logo */}
              <div className="flex justify-start">
                <LocalizedLink href="/">
                  <DesktopLogo />
                </LocalizedLink>
              </div>
              {/* Nav */}
              <div className="flex justify-center">
                <div className="nav flex items-center justify-between lg:gap-4 xl:gap-[38px]">
                  {/* Home */}
                  <LocalizedLink href="/">
                    <Button
                      variant="ghost"
                      className={`rounded-full cursor-pointer flex items-center justify-center h-12 w-12 ${isPathActive(pathname, "/") || isHomeHovered ? "!bg-brand-subtle" : ""} hover:bg-[#f4fdfa] transition`}
                      onMouseEnter={() => setIsHomeHovered(true)}
                      onMouseLeave={() => setIsHomeHovered(false)}
                    >
                      {isPathActive(pathname, "/") || isHomeHovered ? (
                        <HomeActiveIcon className="size-6" />
                      ) : (
                        <HomeIcon className="size-6" />
                      )}
                    </Button>
                  </LocalizedLink>
                  {/* Navigation Items */}
                  {_mainNavItems.map((item) => (
                    <_NavButton
                      key={item.key}
                      item={item}
                      pathname={pathname}
                      activeMenu={activeMenu}
                      onMenuHover={handleMenuHover}
                      onMenuLeave={handleMenuLeave}
                    />
                  ))}
                  {/* Menu - container for button and panel */}
                  <div
                    className="relative"
                    onMouseEnter={() => handleMenuHover("menu")}
                    onMouseLeave={handleMenuLeave}
                  >
                    <Button
                      variant="text"
                      size="icon"
                      className={`rounded-full cursor-pointer flex items-center justify-center h-10 w-10 lg:h-12 lg:w-12 !p-0 transition`}
                    >
                      {activeMenu === "menu" ? (
                        <MenuActiveIcon className="size-6" />
                      ) : (
                        <MenuIcon className="size-6" />
                      )}
                    </Button>
                    {/* Menu Panel - Now rendered outside */}
                  </div>
                </div>
              </div>
              {/* Right side */}
              <div className="flex justify-end items-center">
                {/* Desktop buttons */}
                <Button variant="text">Đăng nhập</Button>
                <OnBoardingDrawer>
                  <Button>Đăng ký</Button>
                </OnBoardingDrawer>
              </div>
            </div>
          </div>
        </nav>
      </div>

      {/* Custom Mega Menu - Rendered outside header for proper z-index */}
      <CustomMegaMenu
        activeMenu={activeMenu}
        setActiveMenu={setActiveMenu}
        menuRef={menuRef}
        onMenuEnter={handleMenuEnter}
        onMenuLeave={handleMenuLeave}
      />

      {/* MenuIcon Panel - Rendered outside header for proper z-index */}
      {activeMenu === "menu" && (
        <div
          ref={menuIconRef}
          className="fixed z-20 hidden lg:block w-[calc(100vw-40px)] max-w-[1038px] xl:w-[1038px]"
          style={{
            top: "96px",
            right: "20px",
          }}
          onMouseEnter={handleMenuEnter}
          onMouseLeave={handleMenuLeave}
        >
          <div
            className="bg-white shadow-xl transform transition-all duration-300 ease-out animate-in fade-in-0 slide-in-from-top-2"
            style={{
              borderRadius: "12px",
              boxShadow: "0 10px 40px rgba(0, 0, 0, 0.1)",
            }}
          >
            <MenuIconPanel />
          </div>
        </div>
      )}
    </>
  );
}

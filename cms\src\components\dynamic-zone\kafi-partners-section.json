{"collectionName": "components_dynamic_zone_kafi_partners_sections", "info": {"displayName": "<PERSON><PERSON>_Partners_Section"}, "options": {}, "attributes": {"section_styles": {"type": "component", "component": "elementals.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.section-heading", "repeatable": false}, "kafi_partners": {"type": "component", "component": "elementals.kafi-partners", "repeatable": false}}, "config": {}}
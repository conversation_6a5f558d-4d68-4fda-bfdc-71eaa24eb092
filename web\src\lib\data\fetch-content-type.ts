import { draftMode } from "next/headers";
import qs from "qs";
import { APP_CONFIG } from "@/config-global";
/**
 * Fetches data for a specified Strapi content type.
 *
 * @param {string} contentType - The type of content to fetch from Strapi.
 * @param {string} params - Query parameters to append to the API request.
 * @return {Promise<object>} The fetched data.
 */

interface StrapiData {
  id: number;
  [key: string]: any; // Allow for any additional fields
}

interface StrapiResponse {
  data: StrapiData | StrapiData[];
}

export function spreadStrapiData(data: StrapiResponse): StrapiData | null {
  if (Array.isArray(data.data) && data.data.length > 0) {
    return data.data[0];
  }
  if (!Array.isArray(data.data)) {
    return data.data;
  }
  return null;
}

export function getCacheKey(
  contentType: string,
  params: Record<string, unknown> = {},
) {
  return `${contentType}-${JSON.stringify(params)}`;
}

export function getCacheOptions(
  contentType: string,
  params: Record<string, unknown> = {},
) {
  const cacheKey = getCacheKey(contentType, params);
  return {
    cache: "no-store",
    next: {
      tags: [cacheKey],
      revalidate: 60,
    },
  };
}

export type TFetchContentTypeOptions = {
  contentType: string;
  params: Record<string, unknown>;
  spreadData?: boolean;
  options?: any;
};

export default async function fetchContentType({
  contentType,
  params,
  spreadData,
  options,
}: TFetchContentTypeOptions): Promise<any> {
  const { isEnabled } = await draftMode();

  try {
    const queryParams = { ...params };

    if (isEnabled) {
      queryParams.status = "draft";
    }

    // Construct the full URL for the API request
    const pathPath = `api/${contentType}`.replace("//", "/");
    const url = new URL(pathPath, APP_CONFIG.apiUrl);
    const apiUrl = `${url.href}?${qs.stringify(queryParams)}`;

    console.log("apiUrl", { apiUrl, queryParams });

    // Perform the fetch request with the provided query parameters
    const response = await fetch(apiUrl, {
      method: "GET",
      cache: "no-store",
      ...options,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
        Accept: "application/json",
        "Cache-Control": "no-cache",
        ...options?.headers,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch data from CMS (url=${url.toString()}, status=${response.status})`,
      );
    }
    const jsonData: StrapiResponse = await response.json();
    return spreadData ? spreadStrapiData(jsonData) : jsonData;
  } catch (error) {
    // Log any errors that occur during the fetch process
    console.error("FetchContentTypeError", error);
  }
}

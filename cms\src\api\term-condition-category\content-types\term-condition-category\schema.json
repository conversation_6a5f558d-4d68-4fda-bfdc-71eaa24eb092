{"kind": "collectionType", "collectionName": "term_condition_categories", "info": {"singularName": "term-condition-category", "pluralName": "term-condition-categories", "displayName": "Term Condition-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "documents": {"type": "relation", "relation": "oneToMany", "target": "api::term-condition-document.term-condition-document", "mappedBy": "category"}}}
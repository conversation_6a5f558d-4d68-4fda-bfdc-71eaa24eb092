{"kind": "collectionType", "collectionName": "shareholder_categories", "info": {"singularName": "shareholder-category", "pluralName": "shareholder-categories", "displayName": "Shareholder-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::shareholder-category.shareholder-category", "mappedBy": "parent"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::shareholder-category.shareholder-category", "inversedBy": "children"}, "documents": {"type": "relation", "relation": "oneToMany", "target": "api::shareholder-document.shareholder-document", "mappedBy": "category"}}}
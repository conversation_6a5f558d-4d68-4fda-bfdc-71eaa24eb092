import {
  GBI_BENEFITS_ICON_BOXES,
  GBI_FAQ,
  GBI_HERO_SECTION,
} from "#/src/__mock__";
import IconBoxesSection from "#/src/components/dynamic-zone/icon-boxes-section";
import PageHeroSection from "#/src/components/dynamic-zone/page-hero-section";
import GBIInvestmentCategorySection from "../gbi/gbi-investment-category-section";
import { SupportInfo, Faqs } from "../shared";
import { GBIToolView } from "../gbi";

export default function GBIView() {
  return (
    <div className="flex flex-col gap-20">
      <PageHeroSection data={GBI_HERO_SECTION} />

      <IconBoxesSection data={GBI_BENEFITS_ICON_BOXES} />

      <GBIInvestmentCategorySection />

      <GBIToolView />

      <Faqs dataFAQ={GBI_FAQ} />

      <SupportInfo />
    </div>
  );
}

// FIXME:
/**
 * <PageHeroSection data={_mock__} />
 *
 * ..any other sections
 *
 * <SupportInfo data={_mock__} />
 * **/

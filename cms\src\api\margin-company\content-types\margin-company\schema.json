{"kind": "collectionType", "collectionName": "margin_companies", "info": {"singularName": "margin-company", "pluralName": "margin-companies", "displayName": "Margin - Companies"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"is_active": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}, "required": true, "default": true}, "company_name": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "company_code": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "loan_ratio": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "exchange": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "enum": ["hose"]}}}
{"collectionName": "components_dynamic_zone_outstanding_awards", "info": {"displayName": "Outstanding_Awards"}, "options": {}, "attributes": {"section_styles": {"type": "component", "component": "elementals.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.section-heading", "repeatable": false}, "awards": {"type": "relation", "relation": "oneToMany", "target": "api::award.award"}, "image": {"type": "media", "multiple": false, "allowedTypes": ["images"]}}, "config": {}}
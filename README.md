# Kafi.vn - Next.js (V15) + Strapi (V5)

A fullstack web application using **Strapi** as a headless CMS and **Next.js** (SourceNext) as the frontend framework. This setup allows for dynamic, API-driven content management with a modern, performant frontend.

## 📁 Project Structure

```
root/
├── cms/          # Strapi CMS
│   ├── config/
│   ├── src/
│   ├── .env
│   └── ...
└── web/         # Next.js frontend
    ├── src/
    ├── public/
    ├── .env
    └── ...
```

## 🚀 Getting Started

```bash
bun install && bun setup:web && bun setup:cms
```

### 🧰 Prerequisites

Make sure you have the following installed:

- [Node.js](https://nodejs.org/) (v18 or newer)
- [npm](https://www.npmjs.com/) or [Yarn](https://yarnpkg.com/)
- [Strapi CLI](https://docs.strapi.io/)
- Docker (optional, for DB like PostgreSQL)

## 📦 Backend Setup (Strapi CMS)

1. Navigate to backend directory:

```bash
cd cms
```

2. Install dependencies:

```bash
npm install
```

3. Run Strapi in development mode:

```bash
npm run develop
```

> The admin panel will be available at [http://localhost:1337/admin](http://localhost:1337/admin)

4. Configure Strapi:

- Register an admin account
- Create content types (e.g., `Post`, `Category`)
- Set permissions for public and authenticated roles
- Add sample content via the CMS

## 🌐 Frontend Setup (Next.js)

1. Navigate to frontend directory:

```bash
cd web
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables in `.env.local`:

```env
NEXT_PUBLIC_API_URL=http://localhost:1337
```

4. Run the frontend development server:

```bash
npm run dev
```

> App available at [http://localhost:3000](http://localhost:3000)

## 🔧 API Integration

The frontend fetches content from Strapi using REST or GraphQL.

**Example REST fetch:**

```js
const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/posts`);
const data = await res.json();
```

**GraphQL (optional):**

- Install the GraphQL plugin in Strapi via Marketplace or CLI.
- Use Apollo Client or similar to fetch data in frontend.

## 🛠 Tech Stack

| Layer       | Tech                     |
|-------------|--------------------------|
| Frontend    | Next.js, React           |
| Styling     | Tailwind CSS (optional)  |
| Backend     | Strapi (Node.js)         |
| Database    | SQLite / PostgreSQL / MongoDB |
| API         | REST / GraphQL           |
| Deployment  | Vercel, Netlify, Render  |

## 🗂 Example Content Models

**Post**

- `title`: string
- `slug`: UID
- `content`: rich text
- `coverImage`: media
- `publishedAt`: datetime

**Category**

- `name`: string
- `posts`: relation (many-to-many)

## 🧪 Testing

- Strapi: Test endpoints using Postman or Swagger (via plugin)
- Frontend: Use Jest, React Testing Library (optional)

## 🐳 Docker (Optional)

Use Docker to run PostgreSQL or containerize the entire app.

**Example PostgreSQL setup with Docker Compose:**

```yaml
services:
  postgres:
    image: postgres
    environment:
      POSTGRES_USER: strapi
      POSTGRES_PASSWORD: strapi
      POSTGRES_DB: strapi
    ports:
      - 5432:5432
```

Update your `backend/.env`:

```env
DATABASE_CLIENT=postgres
DATABASE_NAME=strapi
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=strapi
DATABASE_PASSWORD=strapi
```

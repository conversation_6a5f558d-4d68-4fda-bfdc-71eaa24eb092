import { GBIParams, GBIResult } from "@/services/calculate-gbi";

export enum EGbiField {
  TargetAmount_g1 = "targetAmount_g1",
  AnnualReturn_r = "annualReturn_r",
  TimePeriod_t = "timePeriod_t",
  InitialInvestment_x = "initialInvestment_x",
  MonthlyDeposit_a = "monthlyDeposit_a",
  GBI_g = "gbi",
  GBIPercentage_g_z = "gbiPercentage",
}

export const SLIDER_CONFIG = {
  targetAmount: {
    min: 0,
    max: 3_000_000_000,
    step: 1_000_000,
  },
  annualReturn: {
    min: 6,
    max: 20,
    step: 1,
  },
  initialInvestment: {
    min: 100_000_000,
    max: 2_999_999_999,
    step: 1_000_000,
  },
  timePeriod: {
    min: 3,
    max: 36,
    step: 1,
  },
};

export interface GBIState extends GBIParams, GBIResult {}

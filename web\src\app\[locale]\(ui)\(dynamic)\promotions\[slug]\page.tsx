import PromotionDetailView from "#/src/modules/promotions/promotion-detail-view";
import LoadingPage from "@/components/loading/loading-page";
import { Suspense } from "react";
import { Metadata } from "next";
//-----------------------------------------------------------
export const metadata: Metadata = {
  title: "Ưu đãi chi tiết - Ka<PERSON>",
  description: "Ưu đãi chi tiết - Ka<PERSON>",
};
//-----------------------------------------------------------

export default async function PromotionDetailPage() {
  return (
    <Suspense fallback={<LoadingPage />}>
      <PromotionDetailView />
    </Suspense>
  );
}

export const dynamic = "force-dynamic";

"use client";
import { Card, Typography } from "#/src/components/ui";
import {
  IcLineTelescope,
  IcLineBuilding,
  IcLineBriefcase,
  IcLineShieldPlus,
} from "#/src/components/svg";
const careerData = [
  {
    icon: IcLineTelescope,
    iconProps: {
      size: 60,
      color: "text-brand",
      hoverColor: "text-brand",
      activeColor: "text-brand",
      strokeWidth: 1,
    },
    title: "Tầm nhìn dài hạn",
    description:
      "<PERSON><PERSON> tự hào với sự tăng trưởng vượt bậc qua các năm và vị thế vững chắc trên thị trường. Chúng tôi không ngừng đổi mới để dẫn đầu xu thế, mang đến cơ hội phát triển sự nghiệp bền vững cùng Công ty",
  },
  {
    icon: IcLineBuilding,
    iconProps: {
      size: 60,
      color: "text-brand",
      hoverColor: "text-brand",
      activeColor: "text-brand",
      strokeWidth: 1,
    },
    title: "<PERSON>ôi trường chuyên nghiệp",
    description:
      "<PERSON><PERSON><PERSON>, bạn sẽ làm việc trong một môi trường tài chính chuyên nghiệp. Chúng tôi khuyến khích sự chủ động, sáng tạo và tạo mọi điều kiện để bạn học hỏi, phát triển chuyên môn, bứt phá giới hạn bản thân",
  },
  {
    icon: IcLineBriefcase,
    iconProps: {
      size: 60,
      color: "text-brand",
      hoverColor: "text-brand",
      activeColor: "text-brand",
      strokeWidth: 2,
    },
    title: "Cơ hội phát triển sự nghiệp",
    description:
      "Thông qua các chương trình đào tạo chuyên sâu, lộ trình phát triển nghề nghiệp rõ ràng và cơ hội thăng tiến dựa trên năng lực. Chúng tôi tin rằng sự phát triển của bạn chính là sự phát triển của Kafi",
  },
  {
    icon: IcLineShieldPlus,
    iconProps: {
      size: 60,
      color: "text-brand",
      hoverColor: "text-brand",
      activeColor: "text-brand",
      strokeWidth: 1,
    },
    title: "Chế độ phúc lợi cạnh tranh",
    description:
      "Kafi mang đến chính sách lương, thưởng hấp dẫn, cùng các hệ thống phúc lợi toàn diện (bảo hiểm, chăm sóc sức khỏe, du lịch, nghỉ phép, v.v.) nhằm tạo nền tảng ổn định và lâu dài cho mỗi thành viên trong tổ chức",
  },
];
const RecruitmentWhyJoinUs = () => {
  return (
    <div className="flex flex-col gap-10 md:gap-6 xl:gap-10">
      <div className="flex flex-col gap-4 text-start md:text-center">
        <Typography classname="text-default text-[30px] md:text-3xl xl:text-6xl font-bold">
          Tuyển dụng
        </Typography>
        <Typography variant="body-regular" classname="text-default">
          Khám phá cơ hội nghề nghiệp tại Kafi. Môi trường lý tưởng cho sự
          nghiệp phát triển bền vững.
        </Typography>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {careerData.map((item, index) => (
          <Card
            key={index}
            className="bg-white text-emphasize rounded-xl shadow-none border-none p-6 xl:p-10 flex flex-col gap-6"
          >
            {/* Render the icon component with its props */}
            <item.icon {...item.iconProps} />
            <Typography className="text-gray-900 font-semibold text-xl xl:text-2xl">
              {item.title}
            </Typography>
            <Typography variant="body-regular">{item.description}</Typography>
          </Card>
        ))}
      </div>
    </div>
  );
};
export default RecruitmentWhyJoinUs;

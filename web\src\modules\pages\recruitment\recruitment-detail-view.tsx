"use client";
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Typography,
} from "#/src/components/ui";
import { <PERSON><PERSON>ef<PERSON>, Upload } from "lucide-react";
import { useRouter } from "next/navigation";
import RelatedJobList from "./components/related-job-list";

// Data structure for job details
const jobDetails = {
  title: "Data Engineer",
  tags: [
    { label: "Location", value: "Ho Chi Minh" },
    { label: "Block", value: "Business Technology Solution" },
  ],
  description: [
    "Engage in the full lifecycle of data platform development, including building and maintaining data warehouses and data lakes, as well as designing, implementing, and optimizing scalable ETL processes (batch/streaming) to support analytics, machine learning, and business intelligence initiatives.",
    "Collaborate with development team to integrate data from various sources, including from core system database, APIs and streaming platform.",
    "Contribute to building the data infrastructure for a Quant Trading Platform, including real-time market data ingestion, processing, and management to support scalable and efficient algorithmic trading strategies.",
    "POC and adopting new technologies to improve data platform management in large-scale high throughput.",
    "Ensure data integrity, quality, and governance by implementing best practices in data validation and monitoring.",
    "Ensure compliance with security, privacy, and regulatory standards for data handling.",
    "Reporting and other tasks relevant to data engineer domain knowledge.",
  ],
  requirements: [
    "Bachelor/Master Degree in Applied Mathematics, Quantitative and Computational Finance, Computer Science or equivalent domain.",
    "3+ years of experience in Data Engineering or similar role.",
    "Solid technical skill, proficient in programming languages such as Python (or Go, Java, C++) and are willing to learn new technologies.",
    "Experience with big data technologies (Spark, Hadoop, Beam, Kafka) and distributed computing.",
    "Knowledge of ETL tools (Airflow, dbt, Talend, etc.), DevOps and CI/CD for data pipelines.",
    "Hands-on experience with cloud platforms (GCP, Azure, AWS) is a plus.",
    "Previous experience in a business or industry-specific setting (e.g., finance, retail, healthcare) is a plus.",
    "Strong teamwork skills, honesty, dignity, and responsibility.",
    "Precision, passion, open-mindedness, and quick self-learning skills in big data, as well as enthusiasm for solving business problems.",
  ],
  benefits: [
    "Competitive salary and bonus based on abilities and work experience.",
    "Comprehensive health insurance.",
    "Regular health check-ups.",
    "Team building programs.",
    "Welfare support: maternity, weddings, illness.",
    "Work in a professional, friendly, and modern environment.",
    "Opportunities to participate in the company's training programs.",
  ],
};

// City options for the select input
const cityOptions = ["Hồ Chí Minh", "Hà Nội", "Đà Nẵng", "Khác"];

export default function RecruitmentDetailView() {
  const router = useRouter();

  return (
    <div className="container flex flex-col gap-6">
      <Button
        variant="text"
        className="!px-0 w-fit"
        onClick={() => router.back()}
      >
        <ArrowLeft />
        Quay lại
      </Button>

      <main className="flex flex-col gap-10">
        {/* Header */}
        <div className="space-y-6">
          <Typography className="text-2xl md:text-[30px] xl:text-5xl font-bold ">
            {jobDetails.title}
          </Typography>
          <div className="flex flex-wrap gap-3">
            {jobDetails.tags.map((tag, index) => (
              <div
                key={index}
                className="inline-flex bg-white px-4 py-2 rounded-full items-center justify-center min-h-10 text-start"
              >
                <Typography variant="body-medium">{`${tag.label}: ${tag.value}`}</Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Main content */}
        <div className="mx-auto flex flex-col lg:flex-row xl:gap-20 text-default">
          <section className="flex-1 min-w-0">
            <article className="space-y-10 max-w-3xl">
              <div className="space-y-6 md:space-y-8">
                {/* Mô tả công việc */}
                <Typography className="font-semibold text-2xl">
                  Mô tả công việc
                </Typography>
                <ul className="list-disc list-inside space-y-2 text-base">
                  {jobDetails.description.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>

              {/* Yêu cầu */}
              <div className="space-y-6 md:space-y-8">
                <Typography className="font-semibold text-2xl">
                  Yêu cầu
                </Typography>
                <ul className="list-disc list-inside space-y-2 text-base">
                  {jobDetails.requirements.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>

              {/* Phúc lợi */}
              <div className="space-y-6 md:space-y-8">
                <Typography className="font-semibold text-2xl">
                  Chế độ phúc lợi
                </Typography>
                <ul className="list-disc list-inside space-y-2 text-base">
                  {jobDetails.benefits.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
              <Button className="w-fit lg:hidden inline-block">
                Nộp CV của bạn
              </Button>
            </article>
          </section>

          <aside
            className="bg-white rounded-3xl p-10 w-full max-w-[480px] flex-shrink-0 h-fit hidden lg:block"
            aria-label="Gửi hồ sơ của bạn"
          >
            <Typography className="font-semibold text-2xl mb-6">
              Gửi hồ sơ của bạn
            </Typography>

            <form className="space-y-6">
              <Input
                type="text"
                placeholder="Họ tên"
                required
                className="h-14 border-border-button text-emphasize text-sm leading-5 font-medium"
              />
              <Input
                type="tel"
                placeholder="Số điện thoại"
                required
                className="h-14 border-border-button text-emphasize text-sm leading-5 font-medium"
              />
              <Input
                type="email"
                placeholder="Email"
                required
                className="h-14 border-border-button text-emphasize text-sm leading-5 font-medium"
              />
              <Select required>
                <SelectTrigger className="w-full border-border-button text-emphasize text-sm leading-5 font-medium bg-white rounded-xl min-h-14">
                  <SelectValue placeholder="Tỉnh/ thành phố" />
                </SelectTrigger>
                <SelectContent>
                  {cityOptions.map((city, index) => (
                    <SelectItem key={index} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <label
                htmlFor="file-upload"
                className="relative w-full cursor-pointer border border-gray-300 rounded-lg px-4 py-3 flex items-into justify-between"
              >
                <span>Đính kèm hồ sơ/CV</span>
                <Upload className="text-gray-500" size={16} />
                <input
                  id="file-upload"
                  type="file"
                  accept=".doc,.docx,.xls,.xlsx,.pdf"
                  className="absolute inset-0 opacity-0 cursor-pointer"
                />
              </label>
              <Typography variant="body" className="text-xs">
                Lưu ý: Nộp hồ sơ ứng tuyển theo định dạng .DOC, .XLSX, PDF. Dung
                lượng không quá 5MB và không cài đặt mật khẩu.
              </Typography>
              <Button className="w-full !bg-subtle pointer-events-none">
                Nộp hồ sơ
              </Button>
            </form>
          </aside>
        </div>

        <RelatedJobList />
      </main>
    </div>
  );
}

{"kind": "collectionType", "collectionName": "knowledge_categories", "info": {"singularName": "knowledge-category", "pluralName": "knowledge-categories", "displayName": "Knowledge - Categories"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::knowledge-article.knowledge-article", "mappedBy": "category"}}}
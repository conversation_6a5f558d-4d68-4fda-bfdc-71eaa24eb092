{"collectionName": "components_dynamic_zone_latest_market_insights", "info": {"displayName": "Latest_Market_Insights"}, "options": {}, "attributes": {"section_styles": {"type": "component", "component": "elementals.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.section-heading", "repeatable": false}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::analysis-category.analysis-category"}}, "config": {}}